/**
 * 悬浮意图检测服务
 * 集成 ForesightJS 来处理鼠标快速滑动和悬浮意图预测
 */

import { ForesightManager } from 'js.foresight';

// 悬浮检测配置接口
export interface HoverIntentConfig {
  enableMousePrediction?: boolean;
  trajectoryPredictionTime?: number;
  tabOffset?: number;
  enableScrollPrediction?: boolean;
  debounceDelay?: number;
  throttleInterval?: number;
}

// 悬浮事件回调类型
export type HoverCallback = (element: HTMLElement, event?: Event) => void;
export type HoverEndCallback = (element: HTMLElement, event?: Event) => void;

// 默认配置
const DEFAULT_CONFIG: Required<HoverIntentConfig> = {
  enableMousePrediction: true,
  trajectoryPredictionTime: 150, // 增加预测时间以提高稳定性
  tabOffset: 2,
  enableScrollPrediction: true,
  debounceDelay: 100,
  throttleInterval: 50,
};

/**
 * 悬浮意图检测服务类
 */
export class HoverIntentService {
  private static instance: HoverIntentService | null = null;
  private isInitialized = false;
  private config: Required<HoverIntentConfig>;
  private registeredElements = new Map<HTMLElement, {
    callback: HoverCallback;
    endCallback?: HoverEndCallback;
    foresightRegistration?: any;
  }>();
  private fallbackListeners = new Map<HTMLElement, {
    mouseenter: (e: Event) => void;
    mouseleave: (e: Event) => void;
  }>();

  private constructor(config: HoverIntentConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 获取单例实例
   */
  public static getInstance(config?: HoverIntentConfig): HoverIntentService {
    if (!HoverIntentService.instance) {
      HoverIntentService.instance = new HoverIntentService(config);
    }
    return HoverIntentService.instance;
  }

  /**
   * 初始化 ForesightJS
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 检查环境兼容性
      if (typeof window === 'undefined') {
        throw new Error('Window 对象不可用，无法初始化 ForesightJS');
      }

      if (!document.body) {
        throw new Error('文档体未准备就绪，无法初始化 ForesightJS');
      }

      // 初始化 ForesightManager
      await ForesightManager.initialize({
        enableMousePrediction: this.config.enableMousePrediction,
        trajectoryPredictionTime: this.config.trajectoryPredictionTime,
        tabOffset: this.config.tabOffset,
        enableScrollPrediction: this.config.enableScrollPrediction,
      });

      this.isInitialized = true;
      console.log('✅ ForesightJS 初始化成功');
    } catch (error) {
      console.warn('⚠️ ForesightJS 初始化失败，使用降级方案:', error);
      this.isInitialized = false;
      
      // 记录错误详情用于调试
      if (process.env.NODE_ENV === 'development') {
        console.error('ForesightJS 初始化错误详情:', {
          error: error instanceof Error ? error.message : error,
          config: this.config,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  /**
   * 注册元素的悬浮检测
   */
  public register(
    element: HTMLElement | string,
    callback: HoverCallback,
    endCallback?: HoverEndCallback
  ): () => void {
    try {
      const targetElement = typeof element === 'string' 
        ? document.querySelector(element) as HTMLElement
        : element;

      if (!targetElement) {
        console.warn('❌ 目标元素未找到:', element);
        return () => {};
      }

      // 检查元素是否仍在DOM中
      if (!document.contains(targetElement)) {
        console.warn('❌ 目标元素不在DOM中:', element);
        return () => {};
      }

      // 检查回调函数是否有效
      if (typeof callback !== 'function') {
        console.error('❌ 回调函数无效:', callback);
        return () => {};
      }

      // 防抖处理的回调
      const debouncedCallback = this.debounce((el: HTMLElement, event?: Event) => {
        try {
          callback(el, event);
        } catch (error) {
          console.error('❌ 悬浮回调执行失败:', error);
        }
      }, this.config.debounceDelay);

      // 节流处理的结束回调
      const throttledEndCallback = endCallback 
        ? this.throttle((el: HTMLElement, event?: Event) => {
            try {
              endCallback(el, event);
            } catch (error) {
              console.error('❌ 悬浮结束回调执行失败:', error);
            }
          }, this.config.throttleInterval)
        : undefined;

      if (this.isInitialized && ForesightManager.instance) {
        try {
          // 使用 ForesightJS 注册
          const registration = ForesightManager.instance.register({
            element: targetElement,
            callback: () => {
              debouncedCallback(targetElement);
            },
          });

          this.registeredElements.set(targetElement, {
            callback: debouncedCallback,
            endCallback: throttledEndCallback,
            foresightRegistration: registration,
          });

          // 为结束事件添加传统的 mouseleave 监听器
          if (throttledEndCallback) {
            const handleMouseLeave = (event: Event) => {
              throttledEndCallback(targetElement, event);
            };
            targetElement.addEventListener('mouseleave', handleMouseLeave);
            
            // 存储监听器以便后续清理
            this.fallbackListeners.set(targetElement, {
              mouseenter: () => {},
              mouseleave: handleMouseLeave,
            });
          }

          return () => this.unregister(targetElement);
        } catch (error) {
          console.warn('⚠️ ForesightJS 注册失败，使用降级方案:', error);
        }
      }

      // 降级方案：使用传统的 mouseenter/mouseleave 事件
      return this.registerFallback(targetElement, debouncedCallback, throttledEndCallback);
      
    } catch (error) {
      console.error('❌ 悬浮检测注册失败:', error);
      return () => {};
    }
  }

  /**
   * 降级方案：传统事件监听器
   */
  private registerFallback(
    element: HTMLElement,
    callback: HoverCallback,
    endCallback?: HoverEndCallback
  ): () => void {
    try {
      const handleMouseEnter = (event: Event) => {
        try {
          callback(element, event);
        } catch (error) {
          console.error('❌ 降级方案：鼠标进入回调执行失败:', error);
        }
      };

      const handleMouseLeave = (event: Event) => {
        try {
          if (endCallback) {
            endCallback(element, event);
          }
        } catch (error) {
          console.error('❌ 降级方案：鼠标离开回调执行失败:', error);
        }
      };

      // 添加事件监听器时进行错误处理
      try {
        element.addEventListener('mouseenter', handleMouseEnter);
        element.addEventListener('mouseleave', handleMouseLeave);
      } catch (error) {
        console.error('❌ 降级方案：添加事件监听器失败:', error);
        return () => {};
      }

      this.fallbackListeners.set(element, {
        mouseenter: handleMouseEnter,
        mouseleave: handleMouseLeave,
      });

      this.registeredElements.set(element, {
        callback,
        endCallback,
      });

      return () => this.unregister(element);
    } catch (error) {
      console.error('❌ 降级方案注册失败:', error);
      return () => {};
    }
  }

  /**
   * 取消注册元素
   */
  public unregister(element: HTMLElement): void {
    const registration = this.registeredElements.get(element);
    
    if (registration?.foresightRegistration) {
      // 取消 ForesightJS 注册
      try {
        registration.foresightRegistration.unregister?.();
      } catch (error) {
        console.warn('⚠️ ForesightJS 取消注册失败:', error);
      }
    }

    // 清理传统事件监听器
    const listeners = this.fallbackListeners.get(element);
    if (listeners) {
      element.removeEventListener('mouseenter', listeners.mouseenter);
      element.removeEventListener('mouseleave', listeners.mouseleave);
      this.fallbackListeners.delete(element);
    }

    this.registeredElements.delete(element);
  }

  /**
   * 清理所有注册
   */
  public cleanup(): void {
    // 清理所有已注册的元素
    for (const element of this.registeredElements.keys()) {
      this.unregister(element);
    }

    // 重置初始化状态
    this.isInitialized = false;
  }

  /**
   * 防抖函数
   */
  private debounce<T extends (...args: any[]) => void>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  /**
   * 节流函数
   */
  private throttle<T extends (...args: any[]) => void>(
    func: T,
    interval: number
  ): (...args: Parameters<T>) => void {
    let lastCallTime = 0;
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCallTime >= interval) {
        lastCallTime = now;
        func(...args);
      }
    };
  }

  /**
   * 获取服务状态
   */
  public getStatus() {
    return {
      isInitialized: this.isInitialized,
      registeredElementsCount: this.registeredElements.size,
      config: this.config,
    };
  }
}

// 导出单例实例
export const hoverIntentService = HoverIntentService.getInstance();

// 导出便利函数
export const registerHoverIntent = (
  element: HTMLElement | string,
  callback: HoverCallback,
  endCallback?: HoverEndCallback
) => hoverIntentService.register(element, callback, endCallback);

export const initializeHoverIntent = (config?: HoverIntentConfig) => 
  hoverIntentService.initialize();

export default hoverIntentService;