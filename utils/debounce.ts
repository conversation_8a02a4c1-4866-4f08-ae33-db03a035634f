/**
 * 防抖函数 - 延迟执行，只执行最后一次调用
 * @param func 要防抖的函数
 * @param wait 延迟时间（毫秒）
 * @param immediate 是否立即执行第一次调用
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

/**
 * 节流函数 - 限制执行频率
 * @param func 要节流的函数
 * @param limit 时间间隔（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 高级鼠标悬浮检测器 - 专门用于处理复杂的鼠标悬浮场景
 */
export class AdvancedHoverDetector {
  private element: HTMLElement;
  private onEnter: (element: HTMLElement) => void;
  private onLeave: () => void;
  private enterDelay: number;
  private leaveDelay: number;
  private enterTimer: NodeJS.Timeout | null = null;
  private leaveTimer: NodeJS.Timeout | null = null;
  private isHovering = false;
  private isDestroyed = false;
  
  // 性能优化相关
  private lastMousePosition = { x: 0, y: 0 };
  private mouseMoveThreshold = 5; // 像素
  private lastEventTime = 0;
  private eventThrottleTime = 16; // ~60fps

  constructor(
    element: HTMLElement,
    onEnter: (element: HTMLElement) => void,
    onLeave: () => void,
    options: {
      enterDelay?: number;
      leaveDelay?: number;
      mouseMoveThreshold?: number;
      eventThrottleTime?: number;
    } = {}
  ) {
    this.element = element;
    this.onEnter = onEnter;
    this.onLeave = onLeave;
    this.enterDelay = options.enterDelay || 180;
    this.leaveDelay = options.leaveDelay || 450;
    this.mouseMoveThreshold = options.mouseMoveThreshold || 5;
    this.eventThrottleTime = options.eventThrottleTime || 16;

    this.bindEvents();
  }

  private bindEvents() {
    if (this.isDestroyed) return;
    
    this.element.addEventListener('mouseenter', this.handleMouseEnter, { passive: true });
    this.element.addEventListener('mouseleave', this.handleMouseLeave, { passive: true });
    this.element.addEventListener('mousemove', this.handleMouseMove, { passive: true });
  }

  private handleMouseEnter = (event: MouseEvent) => {
    if (this.isDestroyed) return;
    
    this.clearTimers();
    this.updateMousePosition(event);
    
    if (!this.isHovering) {
      this.enterTimer = setTimeout(() => {
        if (!this.isDestroyed && !this.isHovering) {
          this.isHovering = true;
          try {
            this.onEnter(this.element);
          } catch (error) {
            console.error('Error in hover enter callback:', error);
          }
        }
      }, this.enterDelay);
    }
  };

  private handleMouseLeave = (event: MouseEvent) => {
    if (this.isDestroyed) return;
    
    this.clearTimers();
    this.updateMousePosition(event);
    
    // 检查是否真的离开了元素
    const relatedTarget = event.relatedTarget as HTMLElement;
    if (relatedTarget && this.element.contains(relatedTarget)) {
      return; // 仍在元素内部
    }
    
    if (this.isHovering) {
      this.leaveTimer = setTimeout(() => {
        if (!this.isDestroyed && this.isHovering) {
          this.isHovering = false;
          try {
            this.onLeave();
          } catch (error) {
            console.error('Error in hover leave callback:', error);
          }
        }
      }, this.leaveDelay);
    }
  };

  private handleMouseMove = (event: MouseEvent) => {
    if (this.isDestroyed) return;
    
    const now = Date.now();
    if (now - this.lastEventTime < this.eventThrottleTime) {
      return; // 节流处理
    }
    
    this.lastEventTime = now;
    
    // 检查鼠标移动距离
    const deltaX = Math.abs(event.clientX - this.lastMousePosition.x);
    const deltaY = Math.abs(event.clientY - this.lastMousePosition.y);
    
    if (deltaX > this.mouseMoveThreshold || deltaY > this.mouseMoveThreshold) {
      this.updateMousePosition(event);
      
      // 如果鼠标在移动且还没有悬浮，重置进入定时器
      if (!this.isHovering && this.enterTimer) {
        clearTimeout(this.enterTimer);
        this.enterTimer = setTimeout(() => {
          if (!this.isDestroyed && !this.isHovering) {
            this.isHovering = true;
            try {
              this.onEnter(this.element);
            } catch (error) {
              console.error('Error in hover enter callback:', error);
            }
          }
        }, this.enterDelay);
      }
    }
  };

  private updateMousePosition(event: MouseEvent) {
    this.lastMousePosition.x = event.clientX;
    this.lastMousePosition.y = event.clientY;
  }

  private clearTimers() {
    if (this.enterTimer) {
      clearTimeout(this.enterTimer);
      this.enterTimer = null;
    }
    if (this.leaveTimer) {
      clearTimeout(this.leaveTimer);
      this.leaveTimer = null;
    }
  }

  public destroy() {
    this.isDestroyed = true;
    this.clearTimers();
    
    if (this.element) {
      this.element.removeEventListener('mouseenter', this.handleMouseEnter);
      this.element.removeEventListener('mouseleave', this.handleMouseLeave);
      this.element.removeEventListener('mousemove', this.handleMouseMove);
    }
  }

  public forceEnter() {
    if (this.isDestroyed) return;
    
    this.clearTimers();
    this.isHovering = true;
    try {
      this.onEnter(this.element);
    } catch (error) {
      console.error('Error in force enter:', error);
    }
  }

  public forceLeave() {
    if (this.isDestroyed) return;
    
    this.clearTimers();
    this.isHovering = false;
    try {
      this.onLeave();
    } catch (error) {
      console.error('Error in force leave:', error);
    }
  }

  public getState() {
    return {
      isHovering: this.isHovering,
      isDestroyed: this.isDestroyed,
      hasEnterTimer: !!this.enterTimer,
      hasLeaveTimer: !!this.leaveTimer,
    };
  }
}

/**
 * 创建稳定的鼠标事件处理器 - 支持快速滑动检测
 */
export function createStableMouseHandler(
  onHover: (element: HTMLElement) => void,
  onLeave: () => void,
  options: {
    hoverDelay?: number;
    leaveDelay?: number;
    throttleInterval?: number;
    enableFastSwipe?: boolean;
  } = {}
) {
  const {
    hoverDelay = 180,
    leaveDelay = 450,
    throttleInterval = 50,
    enableFastSwipe = true
  } = options;

  let currentDetector: AdvancedHoverDetector | null = null;
  let lastElement: HTMLElement | null = null;
  let fastSwipeTimer: NodeJS.Timeout | null = null;

  const throttledHover = throttle((element: HTMLElement) => {
    if (element === lastElement) return;

    // 清理之前的检测器
    if (currentDetector) {
      currentDetector.destroy();
    }

    lastElement = element;

    // 创建新的检测器
    currentDetector = new AdvancedHoverDetector(
      element,
      (el) => onHover(el),
      onLeave,
      { 
        enterDelay: hoverDelay, 
        leaveDelay,
        eventThrottleTime: throttleInterval 
      }
    );
  }, throttleInterval);

  // 快速滑动检测
  const handleFastSwipe = enableFastSwipe ? throttle((event: Event) => {
    const target = event.target as HTMLElement;
    const menuItem = target.closest('.ant-menu-item, .ant-menu-submenu');
    
    if (menuItem instanceof HTMLElement && menuItem !== lastElement) {
      // 检测到快速滑动，立即切换
      if (fastSwipeTimer) {
        clearTimeout(fastSwipeTimer);
      }
      
      fastSwipeTimer = setTimeout(() => {
        throttledHover(menuItem);
      }, 50); // 快速响应
    }
  }, 30) : null;

  return {
    handleMouseOver: (event: Event) => {
      const target = event.target as HTMLElement;
      const menuItem = target.closest('.ant-menu-item, .ant-menu-submenu');
      
      if (menuItem instanceof HTMLElement) {
        throttledHover(menuItem);
      }
    },
    
    handleMouseMove: handleFastSwipe || (() => {}),
    
    destroy: () => {
      if (currentDetector) {
        currentDetector.destroy();
        currentDetector = null;
      }
      if (fastSwipeTimer) {
        clearTimeout(fastSwipeTimer);
        fastSwipeTimer = null;
      }
      lastElement = null;
    },

    forceLeave: () => {
      if (currentDetector) {
        currentDetector.forceLeave();
      }
    },

    getState: () => ({
      hasDetector: !!currentDetector,
      lastElement,
      detectorState: currentDetector?.getState() || null,
    }),
  };
}
