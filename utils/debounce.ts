/**
 * 防抖函数 - 延迟执行，只执行最后一次调用
 * @param func 要防抖的函数
 * @param wait 延迟时间（毫秒）
 * @param immediate 是否立即执行第一次调用
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

/**
 * 节流函数 - 限制执行频率
 * @param func 要节流的函数
 * @param limit 时间间隔（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 鼠标悬浮检测器 - 专门用于处理鼠标悬浮事件
 */
export class HoverDetector {
  private element: HTMLElement;
  private onEnter: () => void;
  private onLeave: () => void;
  private enterDelay: number;
  private leaveDelay: number;
  private enterTimer: NodeJS.Timeout | null = null;
  private leaveTimer: NodeJS.Timeout | null = null;
  private isHovering = false;

  constructor(
    element: HTMLElement,
    onEnter: () => void,
    onLeave: () => void,
    options: {
      enterDelay?: number;
      leaveDelay?: number;
    } = {}
  ) {
    this.element = element;
    this.onEnter = onEnter;
    this.onLeave = onLeave;
    this.enterDelay = options.enterDelay || 180;
    this.leaveDelay = options.leaveDelay || 450;

    this.bindEvents();
  }

  private bindEvents() {
    this.element.addEventListener('mouseenter', this.handleMouseEnter);
    this.element.addEventListener('mouseleave', this.handleMouseLeave);
  }

  private handleMouseEnter = () => {
    this.clearTimers();
    
    if (!this.isHovering) {
      this.enterTimer = setTimeout(() => {
        this.isHovering = true;
        this.onEnter();
      }, this.enterDelay);
    }
  };

  private handleMouseLeave = () => {
    this.clearTimers();
    
    if (this.isHovering) {
      this.leaveTimer = setTimeout(() => {
        this.isHovering = false;
        this.onLeave();
      }, this.leaveDelay);
    }
  };

  private clearTimers() {
    if (this.enterTimer) {
      clearTimeout(this.enterTimer);
      this.enterTimer = null;
    }
    if (this.leaveTimer) {
      clearTimeout(this.leaveTimer);
      this.leaveTimer = null;
    }
  }

  public destroy() {
    this.clearTimers();
    this.element.removeEventListener('mouseenter', this.handleMouseEnter);
    this.element.removeEventListener('mouseleave', this.handleMouseLeave);
  }

  public forceEnter() {
    this.clearTimers();
    this.isHovering = true;
    this.onEnter();
  }

  public forceLeave() {
    this.clearTimers();
    this.isHovering = false;
    this.onLeave();
  }
}

/**
 * 创建稳定的鼠标事件处理器
 */
export function createStableMouseHandler(
  onHover: (element: HTMLElement) => void,
  onLeave: () => void,
  options: {
    hoverDelay?: number;
    leaveDelay?: number;
    throttleInterval?: number;
  } = {}
) {
  const {
    hoverDelay = 180,
    leaveDelay = 450,
    throttleInterval = 50
  } = options;

  let currentDetector: HoverDetector | null = null;
  let lastElement: HTMLElement | null = null;

  const throttledHover = throttle((element: HTMLElement) => {
    if (element === lastElement) return;

    // 清理之前的检测器
    if (currentDetector) {
      currentDetector.destroy();
    }

    lastElement = element;

    // 创建新的检测器
    currentDetector = new HoverDetector(
      element,
      () => onHover(element),
      onLeave,
      { enterDelay: hoverDelay, leaveDelay }
    );
  }, throttleInterval);

  return {
    handleMouseOver: (event: Event) => {
      const target = event.target as HTMLElement;
      const menuItem = target.closest('.ant-menu-item, .ant-menu-submenu');
      
      if (menuItem instanceof HTMLElement) {
        throttledHover(menuItem);
      }
    },
    
    destroy: () => {
      if (currentDetector) {
        currentDetector.destroy();
        currentDetector = null;
      }
      lastElement = null;
    },

    forceLeave: () => {
      if (currentDetector) {
        currentDetector.forceLeave();
      }
    }
  };
}
