/**
 * 性能监控工具
 * 监控侧边栏交互的响应时间，确保在100ms内
 */

// 性能指标接口
export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  category: 'hover' | 'click' | 'animation' | 'state_change';
  metadata?: Record<string, any>;
}

// 性能阈值配置
export const PERFORMANCE_THRESHOLDS = {
  HOVER_RESPONSE: 100, // 悬浮响应时间：100ms
  CLICK_RESPONSE: 50,  // 点击响应时间：50ms
  ANIMATION: 250,      // 动画持续时间：250ms
  STATE_CHANGE: 16,    // 状态变更时间：16ms (60fps)
} as const;

// 性能报告类型
export interface PerformanceReport {
  totalMetrics: number;
  averageResponseTime: number;
  slowestInteraction: PerformanceMetric | null;
  fastestInteraction: PerformanceMetric | null;
  thresholdViolations: PerformanceMetric[];
  categorySummary: Record<string, {
    count: number;
    avgDuration: number;
    violations: number;
  }>;
  recommendations: string[];
}

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor | null = null;
  private metrics: PerformanceMetric[] = [];
  private activeTimers = new Map<string, number>();
  private isEnabled = true;
  private maxMetrics = 1000; // 最大保存指标数量

  private constructor() {
    // 在开发模式下启用详细监控
    this.isEnabled = process.env.NODE_ENV === 'development';
    
    // 定期清理旧指标
    this.startCleanupTimer();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 开始计时
   */
  public startTimer(name: string, category: PerformanceMetric['category'], metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const startTime = performance.now();
    this.activeTimers.set(name, startTime);
    
    // 存储元数据用于后续分析
    if (metadata) {
      this.activeTimers.set(`${name}_meta`, metadata as any);
    }
  }

  /**
   * 结束计时并记录指标
   */
  public endTimer(name: string, category: PerformanceMetric['category']): number | null {
    if (!this.isEnabled) return null;

    const startTime = this.activeTimers.get(name);
    if (!startTime) {
      console.warn(`⚠️ 性能监控：未找到计时器 "${name}"`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - startTime;
    const metadata = this.activeTimers.get(`${name}_meta`) as Record<string, any> | undefined;

    // 记录指标
    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: endTime,
      category,
      metadata,
    };

    this.addMetric(metric);

    // 清理计时器
    this.activeTimers.delete(name);
    if (metadata) {
      this.activeTimers.delete(`${name}_meta`);
    }

    // 检查是否超过阈值
    this.checkThreshold(metric);

    return duration;
  }

  /**
   * 直接记录指标（用于不需要计时的情况）
   */
  public recordMetric(
    name: string, 
    duration: number, 
    category: PerformanceMetric['category'],
    metadata?: Record<string, any>
  ): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: performance.now(),
      category,
      metadata,
    };

    this.addMetric(metric);
    this.checkThreshold(metric);
  }

  /**
   * 添加指标到收集器
   */
  private addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);

    // 限制指标数量，移除最旧的指标
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  /**
   * 检查性能阈值
   */
  private checkThreshold(metric: PerformanceMetric): void {
    const threshold = this.getThresholdByCategory(metric.category);
    if (metric.duration > threshold) {
      console.warn(
        `⚠️ 性能警告：${metric.name} 响应时间 ${metric.duration.toFixed(2)}ms 超过阈值 ${threshold}ms`,
        metric
      );

      // 在开发模式下提供优化建议
      if (process.env.NODE_ENV === 'development') {
        this.provideSuggestions(metric);
      }
    }
  }

  /**
   * 根据类别获取阈值
   */
  private getThresholdByCategory(category: PerformanceMetric['category']): number {
    switch (category) {
      case 'hover': return PERFORMANCE_THRESHOLDS.HOVER_RESPONSE;
      case 'click': return PERFORMANCE_THRESHOLDS.CLICK_RESPONSE;
      case 'animation': return PERFORMANCE_THRESHOLDS.ANIMATION;
      case 'state_change': return PERFORMANCE_THRESHOLDS.STATE_CHANGE;
      default: return PERFORMANCE_THRESHOLDS.HOVER_RESPONSE;
    }
  }

  /**
   * 提供性能优化建议
   */
  private provideSuggestions(metric: PerformanceMetric): void {
    const suggestions: string[] = [];

    if (metric.category === 'hover' && metric.duration > PERFORMANCE_THRESHOLDS.HOVER_RESPONSE) {
      suggestions.push('考虑减少防抖延迟时间');
      suggestions.push('检查是否有不必要的DOM查询');
      suggestions.push('考虑使用 requestAnimationFrame 优化动画');
    }

    if (metric.category === 'state_change' && metric.duration > PERFORMANCE_THRESHOLDS.STATE_CHANGE) {
      suggestions.push('检查 Zustand 状态更新是否有不必要的计算');
      suggestions.push('考虑使用 useMemo 缓存计算结果');
      suggestions.push('检查是否触发了不必要的重新渲染');
    }

    if (suggestions.length > 0) {
      console.group(`💡 "${metric.name}" 性能优化建议:`);
      suggestions.forEach(suggestion => console.log(`  • ${suggestion}`));
      console.groupEnd();
    }
  }

  /**
   * 生成性能报告
   */
  public generateReport(): PerformanceReport {
    if (this.metrics.length === 0) {
      return {
        totalMetrics: 0,
        averageResponseTime: 0,
        slowestInteraction: null,
        fastestInteraction: null,
        thresholdViolations: [],
        categorySummary: {},
        recommendations: ['暂无性能数据'],
      };
    }

    // 计算基本统计信息
    const totalDuration = this.metrics.reduce((sum, metric) => sum + metric.duration, 0);
    const averageResponseTime = totalDuration / this.metrics.length;
    
    const slowestInteraction = this.metrics.reduce((slowest, current) => 
      current.duration > slowest.duration ? current : slowest
    );
    
    const fastestInteraction = this.metrics.reduce((fastest, current) => 
      current.duration < fastest.duration ? current : fastest
    );

    // 计算阈值违规
    const thresholdViolations = this.metrics.filter(metric => {
      const threshold = this.getThresholdByCategory(metric.category);
      return metric.duration > threshold;
    });

    // 分类统计
    const categorySummary = this.metrics.reduce((summary, metric) => {
      if (!summary[metric.category]) {
        summary[metric.category] = { count: 0, avgDuration: 0, violations: 0 };
      }
      
      summary[metric.category].count++;
      summary[metric.category].avgDuration += metric.duration;
      
      const threshold = this.getThresholdByCategory(metric.category);
      if (metric.duration > threshold) {
        summary[metric.category].violations++;
      }
      
      return summary;
    }, {} as Record<string, { count: number; avgDuration: number; violations: number }>);

    // 计算平均值
    Object.values(categorySummary).forEach(category => {
      category.avgDuration = category.avgDuration / category.count;
    });

    // 生成推荐
    const recommendations = this.generateRecommendations(categorySummary, thresholdViolations);

    return {
      totalMetrics: this.metrics.length,
      averageResponseTime,
      slowestInteraction,
      fastestInteraction,
      thresholdViolations,
      categorySummary,
      recommendations,
    };
  }

  /**
   * 生成性能优化推荐
   */
  private generateRecommendations(
    categorySummary: PerformanceReport['categorySummary'],
    violations: PerformanceMetric[]
  ): string[] {
    const recommendations: string[] = [];

    // 检查各类别的违规情况
    Object.entries(categorySummary).forEach(([category, stats]) => {
      const violationRate = stats.violations / stats.count;
      
      if (violationRate > 0.3) { // 超过30%的操作违规
        switch (category) {
          case 'hover':
            recommendations.push('悬浮响应时间过长，考虑优化防抖机制或减少DOM操作');
            break;
          case 'click':
            recommendations.push('点击响应时间过长，检查事件处理函数的复杂度');
            break;
          case 'animation':
            recommendations.push('动画性能不佳，考虑使用CSS动画或Web Animations API');
            break;
          case 'state_change':
            recommendations.push('状态变更过慢，检查Zustand状态管理的优化空间');
            break;
        }
      }
    });

    // 通用建议
    if (violations.length > this.metrics.length * 0.2) {
      recommendations.push('整体性能需要优化，建议启用React DevTools Profiler进行详细分析');
    }

    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，继续保持！');
    }

    return recommendations;
  }

  /**
   * 清理旧指标
   */
  private startCleanupTimer(): void {
    setInterval(() => {
      const cutoffTime = performance.now() - 5 * 60 * 1000; // 5分钟前
      this.metrics = this.metrics.filter(metric => metric.timestamp > cutoffTime);
    }, 60 * 1000); // 每分钟清理一次
  }

  /**
   * 重置监控器
   */
  public reset(): void {
    this.metrics = [];
    this.activeTimers.clear();
  }

  /**
   * 启用/禁用监控
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 获取当前指标
   */
  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();

// 便利函数
export const startPerformanceTimer = (name: string, category: PerformanceMetric['category'], metadata?: Record<string, any>) => 
  performanceMonitor.startTimer(name, category, metadata);

export const endPerformanceTimer = (name: string, category: PerformanceMetric['category']) => 
  performanceMonitor.endTimer(name, category);

export const recordPerformance = (name: string, duration: number, category: PerformanceMetric['category'], metadata?: Record<string, any>) => 
  performanceMonitor.recordMetric(name, duration, category, metadata);

export default performanceMonitor;