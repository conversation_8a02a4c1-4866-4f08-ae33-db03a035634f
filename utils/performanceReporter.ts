/**
 * 性能报告工具
 * 定期生成和显示侧边栏性能报告
 */

import { performanceMonitor, PerformanceReport } from './performanceMonitor';

/**
 * 格式化性能报告为控制台输出
 */
export function formatPerformanceReport(report: PerformanceReport): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  console.group('📊 侧边栏性能报告');
  
  // 基本统计
  console.log(`📈 总指标数量: ${report.totalMetrics}`);
  console.log(`⏱️  平均响应时间: ${report.averageResponseTime.toFixed(2)}ms`);
  
  if (report.slowestInteraction) {
    console.log(`🐌 最慢操作: ${report.slowestInteraction.name} (${report.slowestInteraction.duration.toFixed(2)}ms)`);
  }
  
  if (report.fastestInteraction) {
    console.log(`⚡ 最快操作: ${report.fastestInteraction.name} (${report.fastestInteraction.duration.toFixed(2)}ms)`);
  }
  
  // 阈值违规
  if (report.thresholdViolations.length > 0) {
    console.group(`⚠️ 性能警告 (${report.thresholdViolations.length}个)`);
    report.thresholdViolations.forEach(violation => {
      console.log(`  • ${violation.name}: ${violation.duration.toFixed(2)}ms (${violation.category})`);
    });
    console.groupEnd();
  } else {
    console.log('✅ 无性能警告');
  }
  
  // 分类统计
  if (Object.keys(report.categorySummary).length > 0) {
    console.group('📊 分类统计');
    Object.entries(report.categorySummary).forEach(([category, stats]) => {
      const violationRate = ((stats.violations / stats.count) * 100).toFixed(1);
      console.log(
        `  ${getCategoryEmoji(category)} ${category}: ` +
        `${stats.count}次, 平均${stats.avgDuration.toFixed(2)}ms, ` +
        `违规率${violationRate}%`
      );
    });
    console.groupEnd();
  }
  
  // 优化建议
  if (report.recommendations.length > 0) {
    console.group('💡 优化建议');
    report.recommendations.forEach(recommendation => {
      console.log(`  • ${recommendation}`);
    });
    console.groupEnd();
  }
  
  console.groupEnd();
}

/**
 * 获取分类对应的表情符号
 */
function getCategoryEmoji(category: string): string {
  switch (category) {
    case 'hover': return '🖱️';
    case 'click': return '👆';
    case 'animation': return '🎬';
    case 'state_change': return '🔄';
    default: return '📊';
  }
}

/**
 * 启动性能监控定时报告
 */
export function startPerformanceReporting(intervalMinutes: number = 5): () => void {
  if (process.env.NODE_ENV !== 'development') {
    return () => {};
  }

  console.log(`🚀 侧边栏性能监控已启动，每${intervalMinutes}分钟生成一次报告`);

  const intervalId = setInterval(() => {
    const report = performanceMonitor.generateReport();
    
    if (report.totalMetrics > 0) {
      formatPerformanceReport(report);
    } else {
      console.log('📊 侧边栏性能监控：暂无数据');
    }
  }, intervalMinutes * 60 * 1000);

  // 返回清理函数
  return () => {
    clearInterval(intervalId);
    console.log('🛑 侧边栏性能监控已停止');
  };
}

/**
 * 手动生成性能报告
 */
export function generateManualReport(): void {
  const report = performanceMonitor.generateReport();
  formatPerformanceReport(report);
}

/**
 * 性能建议检查器
 */
export function checkPerformanceHealth(): {
  isHealthy: boolean;
  issues: string[];
  suggestions: string[];
} {
  const report = performanceMonitor.generateReport();
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  // 检查平均响应时间
  if (report.averageResponseTime > 50) {
    issues.push(`平均响应时间过长: ${report.averageResponseTime.toFixed(2)}ms`);
    suggestions.push('考虑优化防抖/节流参数');
  }
  
  // 检查违规率
  const violationRate = report.thresholdViolations.length / report.totalMetrics;
  if (violationRate > 0.2) {
    issues.push(`性能违规率过高: ${(violationRate * 100).toFixed(1)}%`);
    suggestions.push('需要进行性能优化');
  }
  
  // 检查各分类的表现
  Object.entries(report.categorySummary).forEach(([category, stats]) => {
    if (stats.avgDuration > getRecommendedThreshold(category)) {
      issues.push(`${category}操作平均时间过长: ${stats.avgDuration.toFixed(2)}ms`);
      suggestions.push(`优化${category}相关的处理逻辑`);
    }
  });
  
  const isHealthy = issues.length === 0;
  
  return {
    isHealthy,
    issues,
    suggestions,
  };
}

/**
 * 获取推荐的性能阈值
 */
function getRecommendedThreshold(category: string): number {
  switch (category) {
    case 'hover': return 80;
    case 'click': return 40;
    case 'animation': return 200;
    case 'state_change': return 10;
    default: return 50;
  }
}

/**
 * 导出性能数据到CSV格式
 */
export function exportPerformanceData(): string {
  const metrics = performanceMonitor.getMetrics();
  
  if (metrics.length === 0) {
    return '';
  }
  
  const headers = ['Name', 'Duration(ms)', 'Category', 'Timestamp', 'Metadata'];
  const csvData = [
    headers.join(','),
    ...metrics.map(metric => [
      `"${metric.name}"`,
      metric.duration.toFixed(2),
      metric.category,
      new Date(metric.timestamp).toISOString(),
      `"${JSON.stringify(metric.metadata || {})}"`,
    ].join(','))
  ];
  
  return csvData.join('\n');
}

/**
 * 重置性能监控数据
 */
export function resetPerformanceData(): void {
  performanceMonitor.reset();
  console.log('🔄 性能监控数据已重置');
}

export default {
  formatPerformanceReport,
  startPerformanceReporting,
  generateManualReport,
  checkPerformanceHealth,
  exportPerformanceData,
  resetPerformanceData,
};