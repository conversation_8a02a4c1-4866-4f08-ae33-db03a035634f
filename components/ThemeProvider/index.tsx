import React, { useMemo, useEffect } from 'react';
import { ConfigProvider, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useThemeStore } from '../../stores';
import { getEnvConfig } from '../../utils/envConfig';

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * 主题提供者组件
 * 使用Ant Design的ConfigProvider来应用主题
 */
const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { isDark, initializeTheme } = useThemeStore();

  // 初始化主题
  useEffect(() => {
    initializeTheme();
  }, [initializeTheme]);

  // 获取环境变量配置
  const envConfig = getEnvConfig();

  // Ant Design 主题配置
  const antdTheme = useMemo(() => {
    return {
      algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
      token: {
        // 基础色彩 - 支持环境变量
        colorPrimary: envConfig.theme.primaryColor,
        borderRadius: envConfig.theme.borderRadius,

        // 字体
        fontSize: 14,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',

        // 间距
        padding: 16,
        margin: 16,

        // 阴影
        boxShadow: isDark
          ? '0 2px 8px rgba(0, 0, 0, 0.45)'
          : '0 2px 8px rgba(0, 0, 0, 0.15)',
      },
      components: {
        Layout: {
          bodyBg: isDark ? '#141414' : '#f5f5f5',
          headerBg: isDark ? '#1f1f1f' : '#ffffff',
          siderBg: isDark ? '#1f1f1f' : '#ffffff',
        },
        Menu: {
          itemBg: 'transparent',
          subMenuItemBg: 'transparent',
          itemSelectedBg: isDark ? '#1677ff33' : '#e6f7ff',
          itemHoverBg: isDark ? '#ffffff0f' : '#f5f5f5',
        },
        Card: {
          headerBg: 'transparent',
          bodyPadding: 24,
          borderRadiusLG: 8,
          colorBorderSecondary: isDark ? '#424242' : '#f0f0f0',
        },
        Button: {
          borderRadius: 6,
        },
        Input: {
          borderRadius: 6,
        },
        Table: {
          headerBg: isDark ? '#262626' : '#fafafa',
        },
      },
    };
  }, [isDark, envConfig.theme.primaryColor, envConfig.theme.borderRadius]);

  return (
    <ConfigProvider 
      theme={antdTheme}
      locale={zhCN}
    >
      {children}
    </ConfigProvider>
  );
};

export default ThemeProvider;
