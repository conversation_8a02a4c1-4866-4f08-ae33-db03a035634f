import React from 'react';
import { Popover, Menu } from 'antd';
import { MenuItem } from '../../types';
import './style.scss';

export interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
}

/**
 * 独立的子菜单弹出组件
 * 用于侧边栏折叠状态下显示子菜单选项
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme,
}) => {
  // 处理菜单项点击
  const handleMenuItemClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) {
          return item;
        }
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        onMenuClick(key, clickedItem.path);
        onClose();
      }
    }
  };

  // 转换子菜单项为Ant Design Menu items格式
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      disabled: item.disabled,
      label: (
        <div className="submenu-item-content">
          <span className="submenu-item-label">{item.label}</span>
          {item.badge && (
            <span className="submenu-item-badge">{item.badge}</span>
          )}
        </div>
      ),
    }));
  };

  // 创建菜单内容
  const menuContent = menuItem?.children ? (
    <Menu
      mode="vertical"
      theme={theme}
      className="submenu-popover-menu"
      onClick={handleMenuItemClick}
      items={convertToMenuItems(menuItem.children)}
      style={{
        border: 'none',
        background: 'transparent',
        padding: '4px 0',
        minWidth: '160px'
      }}
    />
  ) : null;

  if (!visible || !menuItem?.children) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 1,
        pointerEvents: 'none'
      }}
    >
      <Popover
        content={menuContent}
        open={visible}
        arrow={true}
        placement="rightTop"
        trigger={[]}
        mouseEnterDelay={0.1}
        mouseLeaveDelay={0.2}
        onOpenChange={(open) => {
          if (!open) {
            onClose();
          }
        }}
        overlayClassName={`submenu-popover-overlay ${theme}`}
        overlayStyle={{
          pointerEvents: 'auto'
        }}
      >
        <div style={{ width: 1, height: 1, pointerEvents: 'auto' }} />
      </Popover>
    </div>
  );
};

export default SubMenuPopover;
