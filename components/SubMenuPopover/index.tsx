import React, { useEffect, useRef, useState } from 'react';
import { Menu } from 'antd';
import { MenuItem } from '../../types';
import './style.scss';

export interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
}

/**
 * 独立的子菜单弹出组件
 * 用于侧边栏折叠状态下显示子菜单选项
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme,
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 🎯 添加点击状态管理
  const [clickedItemKey, setClickedItemKey] = useState<string | null>(null);
  const [isClosing, setIsClosing] = useState(false);

  // 处理点击外部关闭和ESC键关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [visible, onClose]);

  // 🔧 修复鼠标进入弹出菜单 - 取消关闭定时器
  const handleMouseEnter = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  };

  // 🔧 修复鼠标离开弹出菜单 - 智能延迟关闭
  const handleMouseLeave = (event: React.MouseEvent) => {
    // 🔧 检查鼠标是否移向侧边栏
    const relatedTarget = event.relatedTarget as HTMLElement;
    const sidebar = document.querySelector('.v2-sidebar');

    // 如果鼠标移向侧边栏，不关闭弹出菜单
    if (sidebar && sidebar.contains(relatedTarget)) {
      return;
    }

    // 🔧 延迟关闭，给用户时间重新进入
    closeTimeoutRef.current = setTimeout(() => {
      onClose();
    }, 300); // 增加延迟时间，提高用户体验
  };

  // 🎯 优化菜单项点击处理 - 添加延迟关闭和视觉反馈
  const handleMenuItemClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) {
          return item;
        }
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        // 🎯 立即设置点击状态，提供视觉反馈
        setClickedItemKey(key);
        setIsClosing(true);

        // 🎯 清除任何现有的关闭定时器
        if (closeTimeoutRef.current) {
          clearTimeout(closeTimeoutRef.current);
          closeTimeoutRef.current = null;
        }

        // 🎯 立即执行导航
        onMenuClick(key, clickedItem.path);

        // 🎯 延迟关闭弹出层，给用户足够的视觉反馈时间
        closeTimeoutRef.current = setTimeout(() => {
          onClose();
          // 重置状态
          setClickedItemKey(null);
          setIsClosing(false);
        }, 400); // 400ms 延迟，提供良好的用户体验
      }
    }
  };

  // 🎯 优化子菜单项转换 - 添加点击状态和加载反馈
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => {
      const isClicked = clickedItemKey === item.key;
      const isItemDisabled = item.disabled || isClosing;

      return {
        key: item.key,
        icon: item.icon,
        disabled: isItemDisabled,
        className: `submenu-menu-item ${isClicked ? 'submenu-item-clicked' : ''} ${isClosing && !isClicked ? 'submenu-item-dimmed' : ''}`,
        label: (
          <div className="submenu-item-content">
            <span className="submenu-item-label">{item.label}</span>
            {item.badge && (
              <span className="submenu-item-badge">{item.badge}</span>
            )}
            {/* 🎯 添加加载指示器 */}
            {isClicked && (
              <span className="submenu-item-loading">
                <span className="loading-dot"></span>
                <span className="loading-dot"></span>
                <span className="loading-dot"></span>
              </span>
            )}
          </div>
        ),
      };
    });
  };

  if (!visible || !menuItem?.children) {
    return null;
  }

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme} ${isClosing ? 'submenu-popover-closing' : ''}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div className="submenu-popover-arrow"></div>

      <div className="submenu-popover-content">
        <Menu
          mode="vertical"
          theme={theme}
          className="submenu-popover-menu"
          onClick={handleMenuItemClick}
          items={convertToMenuItems(menuItem.children)}
          style={{
            border: 'none',
            background: 'transparent',
            padding: '4px 0'
          }}
        />
      </div>
    </div>
  );
};

export default SubMenuPopover;
