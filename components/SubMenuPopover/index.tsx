import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Menu } from 'antd';
import { MenuItem } from '../../types';
import './style.scss';

export interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
}

/**
 * 独立的子菜单弹出组件
 * 用于侧边栏折叠状态下显示子菜单选项
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme,
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const mouseInsideRef = useRef(false);
  const cleanupTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 🎯 简化状态管理
  const [clickedItemKey, setClickedItemKey] = useState<string | null>(null);

  // 🔧 统一的清理函数
  const clearAllTimers = useCallback(() => {
    if (cleanupTimerRef.current) {
      clearTimeout(cleanupTimerRef.current);
      cleanupTimerRef.current = null;
    }
  }, []);

  // 🔧 通知父组件的稳定函数
  const notifyParent = useCallback((action: 'enter' | 'leave') => {
    const event = new CustomEvent(`submenu-popover-${action}`, {
      detail: { timestamp: Date.now() }
    });
    document.dispatchEvent(event);
  }, []);

  // 🔧 稳定的鼠标进入处理
  const handleMouseEnter = useCallback(() => {
    mouseInsideRef.current = true;
    clearAllTimers();
    notifyParent('enter');
  }, [clearAllTimers, notifyParent]);

  // 🔧 稳定的鼠标离开处理
  const handleMouseLeave = useCallback((event: React.MouseEvent) => {
    const relatedTarget = event.relatedTarget as HTMLElement;
    const popover = popoverRef.current;

    // 检查是否真的离开了弹出层
    if (relatedTarget && popover && popover.contains(relatedTarget)) {
      return;
    }

    mouseInsideRef.current = false;

    // 延迟关闭，给用户时间移动到相关区域
    cleanupTimerRef.current = setTimeout(() => {
      if (!mouseInsideRef.current) {
        notifyParent('leave');
        onClose();
      }
    }, 150);
  }, [clearAllTimers, notifyParent, onClose]);

  // 🔧 统一的事件处理和清理
  useEffect(() => {
    if (!visible) return;

    // 处理点击外部关闭和ESC键关闭
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscKey);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
      clearAllTimers();
    };
  }, [visible, onClose, clearAllTimers]);





  // 🎯 简化的菜单项点击处理
  const handleMenuItemClick = useCallback(({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) {
          return item;
        }
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        // 设置点击状态
        setClickedItemKey(key);

        // 清除定时器
        clearAllTimers();

        // 执行导航
        onMenuClick(key, clickedItem.path);

        // 延迟关闭
        cleanupTimerRef.current = setTimeout(() => {
          onClose();
        }, 300);
      }
    }
  }, [menuItem, onMenuClick, onClose, clearAllTimers]);

  // 🎯 简化的子菜单项转换
  const convertToMenuItems = useCallback((items: MenuItem[]) => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      disabled: item.disabled,
      className: clickedItemKey === item.key ? 'submenu-item-clicked' : '',
      label: (
        <div className="submenu-item-content">
          <span className="submenu-item-label">{item.label}</span>
          {item.badge && (
            <span className="submenu-item-badge">{item.badge}</span>
          )}
          {clickedItemKey === item.key && (
            <span className="submenu-item-loading">⏳</span>
          )}
        </div>
      ),
  }, [clickedItemKey]);

  if (!visible || !menuItem?.children) {
    return null;
  }

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div className="submenu-popover-arrow"></div>

      <div className="submenu-popover-content">
        <Menu
          mode="vertical"
          theme={theme}
          className="submenu-popover-menu"
          onClick={handleMenuItemClick}
          items={convertToMenuItems(menuItem.children)}
          style={{
            border: 'none',
            background: 'transparent',
            padding: '4px 0'
          }}
        />
      </div>
    </div>
  );
};

export default SubMenuPopover;
