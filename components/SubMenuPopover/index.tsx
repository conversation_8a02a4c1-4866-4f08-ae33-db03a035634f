import React, { useEffect, useRef, useState } from 'react';
import { Menu } from 'antd';
import { MenuItem } from '../../types';
import './style.scss';

export interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
}

/**
 * 独立的子菜单弹出组件
 * 用于侧边栏折叠状态下显示子菜单选项
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme,
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 🎯 添加点击状态管理
  const [clickedItemKey, setClickedItemKey] = useState<string | null>(null);
  const [isClosing, setIsClosing] = useState(false);

  // 🔧 添加鼠标状态跟踪
  const [isMouseInside, setIsMouseInside] = useState(false);
  const mouseInsideRef = useRef(false);

  // 🔧 调试系统
  const debugRef = useRef({
    eventCount: 0,
    lastEvent: '',
    timers: new Set<NodeJS.Timeout>()
  });

  const debugLog = (event: string, details?: any) => {
    if (process.env.NODE_ENV === 'development' && window.location.search.includes('debug=submenu')) {
      debugRef.current.eventCount++;
      debugRef.current.lastEvent = event;
      console.log(`[SubMenuPopover] ${debugRef.current.eventCount}: ${event}`, details);
    }
  };

  // 处理点击外部关闭和ESC键关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [visible, onClose]);

  // 🔧 基于状态机的稳定事件处理系统
  useEffect(() => {
    if (!visible || !popoverRef.current) return;

    const popoverElement = popoverRef.current;
    let mouseState = 'outside'; // 'outside' | 'entering' | 'inside' | 'leaving'
    let stateChangeTimer: NodeJS.Timeout | null = null;

    const setState = (newState: string, reason: string) => {
      if (mouseState !== newState) {
        debugLog(`State change: ${mouseState} -> ${newState}`, { reason });
        mouseState = newState;

        // 清除状态变化定时器
        if (stateChangeTimer) {
          clearTimeout(stateChangeTimer);
          stateChangeTimer = null;
        }
      }
    };

    // 🔧 统一的鼠标进入处理
    const handleMouseEnterUnified = (event: Event) => {
      debugLog('Mouse enter unified', {
        target: (event.target as HTMLElement)?.className,
        currentState: mouseState
      });

      setState('inside', 'mouse enter');
      mouseInsideRef.current = true;
      setIsMouseInside(true);

      // 立即清除所有关闭定时器
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
        debugLog('Cleared close timer on enter');
      }

      // 通知父组件
      const customEvent = new CustomEvent('submenu-popover-enter', {
        detail: {
          timestamp: Date.now(),
          source: 'unified',
          state: mouseState
        }
      });
      document.dispatchEvent(customEvent);
    };

    // 🔧 统一的鼠标离开处理
    const handleMouseLeaveUnified = (event: Event) => {
      const mouseEvent = event as MouseEvent;
      const relatedTarget = mouseEvent.relatedTarget as HTMLElement;

      debugLog('Mouse leave unified', {
        target: (event.target as HTMLElement)?.className,
        relatedTarget: relatedTarget?.className,
        currentState: mouseState
      });

      // 检查是否真的离开了弹出层
      if (relatedTarget && popoverElement.contains(relatedTarget)) {
        debugLog('Still inside popover, ignoring leave event');
        return;
      }

      setState('leaving', 'mouse leave');

      // 🔧 使用状态变化定时器来稳定状态
      stateChangeTimer = setTimeout(() => {
        if (mouseState === 'leaving') {
          setState('outside', 'timer confirmation');
          mouseInsideRef.current = false;
          setIsMouseInside(false);

          const customEvent = new CustomEvent('submenu-popover-leaving', {
            detail: {
              timestamp: Date.now(),
              source: 'unified',
              confirmed: true
            }
          });
          document.dispatchEvent(customEvent);
        }
      }, 30); // 短暂延迟确认状态变化
    };

    // 🔧 只使用一组事件监听器，避免冲突
    popoverElement.addEventListener('mouseenter', handleMouseEnterUnified, true);
    popoverElement.addEventListener('mouseleave', handleMouseLeaveUnified, true);

    return () => {
      popoverElement.removeEventListener('mouseenter', handleMouseEnterUnified, true);
      popoverElement.removeEventListener('mouseleave', handleMouseLeaveUnified, true);

      if (stateChangeTimer) {
        clearTimeout(stateChangeTimer);
      }
    };
  }, [visible]);

  // 🔧 简化的容器级鼠标进入处理
  const handleMouseEnter = () => {
    debugLog('Container mouse enter');

    mouseInsideRef.current = true;
    setIsMouseInside(true);

    // 🔧 强制清除所有定时器
    debugRef.current.timers.forEach(timer => clearTimeout(timer));
    debugRef.current.timers.clear();

    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
      debugLog('Cleared container close timer');
    }

    // 通知父组件
    const event = new CustomEvent('submenu-popover-enter', {
      detail: {
        timestamp: Date.now(),
        source: 'container',
        priority: 'high'
      }
    });
    document.dispatchEvent(event);
  };

  // 🔧 保守的容器级鼠标离开处理
  const handleMouseLeave = (event: React.MouseEvent) => {
    const relatedTarget = event.relatedTarget as HTMLElement;

    debugLog('Container mouse leave', {
      relatedTarget: relatedTarget?.className,
      mouseInside: mouseInsideRef.current
    });

    // 🔧 如果内部状态认为鼠标还在内部，延迟处理
    if (mouseInsideRef.current) {
      debugLog('Internal state says mouse still inside, delaying leave');

      // 设置一个验证定时器
      const verifyTimer = setTimeout(() => {
        if (!mouseInsideRef.current) {
          debugLog('Delayed verification: mouse actually left');
          handleActualLeave(event);
        }
      }, 100);

      debugRef.current.timers.add(verifyTimer);
      return;
    }

    handleActualLeave(event);
  };

  // 🔧 实际的离开处理逻辑
  const handleActualLeave = (event: React.MouseEvent) => {
    const relatedTarget = event.relatedTarget as HTMLElement;

    mouseInsideRef.current = false;
    setIsMouseInside(false);

    // 检查鼠标移动目标
    const sidebar = document.querySelector('.v2-sidebar');
    const popover = popoverRef.current;

    const isMovingToSidebar = sidebar && (
      sidebar.contains(relatedTarget) || sidebar === relatedTarget
    );

    const isMovingWithinPopover = popover && (
      popover.contains(relatedTarget) || popover === relatedTarget
    );

    debugLog('Actual leave check', {
      isMovingToSidebar,
      isMovingWithinPopover
    });

    // 🔧 只有真正离开相关区域才启动关闭流程
    if (!isMovingToSidebar && !isMovingWithinPopover) {
      debugLog('Starting close sequence');

      // 🔧 多重验证关闭机制
      const verifyTimer1 = setTimeout(() => {
        if (!mouseInsideRef.current) {
          debugLog('First verification passed');

          const verifyTimer2 = setTimeout(() => {
            if (!mouseInsideRef.current) {
              debugLog('Second verification passed, closing');

              const customEvent = new CustomEvent('submenu-popover-leaving', {
                detail: {
                  timestamp: Date.now(),
                  source: 'container',
                  verified: true
                }
              });
              document.dispatchEvent(customEvent);

              closeTimeoutRef.current = setTimeout(() => {
                if (!mouseInsideRef.current) {
                  debugLog('Final close execution');
                  onClose();
                }
              }, 50);
            }
          }, 50);

          debugRef.current.timers.add(verifyTimer2);
        }
      }, 50);

      debugRef.current.timers.add(verifyTimer1);
    }
  };

  // 🎯 优化菜单项点击处理 - 添加延迟关闭和视觉反馈
  const handleMenuItemClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) {
          return item;
        }
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        // 🎯 立即设置点击状态，提供视觉反馈
        setClickedItemKey(key);
        setIsClosing(true);

        // 🎯 清除任何现有的关闭定时器
        if (closeTimeoutRef.current) {
          clearTimeout(closeTimeoutRef.current);
          closeTimeoutRef.current = null;
        }

        // 🎯 立即执行导航
        onMenuClick(key, clickedItem.path);

        // 🎯 延迟关闭弹出层，给用户足够的视觉反馈时间
        closeTimeoutRef.current = setTimeout(() => {
          onClose();
          // 重置状态
          setClickedItemKey(null);
          setIsClosing(false);
        }, 400); // 400ms 延迟，提供良好的用户体验
      }
    }
  };

  // 🎯 优化子菜单项转换 - 添加点击状态和加载反馈
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => {
      const isClicked = clickedItemKey === item.key;
      const isItemDisabled = item.disabled || isClosing;

      return {
        key: item.key,
        icon: item.icon,
        disabled: isItemDisabled,
        className: `submenu-menu-item ${isClicked ? 'submenu-item-clicked' : ''} ${isClosing && !isClicked ? 'submenu-item-dimmed' : ''}`,
        label: (
          <div className="submenu-item-content">
            <span className="submenu-item-label">{item.label}</span>
            {item.badge && (
              <span className="submenu-item-badge">{item.badge}</span>
            )}
            {/* 🎯 添加加载指示器 */}
            {isClicked && (
              <span className="submenu-item-loading">
                <span className="loading-dot"></span>
                <span className="loading-dot"></span>
                <span className="loading-dot"></span>
              </span>
            )}
          </div>
        ),
      };
    });
  };

  if (!visible || !menuItem?.children) {
    return null;
  }

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme} ${isClosing ? 'submenu-popover-closing' : ''}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div className="submenu-popover-arrow"></div>

      <div className="submenu-popover-content">
        <Menu
          mode="vertical"
          theme={theme}
          className="submenu-popover-menu"
          onClick={handleMenuItemClick}
          items={convertToMenuItems(menuItem.children)}
          style={{
            border: 'none',
            background: 'transparent',
            padding: '4px 0'
          }}
        />
      </div>
    </div>
  );
};

export default SubMenuPopover;
