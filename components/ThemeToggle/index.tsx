import React, { useRef, useCallback, useEffect, useState } from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined, LoadingOutlined } from '@ant-design/icons';
import { useThemeStore } from '../../stores';
import './style.scss';

interface ThemeToggleProps {
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

// 检测浏览器是否支持 View Transition API 和用户动画偏好
function enableTransitions(): boolean {
  return (
    typeof document !== 'undefined' &&
    'startViewTransition' in document &&
    window.matchMedia('(prefers-reduced-motion: no-preference)').matches
  );
}

/**
 * 主题切换按钮组件
 * 带有圆圈展开动画的亮色/暗色主题切换
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'middle'
}) => {
  const { isDark, toggleTheme } = useThemeStore();
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [isAnimating, setIsAnimating] = useState(false);

  /**
   * 添加触觉反馈（如果支持）
   */
  const addHapticFeedback = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50); // 轻微震动50ms
    }
  }, []);

  /**
   * 处理主题切换（带动画效果）
   */
  const handleToggleTheme = useCallback(async (e?: React.MouseEvent) => {
    // 防止重复点击
    if (isAnimating) return;
    
    // 添加触觉反馈
    addHapticFeedback();
    
    setIsAnimating(true);

    try {
      // 兼容性检测
      if (!enableTransitions()) {
        // 不支持动画时，直接切换主题
        toggleTheme();
        setIsAnimating(false);
        return;
      }

      // 获取点击坐标 (如果是点击事件)
      let clientX = window.innerWidth / 2; // 默认屏幕中心
      let clientY = window.innerHeight / 2;
      
      if (e && e.clientX !== undefined && e.clientY !== undefined) {
        clientX = e.clientX;
        clientY = e.clientY;
      } else if (buttonRef.current) {
        // 如果没有事件对象（比如键盘触发），使用按钮位置
        const rect = buttonRef.current.getBoundingClientRect();
        clientX = rect.left + rect.width / 2;
        clientY = rect.top + rect.height / 2;
      }

      const html = document.documentElement;

      if (html) {
        // 禁用CSS过渡，避免与View Transition冲突
        html.setAttribute('disabled-transition', '');

        // 计算圆形裁剪路径 - 从点击位置扩散到整个屏幕
        const maxRadius = Math.hypot(
          Math.max(clientX, window.innerWidth - clientX),
          Math.max(clientY, window.innerHeight - clientY)
        );

        const clipPath = [
          `circle(0px at ${clientX}px ${clientY}px)`,
          `circle(${maxRadius}px at ${clientX}px ${clientY}px)`
        ];

        // 启动 View Transition
        await document.startViewTransition(async () => {
          // 在这里执行主题切换
          toggleTheme();
        }).ready;

        // 创建自定义动画 - 新内容始终从点击位置向外扩散
        const animation = document.documentElement.animate(
          { 
            // 两个方向都使用从小到大的扩散动画
            // 亮色→暗色：暗色内容从点击位置扩散
            // 暗色→亮色：亮色内容从点击位置扩散
            clipPath: clipPath 
          },
          {
            duration: 500,
            easing: 'ease-in-out',
            pseudoElement: '::view-transition-new(root)'
          }
        );

        // 动画完成后的清理工作
        animation.finished.then(() => {
          html.removeAttribute('disabled-transition');
          setIsAnimating(false);
        });
      } else {
        setIsAnimating(false);
      }
    } catch (error) {
      console.warn('Theme transition animation failed:', error);
      // 动画失败时，确保主题仍然能够切换
      toggleTheme();
      setIsAnimating(false);
    }
  }, [toggleTheme, addHapticFeedback, isAnimating]);

  /**
   * 键盘事件处理
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggleTheme();
    }
  }, [handleToggleTheme]);

  /**
   * 全局键盘快捷键支持 (Ctrl/Cmd + Shift + T)
   */
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        handleToggleTheme();
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [handleToggleTheme]);

  return (
    <Tooltip title={isAnimating ? '主题切换中...' : `切换到${isDark ? '亮色' : '暗色'}主题 (Ctrl+Shift+T)`}>
      <Button
        ref={buttonRef}
        type="text"
        size={size}
        icon={isAnimating ? <LoadingOutlined /> : (isDark ? <SunOutlined /> : <MoonOutlined />)}
        onClick={handleToggleTheme}
        onKeyDown={handleKeyDown}
        className={`theme-toggle-button ${className} ${isAnimating ? 'animating' : ''}`}
        aria-label={`切换到${isDark ? '亮色' : '暗色'}主题，快捷键 Ctrl+Shift+T`}
        disabled={isAnimating}
        tabIndex={0}
      />
    </Tooltip>
  );
};

export default ThemeToggle;
