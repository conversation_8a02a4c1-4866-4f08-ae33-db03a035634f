import React from 'react';

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  size?: number;
}

/**
 * 自定义太阳图标 - 用于暗色主题时显示（点击切换到亮色）
 */
export const CustomSunIcon: React.FC<IconProps> = ({ 
  className = '', 
  style = {}, 
  size = 16 
}) => {
  return (
    <svg
      className={`custom-sun-icon ${className}`}
      style={{ width: size, height: size, ...style }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 太阳中心圆 */}
      <circle
        cx="12"
        cy="12"
        r="4"
        fill="currentColor"
      />
      {/* 太阳光线 */}
      <path
        d="M12 2V4M12 20V22M4 12H2M6.31412 6.31412L4.8999 4.8999M17.6859 6.31412L19.1001 4.8999M6.31412 17.69L4.8999 19.1042M17.6859 17.69L19.1001 19.1042M22 12H20"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  );
};

/**
 * 自定义月亮图标 - 用于亮色主题时显示（点击切换到暗色）
 */
export const CustomMoonIcon: React.FC<IconProps> = ({ 
  className = '', 
  style = {}, 
  size = 16 
}) => {
  return (
    <svg
      className={`custom-moon-icon ${className}`}
      style={{ width: size, height: size, ...style }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 月亮形状 */}
      <path
        d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79Z"
        fill="currentColor"
      />
    </svg>
  );
};

/**
 * 自定义加载图标 - 简单的圆点，无动画
 */
export const CustomLoadingIcon: React.FC<IconProps> = ({ 
  className = '', 
  style = {}, 
  size = 16 
}) => {
  return (
    <svg
      className={`custom-loading-icon ${className}`}
      style={{ width: size, height: size, ...style }}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 三个静态圆点表示加载状态 */}
      <circle cx="6" cy="12" r="2" fill="currentColor" opacity="0.4" />
      <circle cx="12" cy="12" r="2" fill="currentColor" opacity="0.7" />
      <circle cx="18" cy="12" r="2" fill="currentColor" opacity="1" />
    </svg>
  );
};
