import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button, Breadcrumb, Space, Badge, Tooltip, notification } from 'antd';
import {
  BellOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { HeaderProps } from '../../types';
import { useResponsive } from '../../hooks/useResponsiveListener';
import UserAvatar from '../UserAvatar';
import TabsHistory from '../TabsHistory';
import ThemeToggle from '../ThemeToggle';

import './style.scss';

/**
 * 不包含折叠按钮的 Header 组件
 * 用于配合 Layout.Sider 的内置触发器使用
 */
const HeaderWithoutTrigger: React.FC<Omit<HeaderProps, 'collapsed' | 'onCollapse'>> = ({
  user,
  title = 'Admin V2',
  showBreadcrumb = true,
  breadcrumbItems = [],
  breadcrumbComponent,
  actions
}) => {
  const { isMobile } = useResponsive();
  const [api, contextHolder] = notification.useNotification();

  // 处理通知点击
  const handleNotificationClick = () => {
    const notifications = [
      {
        type: 'success',
        message: '系统通知',
        description: '数据同步完成，共处理 1,234 条记录',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      },
      {
        type: 'warning',
        message: '性能警告',
        description: '当前系统负载较高，建议优化查询',
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
      },
      {
        type: 'info',
        message: '功能更新',
        description: '新版本 v2.1.0 已发布，包含多项改进',
        icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      },
      {
        type: 'error',
        message: '错误提醒',
        description: '检测到 3 个数据验证错误，请及时处理',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      },
    ];

    // 随机显示一个通知
    const randomNotification = notifications[Math.floor(Math.random() * notifications.length)];
    
    api.open({
      message: randomNotification.message,
      description: randomNotification.description,
      icon: randomNotification.icon,
      placement: 'topRight',
      duration: 4.5,
    });
  };

  // 渲染面包屑
  const renderBreadcrumb = () => {
    // 如果提供了自定义面包屑组件，优先使用
    if (breadcrumbComponent) {
      return breadcrumbComponent;
    }

    // 格式化面包屑项
    const breadcrumbItemsFormatted = breadcrumbItems.map(item => ({
      title: item.href ? (
        <Link to={item.href} className="breadcrumb-link">
          {item.title}
        </Link>
      ) : (
        <span className="breadcrumb-text">{item.title}</span>
      ),
    }));

    return (
      <div className="breadcrumb-container">
        {/* 面包屑导航 */}
        <Breadcrumb
          className="header-breadcrumb"
          items={breadcrumbItemsFormatted}
        />
      </div>
    );
  };

  // 处理用户退出登录
  const handleLogout = async () => {
    try {
      // 清除本地存储的用户信息和token
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('user');

      // 重定向到登录页
      window.location.href = '/login';
    } catch (error) {
      console.error('退出登录失败:', error);
      throw error; // 重新抛出错误，让UserAvatar组件显示错误信息
    }
  };

  // 处理用户菜单点击
  const handleUserMenuClick = (key: string) => {
    console.log('用户菜单点击:', key);
    // 这里可以添加路由跳转或其他逻辑
  };

  // 渲染用户信息 - 使用独立的UserAvatar组件
  const renderUserInfo = () => {
    return (
      <UserAvatar
        user={user}
        onLogout={handleLogout}
        onMenuClick={handleUserMenuClick}
      />
    );
  };

  // 渲染操作按钮
  const renderActions = () => {
    return (
      <Space size="small" className="header-actions">
        {/* 通知按钮 - 使用弹窗式通知 */}
        <Tooltip title="通知">
          <Badge dot>
            <Button
              type="text"
              icon={<BellOutlined />}
              className="action-button"
              onClick={handleNotificationClick}
            />
          </Badge>
        </Tooltip>

        {/* 主题切换按钮 */}
        <ThemeToggle className="action-button" />

        {/* 设置按钮 */}
        <Tooltip title="设置">
          <Button
            type="text"
            icon={<SettingOutlined />}
            className="action-button"
          />
        </Tooltip>

        {/* 自定义操作 */}
        {actions}

        {/* 用户信息 */}
        {renderUserInfo()}
      </Space>
    );
  };

  return (
    <div className="v2-header-container">
      {contextHolder}
      {/* 顶部导航栏 */}
      <div className="v2-header">
        <div className="header-left">
          {/* 标题和面包屑 */}
          <div className="header-content">
            {isMobile ? (
              <span className="header-title">{title}</span>
            ) : (
              renderBreadcrumb()
            )}
          </div>
        </div>

        <div className="header-right">
          {renderActions()}
        </div>
      </div>

      {/* 独立的标签页历史记录区域 */}
      <div className="tabs-history-area">
        <TabsHistory />
      </div>
    </div>
  );
};

export default HeaderWithoutTrigger;
