import React, { useEffect, useRef } from 'react';
import { Popover, Menu } from 'antd';
import { MenuItem } from '../../types';
import './SubMenuPopover.scss';

interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
  trigger?: 'click' | 'hover';
  width?: number | string;
  placement?: 'right' | 'left';
}

/**
 * 侧边栏折叠状态下的子菜单弹出组件
 * 基于 Ant Design 官方 Popover 组件重构，解决自定义箭头问题
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme = 'light',
  trigger = 'hover',
  width = 200,
  placement = 'right',
}) => {

  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  // 处理 Popover 显示状态变化
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // 延迟关闭，给鼠标移动到 Popover 内容的时间
      closeTimeoutRef.current = setTimeout(() => {
        onClose();
      }, 100);
    } else {
      // 如果重新打开，清除关闭定时器
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
    }
  };

  // 处理菜单项点击
  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        onMenuClick(key, clickedItem.path);
        onClose();
      }
    }
  };

  // 转换子菜单项为 Ant Design Menu items 格式
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      disabled: item.disabled,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{item.label}</span>
          {item.badge && (
            <span style={{
              background: '#ff4d4f',
              color: 'white',
              fontSize: '12px',
              padding: '2px 6px',
              borderRadius: '10px',
              minWidth: '16px',
              textAlign: 'center',
              marginLeft: '8px'
            }}>
              {item.badge}
            </span>
          )}
        </div>
      ),
    }));
  };

  if (!visible || !menuItem?.children) {
    return null;
  }



  // Popover 内容
  const popoverContent = (
    <div style={{
      minWidth: typeof width === 'number' ? `${width}px` : width,
      maxWidth: '240px',
    }}>
      <Menu
        mode="vertical"
        theme={theme}
        onClick={handleMenuClick}
        items={convertToMenuItems(menuItem.children)}
        style={{
          border: 'none',
          background: 'transparent',
          // 确保文字颜色正确显示
          color: theme === 'dark' ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
        }}
        // 添加类名以便样式控制
        className={`submenu-popover-menu ${theme}`}
      />
    </div>
  );

  return (
    <div
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
        pointerEvents: 'none', // 让容器不阻挡鼠标事件
      }}
    >
      <Popover
        content={popoverContent}
        open={visible}
        onOpenChange={handleOpenChange}
        trigger={trigger === 'hover' ? ['hover'] : ['click']}  // 恢复触发器
        placement={placement === 'right' ? 'rightTop' : 'leftTop'}
        arrow={{ pointAtCenter: false }}
        mouseEnterDelay={0.1}
        mouseLeaveDelay={0.3}
        destroyTooltipOnHide={false}
        getPopupContainer={() => document.body}
        overlayClassName="submenu-popover-container"  // 添加类名以便样式生效
        overlayStyle={{
          pointerEvents: 'auto', // 确保弹出内容可以接收鼠标事件
        }}
        // 鼠标事件处理 - 确保悬浮窗交互正常
        onMouseEnter={() => {
          // 鼠标进入 Popover 时，清除关闭定时器
          if (closeTimeoutRef.current) {
            clearTimeout(closeTimeoutRef.current);
            closeTimeoutRef.current = null;
          }
        }}
        onMouseLeave={() => {
          // 鼠标离开 Popover 时，延迟关闭
          if (trigger === 'hover') {
            closeTimeoutRef.current = setTimeout(() => {
              onClose();
            }, 200); // 减少延迟时间，提供更好的响应性
          }
        }}
      >
        <div
          style={{
            width: 8,
            height: 8,
            background: 'transparent',
            pointerEvents: 'auto', // 确保触发器可以接收鼠标事件
          }}
        />
      </Popover>
    </div>
  );
};

export default SubMenuPopover;
