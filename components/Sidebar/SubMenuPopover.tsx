import React, { useEffect, useRef } from 'react';
import { MenuItem } from '../../types';
import './SubMenuPopover.scss';

interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
  width?: number | string;
}

/**
 * 侧边栏折叠状态下的子菜单弹出组件
 * 优化版本：
 * - 立即响应悬浮触发 (0ms 延迟)
 * - 延长关闭响应 (2000ms 延迟)
 * - 简化的事件处理逻辑
 * - 给用户充足时间重新移动到菜单区域
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme = 'light',
  width = 500,
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 简化的事件监听器 - 只处理必要的交互
  useEffect(() => {
    if (!visible) return;

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    // 立即添加 ESC 键监听，移除复杂的延迟逻辑
    document.addEventListener('keydown', handleEscKey);

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      // 清理定时器
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
    };
  }, [visible, onClose]);

  // 处理鼠标进入弹出菜单 - 简化逻辑，只支持悬浮触发
  const handleMouseEnter = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  };

  // 处理鼠标离开弹出菜单 - 延长关闭时间，给用户更多时间
  const handleMouseLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      onClose();
    }, 2000); // 2秒延迟，避免意外关闭
  };

  // 简化的菜单项点击处理
  const handleMenuItemClick = (item: MenuItem) => {
    if (item.path && !item.children?.length) {
      onMenuClick(item.key, item.path);
      onClose(); // 点击后立即关闭
    }
  };



  // 渲染菜单项
  const renderMenuItem = (item: MenuItem) => (
    <div
      key={item.key}
      className={`submenu-menu-item ${item.disabled ? 'disabled' : ''}`}
      onClick={() => !item.disabled && handleMenuItemClick(item)}
    >
      {item.icon && <span className="submenu-item-icon">{item.icon}</span>}
      <span className="submenu-item-label">{item.label}</span>
      {item.badge && (
        <span className="submenu-item-badge">{item.badge}</span>
      )}
    </div>
  );

  if (!visible || !menuItem?.children) {
    return null;
  }

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
        backgroundColor: theme === 'dark' ? '#1f1f1f' : '#ffffff',
        border: theme === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid #d9d9d9',
        borderRadius: '6px',
        width: typeof width === 'number' ? `${width}px` : width,
        minWidth: '160px',
        maxWidth: '240px',
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="submenu-popover-content">
        <div className="submenu-popover-menu">
          {menuItem.children.map(renderMenuItem)}
        </div>
      </div>
    </div>
  );
};

export default SubMenuPopover;
