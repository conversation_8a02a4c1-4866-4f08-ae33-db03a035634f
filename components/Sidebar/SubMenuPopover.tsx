import React, { useEffect, useRef } from 'react';
import { Popover, Menu } from 'antd';
import { MenuItem } from '../../types';

interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
  trigger?: 'click' | 'hover';
  width?: number | string;
  placement?: 'right' | 'left';
}

/**
 * 侧边栏折叠状态下的子菜单弹出组件
 * 基于 Ant Design 官方 Popover 组件重构，解决自定义箭头问题
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme = 'light',
  trigger = 'hover',
  width = 200,
  placement = 'right',
}) => {

  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  // 处理 Popover 显示状态变化
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  // 处理菜单项点击
  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        onMenuClick(key, clickedItem.path);
        onClose();
      }
    }
  };

  // 转换子菜单项为 Ant Design Menu items 格式
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      disabled: item.disabled,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{item.label}</span>
          {item.badge && (
            <span style={{
              background: '#ff4d4f',
              color: 'white',
              fontSize: '12px',
              padding: '2px 6px',
              borderRadius: '10px',
              minWidth: '16px',
              textAlign: 'center',
              marginLeft: '8px'
            }}>
              {item.badge}
            </span>
          )}
        </div>
      ),
    }));
  };

  if (!visible || !menuItem?.children) {
    return null;
  }



  // Popover 内容
  const popoverContent = (
    <Menu
      mode="vertical"
      theme={theme}
      onClick={handleMenuClick}
      items={convertToMenuItems(menuItem.children)}
      style={{
        border: 'none',
        background: 'transparent',
        minWidth: typeof width === 'number' ? `${width}px` : width,
        maxWidth: '240px',
      }}
    />
  );

  return (
    <div
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
      }}
    >
      <Popover
        content={popoverContent}
        open={visible}
        onOpenChange={handleOpenChange}
        trigger={[]}  // 禁用默认触发器，由外部控制
        placement={placement === 'right' ? 'rightTop' : 'leftTop'}
        arrow={{ pointAtCenter: false }}
        mouseEnterDelay={0.1}
        mouseLeaveDelay={0.3}
        destroyTooltipOnHide={false}
        getPopupContainer={() => document.body}
      >
        <div
          style={{
            width: 1,
            height: 1,
            background: 'transparent',
          }}
        />
      </Popover>
    </div>
  );
};

export default SubMenuPopover;
