import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { SidebarProps } from '../../types';
import { useResponsive } from '../../hooks/useResponsiveListener';

import './style.scss';

const { Sider } = Layout;

/**
 * 使用 Ant Design 官方 Layout.Sider 的侧边栏组件
 * 支持内置的折叠功能和主题切换
 */
const SiderLayout: React.FC<SidebarProps> = ({
  collapsed = false,
  onCollapse,
  menuItems = [],
  selectedKey,
  onMenuSelect,
  theme = 'light',
  width = 240,
  collapsedWidth = 80,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile } = useResponsive();

  // 菜单状态
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // 转换菜单项为 Ant Design Menu 格式
  const convertMenuItems = (items: any[]): any[] => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      label: item.label,
      children: item.children ? convertMenuItems(item.children) : undefined,
    }));
  };

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    setSelectedKeys([key]);
    onMenuSelect?.(key);
    
    // 查找对应的菜单项并导航
    const findMenuItem = (items: any[], targetKey: string): any => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    const menuItem = findMenuItem(menuItems, key);
    if (menuItem?.path) {
      navigate(menuItem.path);
    }
  };

  // 处理子菜单展开/收起
  const handleOpenChange = (keys: string[]) => {
    if (!collapsed) {
      setOpenKeys(keys);
    }
  };

  // 根据当前路径设置选中的菜单项
  useEffect(() => {
    const currentPath = location.pathname;
    
    const findKeyByPath = (items: any[], path: string): string | null => {
      for (const item of items) {
        if (item.path === path) return item.key;
        if (item.children) {
          const found = findKeyByPath(item.children, path);
          if (found) return found;
        }
      }
      return null;
    };

    const key = findKeyByPath(menuItems, currentPath);
    if (key) {
      setSelectedKeys([key]);
    }
  }, [location.pathname, menuItems]);

  // 转换后的菜单项
  const antdMenuItems = convertMenuItems(menuItems);

  // 自定义触发器
  const customTrigger = (
    <Button
      type="text"
      icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
      onClick={() => onCollapse?.(!collapsed)}
      style={{
        fontSize: '16px',
        width: 64,
        height: 64,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    />
  );

  return (
    <Sider
      theme={theme}
      collapsible
      collapsed={collapsed}
      onCollapse={onCollapse}
      trigger={customTrigger}
      width={width}
      collapsedWidth={collapsedWidth}
      breakpoint={isMobile ? 'lg' : undefined}
      className={`v2-sider-layout ${theme}`}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
      }}
    >
      {/* Logo区域 */}
      <div className="sider-logo">
        <div className="logo-content">
          {collapsed ? (
            <span className="logo-icon">A</span>
          ) : (
            <>
              <span className="logo-icon">A</span>
              <span className="logo-text">Admin V2</span>
            </>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <Menu
        theme={theme}
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={collapsed ? [] : openKeys}
        onOpenChange={handleOpenChange}
        onClick={handleMenuClick}
        items={antdMenuItems}
        style={{ borderRight: 0 }}
      />

      {/* 底部区域 */}
      {!collapsed && (
        <div className="sider-footer">
          <div className="footer-content">
            <span className="version-info">V2.0.0</span>
          </div>
        </div>
      )}
    </Sider>
  );
};

export default SiderLayout;
