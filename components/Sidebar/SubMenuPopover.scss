@use '../../styles/variables' as *;

// 子菜单弹出组件样式 - 重构版本，流畅动画设计
.submenu-popover {
  position: fixed !important;
  z-index: 9999 !important;
  background: $sidebar-bg-light;
  border-radius: $border-radius-base;
  border: 1px solid $border-color-split;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: submenuFadeIn 0.25s cubic-bezier(0.4, 0.0, 0.2, 1);
  display: block !important;
  overflow: visible;
  backdrop-filter: blur(8px);
  
  // 性能优化
  transform-origin: left center;
  will-change: transform, opacity;
  contain: layout style paint;

  // 暗色主题
  &.dark {
    background: $sidebar-bg-dark;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  // 过渡状态样式
  &.transitioning {
    transition: opacity 0.15s ease, transform 0.15s ease;
    pointer-events: none;
  }
  
  // 加载状态
  &.loading {
    opacity: 0.6;
    
    .submenu-popover-content {
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
        border: 2px solid $primary-color;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
      }
    }
  }



  .submenu-popover-content {
    padding: 4px 0;
    overflow: hidden;
    position: relative;
  }

  // 自定义菜单样式，不依赖Ant Design
  .submenu-popover-menu {
    .submenu-menu-item {
      height: 40px;
      line-height: 40px;
      margin: 2px 8px;
      padding: 0 12px;
      border-radius: $border-radius-base;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: $font-size-sm;
      color: $text-color;
      position: relative;

      // 图标样式
      .submenu-item-icon {
        font-size: 16px;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.2s ease;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .anticon {
          font-size: 16px;
        }
      }

      .submenu-item-label {
        flex: 1;
        transition: all 0.2s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
        letter-spacing: 0.5px;
      }

      .submenu-item-badge {
        background: $error-color;
        color: white;
        font-size: $font-size-xs;
        padding: 3px 8px;
        border-radius: $border-radius-lg;
        min-width: 18px;
        text-align: center;
        margin-left: 12px;
        flex-shrink: 0;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(255, 77, 79, 0.3);
      }

      // 悬停状态 - 增强版
      &:hover:not(.disabled) {
        background: $background-color-light;
        color: $primary-color;
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba($primary-color, 0.1);

        .submenu-item-icon {
          color: $primary-color;
          transform: scale(1.1) rotate(5deg);
        }

        .submenu-item-label {
          font-weight: 500;
        }
      }
      
      // 点击状态
      &:active:not(.disabled) {
        transform: translateX(1px) scale(0.98);
        transition-duration: 0.1s;
      }

      // 禁用状态
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
                  &:hover {
            background: transparent;
            color: $text-color;
            transform: none;
            box-shadow: none;
            
            .submenu-item-icon {
              transform: none;
            }
            
            .submenu-item-label {
              font-weight: 400;
            }
          }
      }

      // 第一个和最后一个项目的特殊处理
      &:first-child {
        margin-top: 4px;
        animation: menuItemSlideIn 0.3s ease-out;
      }

      &:last-child {
        margin-bottom: 4px;
      }
      
      // 交错动画效果
      &:nth-child(1) { animation-delay: 0ms; }
      &:nth-child(2) { animation-delay: 50ms; }
      &:nth-child(3) { animation-delay: 100ms; }
      &:nth-child(4) { animation-delay: 150ms; }
      &:nth-child(5) { animation-delay: 200ms; }
      &:nth-child(n+6) { animation-delay: 250ms; }
      
      // 默认菜单项动画
      animation: menuItemSlideIn 0.25s ease-out both;
    }
  }

  // 暗色主题下的菜单项
  &.dark .submenu-popover-menu {
    .submenu-menu-item {
      color: rgba(255, 255, 255, 0.85);

      &:hover:not(.disabled) {
        background: rgba(255, 255, 255, 0.08);
        color: $primary-color;

        .submenu-item-icon {
          color: $primary-color;
          transform: scale(1.05);
        }

        .submenu-item-label {
          font-weight: 500;
        }
      }

      &.disabled {
        color: rgba(255, 255, 255, 0.45);
        
        &:hover {
          .submenu-item-icon {
            transform: none;
          }
          
          .submenu-item-label {
            font-weight: 400;
          }
        }
      }

      // 徽章样式已在主样式中定义，暗色主题下无需额外样式
    }
  }

  // 分割线样式（如果需要）
  .submenu-divider {
    height: 1px;
    background: $border-color-split;
    margin: 4px 0;
  }

  &.dark .submenu-divider {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 动画效果 - 更流畅的动画
@keyframes submenuFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-12px) scale(0.9);
    filter: blur(2px);
  }
  60% {
    opacity: 0.8;
    transform: translateX(2px) scale(1.02);
    filter: blur(0.5px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 菜单项进入动画
@keyframes menuItemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 响应式适配
@media (max-width: $breakpoint-sm) {
  .submenu-popover {
    display: none; // 移动端不显示弹出菜单
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .submenu-popover {
    border-width: 0.5px;
  }
}

// 减少动画偏好设置
@media (prefers-reduced-motion: reduce) {
  .submenu-popover {
    animation: none !important;
    
    .submenu-menu-item {
      animation: none !important;
      transition: none !important;
      
      &:hover:not(.disabled) {
        transform: none !important;
      }
      
      .submenu-item-icon {
        transform: none !important;
      }
    }
  }
}

// 键盘导航支持
.submenu-popover {
  .submenu-menu-item {
    &:focus-visible {
      outline: 2px solid $primary-color;
      outline-offset: 2px;
      border-radius: $border-radius-base;
    }
    
    // 高对比度模式
    @media (prefers-contrast: high) {
      &:hover:not(.disabled) {
        background: $primary-color;
        color: white;
        
        .submenu-item-icon {
          color: white;
        }
      }
    }
  }
}

