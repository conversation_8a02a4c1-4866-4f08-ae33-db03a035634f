@use '../../styles/variables' as *;

// 子菜单弹出组件样式 - 改进版本，参考HeaderPopover设计
.submenu-popover {
  position: fixed !important;
  z-index: 9999 !important;
  background: $sidebar-bg-light;
  border-radius: $border-radius-base;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid $border-color-split;
  animation: submenuFadeIn 0.2s ease-out;
  display: block !important;
  overflow: visible; // 确保箭头可见

  // 暗色主题
  &.dark {
    background: $sidebar-bg-dark;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.2);

    .submenu-popover-arrow {
      &::before {
        background: $sidebar-bg-dark;
        border-color: rgba(255, 255, 255, 0.1);
        box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.2);
      }

      &::after {
        background: $sidebar-bg-dark;
      }
    }
  }

  // 箭头指示器 - 支持多个方向
  .submenu-popover-arrow {
    position: absolute;
    width: 12px;
    height: 12px;
    z-index: 1;

    // 使用伪元素创建箭头
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 12px;
      height: 12px;
      background: $sidebar-bg-light;
      border: 1px solid $border-color-split;
      border-right: none;
      border-bottom: none;
      box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05);
    }

    // 遮盖多余的边框
    &::after {
      content: '';
      position: absolute;
      left: 2px;
      top: 0;
      width: 10px;
      height: 12px;
      background: $sidebar-bg-light;
      z-index: 2;
    }
  }

  .submenu-popover-content {
    padding: 6px 0;
    overflow: hidden;
    position: relative;
    z-index: 2;
  }

  // 自定义菜单样式，不依赖Ant Design
  .submenu-popover-menu {
    .submenu-menu-item {
      height: 40px;
      line-height: 40px;
      margin: 2px 8px;
      padding: 0 12px;
      border-radius: $border-radius-base;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: $font-size-sm;
      color: $text-color;
      position: relative;

      // 图标样式
      .submenu-item-icon {
        font-size: 16px;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.2s ease;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .anticon {
          font-size: 16px;
        }
      }

      .submenu-item-label {
        flex: 1;
        transition: all 0.2s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
        letter-spacing: 0.5px;
      }

      .submenu-item-badge {
        background: $error-color;
        color: white;
        font-size: $font-size-xs;
        padding: 3px 8px;
        border-radius: $border-radius-lg;
        min-width: 18px;
        text-align: center;
        margin-left: 12px;
        flex-shrink: 0;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(255, 77, 79, 0.3);
      }

      // 悬停状态
      &:hover:not(.disabled) {
        background: $background-color-light;
        color: $primary-color;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        
        .submenu-item-icon {
          color: $primary-color;
          transform: scale(1.1);
        }

        .submenu-item-label {
          font-weight: 500;
        }
      }

      // 禁用状态
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          background: transparent;
          color: $text-color;
          transform: none;
          box-shadow: none;
          
          .submenu-item-icon {
            transform: none;
          }
          
          .submenu-item-label {
            font-weight: 400;
          }
        }
      }

      // 第一个和最后一个项目的特殊处理
      &:first-child {
        margin-top: 4px;
      }

      &:last-child {
        margin-bottom: 4px;
      }
    }
  }

  // 暗色主题下的菜单项
  &.dark .submenu-popover-menu {
    .submenu-menu-item {
      color: rgba(255, 255, 255, 0.85);

      &:hover:not(.disabled) {
        background: rgba(255, 255, 255, 0.08);
        color: $primary-color;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        
        .submenu-item-icon {
          color: $primary-color;
          transform: scale(1.1);
        }

        .submenu-item-label {
          font-weight: 500;
        }
      }

      &.disabled {
        color: rgba(255, 255, 255, 0.45);
        
        &:hover {
          .submenu-item-icon {
            transform: none;
          }
          
          .submenu-item-label {
            font-weight: 400;
          }
        }
      }

      .submenu-item-badge {
        box-shadow: 0 1px 3px rgba(255, 77, 79, 0.5);
      }
    }
  }

  // 分割线样式（如果需要）
  .submenu-divider {
    height: 1px;
    background: $border-color-split;
    margin: 4px 0;
  }

  &.dark .submenu-divider {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 动画效果 - 更流畅的动画
@keyframes submenuFadeIn {
  from {
    opacity: 0;
    transform: translateX(-8px) scale(0.95);
    filter: blur(1px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

// 响应式适配
@media (max-width: $breakpoint-sm) {
  .submenu-popover {
    display: none; // 移动端不显示弹出菜单
  }
}

// ===== 新增：Ant Design Menu 组件样式修复 =====
.submenu-popover-menu {
  // 基础样式
  &.ant-menu {
    background: transparent !important;
    border: none !important;
  }

  .ant-menu-item,
  .ant-menu-submenu-title {
    // 确保文字颜色正确显示
    color: inherit !important;
    margin: 2px 8px !important;
    border-radius: 6px !important;
    height: 40px !important;
    line-height: 40px !important;

    // 悬停效果
    &:hover {
      background-color: rgba(0, 0, 0, 0.04) !important;
      color: $primary-color !important;
    }

    // 选中状态
    &.ant-menu-item-selected {
      background-color: rgba(24, 144, 255, 0.1) !important;
      color: $primary-color !important;
    }

    // 禁用状态
    &.ant-menu-item-disabled {
      color: rgba(0, 0, 0, 0.25) !important;
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  // 菜单项图标
  .ant-menu-item-icon,
  .ant-menu-submenu-arrow {
    color: inherit !important;
    margin-right: 12px !important;
  }

  // 明亮主题
  &.light {
    .ant-menu-item,
    .ant-menu-submenu-title {
      color: rgba(0, 0, 0, 0.85) !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04) !important;
        color: $primary-color !important;
      }

      &.ant-menu-item-selected {
        background-color: rgba(24, 144, 255, 0.1) !important;
        color: $primary-color !important;
      }

      &.ant-menu-item-disabled {
        color: rgba(0, 0, 0, 0.25) !important;
      }
    }
  }

  // 暗黑主题
  &.dark {
    .ant-menu-item,
    .ant-menu-submenu-title {
      color: rgba(255, 255, 255, 0.85) !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.08) !important;
        color: $primary-color !important;
      }

      &.ant-menu-item-selected {
        background-color: rgba(24, 144, 255, 0.2) !important;
        color: $primary-color !important;
      }

      &.ant-menu-item-disabled {
        color: rgba(255, 255, 255, 0.25) !important;
      }
    }
  }
}

// Popover 容器样式优化
.ant-popover.submenu-popover-container {
  // 确保 z-index 正确
  z-index: 9999 !important;

  .ant-popover-content {
    // 确保内容区域可以接收鼠标事件
    pointer-events: auto;

    .ant-popover-inner {
      // 调整内边距
      padding: 4px 0;

      // 确保背景色正确
      background: $sidebar-bg-light;
      border: 1px solid $border-color-split;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08);
    }
  }

  // 箭头样式
  .ant-popover-arrow {
    .ant-popover-arrow-content {
      background: $sidebar-bg-light;
      border: 1px solid $border-color-split;
    }
  }
}

// 暗黑主题下的 Popover 样式
[data-theme="dark"] .ant-popover.submenu-popover-container {
  .ant-popover-content {
    .ant-popover-inner {
      background: $sidebar-bg-dark;
      border-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.2);
    }
  }

  .ant-popover-arrow {
    .ant-popover-arrow-content {
      background: $sidebar-bg-dark;
      border-color: rgba(255, 255, 255, 0.1);
    }
  }
}

