@use '../../styles/variables' as *;

// 子菜单弹出组件样式 - 简洁版本，无阴影设计
.submenu-popover {
  position: fixed !important;
  z-index: 9999 !important;
  background: $sidebar-bg-light;
  border-radius: $border-radius-base;
  border: 1px solid $border-color-split;
  animation: submenuFadeIn 0.2s ease-out;
  display: block !important;
  overflow: visible;

  // 暗色主题
  &.dark {
    background: $sidebar-bg-dark;
    border-color: rgba(255, 255, 255, 0.1);
  }



  .submenu-popover-content {
    padding: 4px 0;
    overflow: hidden;
    position: relative;
  }

  // 自定义菜单样式，不依赖Ant Design
  .submenu-popover-menu {
    .submenu-menu-item {
      height: 40px;
      line-height: 40px;
      margin: 2px 8px;
      padding: 0 12px;
      border-radius: $border-radius-base;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: $font-size-sm;
      color: $text-color;
      position: relative;

      // 图标样式
      .submenu-item-icon {
        font-size: 16px;
        margin-right: 12px;
        flex-shrink: 0;
        transition: all 0.2s ease;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .anticon {
          font-size: 16px;
        }
      }

      .submenu-item-label {
        flex: 1;
        transition: all 0.2s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
        letter-spacing: 0.5px;
      }

      .submenu-item-badge {
        background: $error-color;
        color: white;
        font-size: $font-size-xs;
        padding: 3px 8px;
        border-radius: $border-radius-lg;
        min-width: 18px;
        text-align: center;
        margin-left: 12px;
        flex-shrink: 0;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(255, 77, 79, 0.3);
      }

      // 悬停状态
      &:hover:not(.disabled) {
        background: $background-color-light;
        color: $primary-color;

        .submenu-item-icon {
          color: $primary-color;
          transform: scale(1.05);
        }

        .submenu-item-label {
          font-weight: 500;
        }
      }

      // 禁用状态
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          background: transparent;
          color: $text-color;
          transform: none;
          box-shadow: none;
          
          .submenu-item-icon {
            transform: none;
          }
          
          .submenu-item-label {
            font-weight: 400;
          }
        }
      }

      // 第一个和最后一个项目的特殊处理
      &:first-child {
        margin-top: 4px;
      }

      &:last-child {
        margin-bottom: 4px;
      }
    }
  }

  // 暗色主题下的菜单项
  &.dark .submenu-popover-menu {
    .submenu-menu-item {
      color: rgba(255, 255, 255, 0.85);

      &:hover:not(.disabled) {
        background: rgba(255, 255, 255, 0.08);
        color: $primary-color;

        .submenu-item-icon {
          color: $primary-color;
          transform: scale(1.05);
        }

        .submenu-item-label {
          font-weight: 500;
        }
      }

      &.disabled {
        color: rgba(255, 255, 255, 0.45);
        
        &:hover {
          .submenu-item-icon {
            transform: none;
          }
          
          .submenu-item-label {
            font-weight: 400;
          }
        }
      }

      // 徽章样式已在主样式中定义，暗色主题下无需额外样式
    }
  }

  // 分割线样式（如果需要）
  .submenu-divider {
    height: 1px;
    background: $border-color-split;
    margin: 4px 0;
  }

  &.dark .submenu-divider {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 动画效果 - 更流畅的动画
@keyframes submenuFadeIn {
  from {
    opacity: 0;
    transform: translateX(-8px) scale(0.95);
    filter: blur(1px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

// 响应式适配
@media (max-width: $breakpoint-sm) {
  .submenu-popover {
    display: none; // 移动端不显示弹出菜单
  }
}

