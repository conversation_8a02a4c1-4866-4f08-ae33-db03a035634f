/**
 * 侧边栏错误边界组件
 * 确保在异常情况下的基本可用性
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button } from 'antd';
import { ExclamationCircleOutlined, ReloadOutlined } from '@ant-design/icons';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

const MAX_RETRY_COUNT = 3;

export class SidebarErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 侧边栏组件出现错误:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // 调用外部错误处理函数
    this.props.onError?.(error, errorInfo);

    // 错误上报 (如果有错误监控系统)
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 这里可以集成错误监控系统，比如 Sentry
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 侧边栏错误详情');
      console.error('错误:', error);
      console.error('组件栈:', errorInfo.componentStack);
      console.error('错误栈:', error.stack);
      console.groupEnd();
    }

    // 可以在这里发送错误报告到监控服务
    // 例如：errorReportingService.report(error, errorInfo);
  };

  private handleRetry = () => {
    const { retryCount } = this.state;
    
    if (retryCount >= MAX_RETRY_COUNT) {
      console.warn('⚠️ 侧边栏重试次数已达上限，停止重试');
      return;
    }

    console.log(`🔄 尝试恢复侧边栏组件 (第 ${retryCount + 1} 次)`);
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: retryCount + 1,
    });

    // 延迟重试，避免立即再次失败
    this.retryTimeoutId = setTimeout(() => {
      // 如果重试后仍然有错误，这个组件会再次被 componentDidCatch 捕获
    }, 1000);
  };

  private handleReload = () => {
    // 刷新整个页面作为最后的降级方案
    window.location.reload();
  };

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  render() {
    const { hasError, error, retryCount } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      // 如果提供了自定义降级UI，使用它
      if (fallback) {
        return fallback;
      }

      // 根据重试次数显示不同的错误UI
      const canRetry = retryCount < MAX_RETRY_COUNT;
      const isHighRetryCount = retryCount >= 2;

      return (
        <div className="sidebar-error-boundary" style={{ 
          padding: '20px', 
          textAlign: 'center',
          background: 'var(--sidebar-bg, #fff)',
          border: '1px solid var(--border-color, #d9d9d9)',
          borderRadius: '6px',
          minHeight: '200px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <Result
            icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            title="侧边栏暂时不可用"
            subTitle={
              isHighRetryCount 
                ? "侧边栏遇到了持续性问题，请刷新页面重试" 
                : "侧边栏组件发生了错误，请尝试重新加载"
            }
            extra={[
              canRetry && (
                <Button 
                  key="retry" 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={this.handleRetry}
                >
                  重试
                </Button>
              ),
              <Button 
                key="reload" 
                type={isHighRetryCount ? "primary" : "default"}
                onClick={this.handleReload}
              >
                刷新页面
              </Button>,
            ].filter(Boolean)}
          />
          
          {process.env.NODE_ENV === 'development' && error && (
            <details style={{ 
              marginTop: '20px', 
              textAlign: 'left',
              fontSize: '12px',
              maxWidth: '100%',
              overflow: 'auto',
            }}>
              <summary style={{ cursor: 'pointer', color: '#666' }}>
                查看错误详情 (开发模式)
              </summary>
              <pre style={{ 
                marginTop: '10px',
                padding: '10px',
                background: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '11px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
              }}>
                {error.message}
                {'\n\n'}
                {error.stack}
              </pre>
            </details>
          )}
        </div>
      );
    }

    return children;
  }
}

/**
 * 简化的侧边栏降级组件
 * 当主侧边栏完全失效时使用
 */
export const FallbackSidebar: React.FC<{
  menuItems: any[];
  onMenuClick?: (key: string, path?: string) => void;
}> = ({ menuItems, onMenuClick }) => {
  const handleClick = (item: any) => {
    if (item.path && onMenuClick) {
      onMenuClick(item.key, item.path);
    }
  };

  return (
    <div className="fallback-sidebar" style={{
      width: '240px',
      background: '#fff',
      borderRight: '1px solid #d9d9d9',
      padding: '16px 0',
      height: '100vh',
      overflow: 'auto',
    }}>
      <div style={{ 
        padding: '0 16px 16px', 
        borderBottom: '1px solid #f0f0f0',
        marginBottom: '16px',
      }}>
        <h3 style={{ margin: 0, color: '#666' }}>导航菜单</h3>
        <p style={{ margin: '4px 0 0', fontSize: '12px', color: '#999' }}>
          简化模式
        </p>
      </div>
      
      {menuItems.map((item) => (
        <div
          key={item.key}
          style={{
            padding: '8px 16px',
            cursor: item.path ? 'pointer' : 'default',
            borderLeft: '3px solid transparent',
          }}
          onMouseEnter={(e) => {
            if (item.path) {
              (e.target as HTMLElement).style.borderLeftColor = '#1890ff';
              (e.target as HTMLElement).style.backgroundColor = '#f0f9ff';
            }
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.borderLeftColor = 'transparent';
            (e.target as HTMLElement).style.backgroundColor = 'transparent';
          }}
          onClick={() => handleClick(item)}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {item.icon && (
              <span style={{ marginRight: '8px' }}>{item.icon}</span>
            )}
            <span>{item.label}</span>
          </div>
          
          {item.children && item.children.length > 0 && (
            <div style={{ marginLeft: '24px', marginTop: '8px' }}>
              {item.children.map((child: any) => (
                <div
                  key={child.key}
                  style={{
                    padding: '4px 8px',
                    fontSize: '14px',
                    color: '#666',
                    cursor: child.path ? 'pointer' : 'default',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClick(child);
                  }}
                >
                  {child.label}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default SidebarErrorBoundary;