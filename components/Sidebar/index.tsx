import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Menu, Drawer } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { SidebarProps, MenuItem } from '../../types';
import { getMenuStateByPath } from '../../utils/menuData';
import { useResponsive } from '../../hooks/useResponsiveListener';
import { useStore } from '../../stores';
import { hoverIntentService, initializeHoverIntent } from '../../utils/hoverIntentService';
import { startPerformanceTimer, endPerformanceTimer } from '../../utils/performanceMonitor';
import SubMenuPopover from './SubMenuPopover';
import { SidebarErrorBoundary, FallbackSidebar } from './ErrorBoundary';
import './style.scss';
import './SubMenuPopover.scss';

/**
 * 侧边栏组件 - 重构版本
 * 集成 Zustand 状态管理、ForesightJS 悬浮检测、优化性能
 * 支持响应式布局、主题切换、菜单折叠等功能
 */
const Sidebar: React.FC<SidebarProps> = ({
  collapsed = false,
  onCollapse,
  menuItems = [],
  selectedKey,
  onMenuSelect,
  theme = 'light',
  width = 240,
  collapsedWidth = 80,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile } = useResponsive();
  
  // 从 Zustand store 获取状态
  const {
    subMenuPopover,
    showSubMenuPopover,
    hideSubMenuPopover,
    setSubMenuHovered,
    clearSubMenuState,
  } = useStore();

  // 菜单状态 (仍然使用本地状态，因为这些是特定于组件的状态)
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [activeParentKeys, setActiveParentKeys] = useState<string[]>([]);

  const sidebarRef = useRef<HTMLDivElement>(null);
  const hoverCleanupRef = useRef<(() => void)[]>([]);

  // 初始化悬浮检测服务和清理
  useEffect(() => {
    // 初始化 hoverIntentService
    initializeHoverIntent().catch(console.warn);
    
    return () => {
      // 清理所有悬浮检测
      hoverCleanupRef.current.forEach(cleanup => cleanup());
      hoverCleanupRef.current = [];
      
      // 清理子菜单状态
      clearSubMenuState();
    };
  }, [clearSubMenuState]);

  // 🔧 修复子菜单状态：处理侧边栏折叠时强制关闭所有展开的子菜单
  useEffect(() => {
    if (collapsed) {
      // 立即清空所有展开的子菜单状态
      setOpenKeys([]);

      // 强制关闭任何可能的弹出菜单
      hideSubMenuPopover();

      // 清理所有悬浮检测
      hoverCleanupRef.current.forEach(cleanup => cleanup());
      hoverCleanupRef.current = [];

      // 🔧 强制重置Ant Design Menu组件的内部状态
      // 通过短暂的延迟确保DOM更新完成
      setTimeout(() => {
        const menuElement = document.querySelector('.sidebar-menu-component');
        if (menuElement) {
          // 移除所有展开的子菜单类名
          const expandedItems = menuElement.querySelectorAll('.ant-menu-submenu-open');
          expandedItems.forEach(item => {
            item.classList.remove('ant-menu-submenu-open');
          });
        }
      }, 0);
    }
  }, [collapsed, hideSubMenuPopover]);

  // 根据当前路径更新菜单状态 - 完整的状态管理
  useEffect(() => {
    const menuState = getMenuStateByPath(location.pathname, collapsed);

    // 使用外部传入的selectedKey或计算出的selectedKeys
    setSelectedKeys(selectedKey ? [selectedKey] : menuState.selectedKeys);
    setActiveParentKeys(menuState.activeParentKeys);

    // 只在非折叠状态下设置展开的keys
    if (!collapsed) {
      setOpenKeys(menuState.openKeys);
    } else {
      setOpenKeys([]);
    }
  }, [location.pathname, selectedKey, collapsed]);

  // 查找菜单项的辅助函数 - 复制index_new.tsx
  const findMenuItem = (items: MenuItem[], key: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === key) {
        return item;
      }
      if (item.children) {
        const found = findMenuItem(item.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  // 🔧 修复菜单点击逻辑 - 只有叶子节点才能被选中和导航，集成性能监控
  const handleMenuClick = ({ key }: { key: string }) => {
    // 开始性能监控
    startPerformanceTimer(`menu-click-${key}`, 'click', { menuKey: key });

    const menuItem = findMenuItem(menuItems, key);

    // 🔧 检查是否为叶子节点（没有子菜单的菜单项）
    const hasChildren = menuItem?.children && menuItem.children.length > 0;

    if (menuItem?.path && !hasChildren) {
      // 只有叶子节点才进行导航和选中
      navigate(menuItem.path);
      onMenuSelect?.(key, menuItem.path);
      setSelectedKeys([key]); // 手动设置选中状态
    } else if (hasChildren && !collapsed) {
      // 🔧 父菜单项只在展开状态下处理展开/收起，收缩状态下由HeaderPopover处理
      const isOpen = openKeys.includes(key);
      if (isOpen) {
        setOpenKeys(openKeys.filter(k => k !== key));
      } else {
        setOpenKeys([...openKeys, key]);
      }
    }

    // 结束性能监控
    endPerformanceTimer(`menu-click-${key}`, 'click');
  };

  // 处理子菜单展开/收起 - 完全复制index_new.tsx逻辑
  const handleOpenChange = (keys: string[]) => {
    // 在折叠状态下不允许展开子菜单
    if (collapsed && !isMobile) {
      setOpenKeys([]);
      return;
    }

    // 展开状态下允许正常的子菜单展开/收缩
    setOpenKeys(keys);
  };

  // 处理弹出菜单的菜单项点击
  const handlePopoverMenuClick = (key: string, path?: string) => {
    if (path) {
      navigate(path);
      onMenuSelect?.(key, path);
      hideSubMenuPopover(); // 关闭弹出菜单

      // 🔧 状态更新将由useEffect处理，基于新的路径
      // 这样确保状态逻辑的一致性
    }
  };



  // 处理子菜单标题点击（用于折叠状态）
  const handleSubMenuTitleClick = (key: string, event: React.MouseEvent) => {
    if (collapsed && !isMobile) {
      event.preventDefault();
      event.stopPropagation();

      const menuItem = findMenuItem(menuItems, key);
      if (menuItem?.children && menuItem.children.length > 0) {
        // 使用 Zustand 状态管理的 SubMenuPopover
        const target = event.currentTarget as HTMLElement;
        if (target && sidebarRef.current) {
          const sidebarRect = sidebarRef.current.getBoundingClientRect();
          const targetRect = target.getBoundingClientRect();

          const position = {
            top: targetRect.top + (targetRect.height / 2) - 20,
            left: sidebarRect.right + 8,
          };

          showSubMenuPopover(menuItem, position, key);
        }
      }
    }
  };

  // 优化的鼠标离开处理 - 使用防抖机制
  const handleMouseLeave = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        if (subMenuPopover.visible && collapsed && !isMobile) {
          // 检查鼠标是否在弹出菜单上
          const popoverElement = document.querySelector('.submenu-popover');
          if (popoverElement && popoverElement.matches(':hover')) {
            return; // 如果鼠标在弹出菜单上，不关闭
          }
          hideSubMenuPopover();
        }
      }, 300);
    };
  }, [subMenuPopover.visible, collapsed, isMobile, hideSubMenuPopover]);

  // 添加智能悬浮事件监听器处理子菜单显示
  useEffect(() => {
    if (!collapsed || isMobile) {
      // 清理现有的悬浮检测
      hoverCleanupRef.current.forEach(cleanup => cleanup());
      hoverCleanupRef.current = [];
      return;
    }

    const menuContainer = document.querySelector('.sidebar-menu-component');
    if (!menuContainer) return;

    // 为每个菜单项注册悬浮检测
    const menuElements = menuContainer.querySelectorAll('.ant-menu-submenu, .ant-menu-item');
    const cleanupFunctions: (() => void)[] = [];

    menuElements.forEach((menuElement, index) => {
      if (index < menuItems.length) {
        const menuItemData = menuItems[index];
        
        if (menuItemData?.children && menuItemData.children.length > 0) {
          // 有子菜单的项目：注册智能悬浮检测
          const cleanup = hoverIntentService.register(
            menuElement as HTMLElement,
            // 鼠标悬浮：显示子菜单
            () => {
              handleSubmenuItemHover(menuItemData.key, menuItemData, menuElement as HTMLElement);
            },
            // 鼠标离开：设置悬浮状态
            () => {
              setSubMenuHovered(null);
            }
          );
          cleanupFunctions.push(cleanup);
        } else {
          // 无子菜单项目：注册简单悬浮检测来关闭弹出菜单
          const cleanup = hoverIntentService.register(
            menuElement as HTMLElement,
            // 鼠标悬浮：关闭弹出菜单
            () => {
              hideSubMenuPopover();
            }
          );
          cleanupFunctions.push(cleanup);
        }
      }
    });

    hoverCleanupRef.current = cleanupFunctions;

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [collapsed, isMobile, menuItems, setSubMenuHovered, hideSubMenuPopover]);

  // 处理子菜单项悬停 - 优化版本，集成性能监控
  const handleSubmenuItemHover = (key: string, menuItem: MenuItem, target: HTMLElement) => {
    if (collapsed && !isMobile && menuItem.children && menuItem.children.length > 0) {
      if (target && sidebarRef.current) {
        // 开始性能监控
        startPerformanceTimer(`submenu-hover-${key}`, 'hover', {
          menuKey: key,
          childCount: menuItem.children.length,
        });

        const sidebarRect = sidebarRef.current.getBoundingClientRect();
        const targetRect = target.getBoundingClientRect();

        const position = {
          top: targetRect.top + (targetRect.height / 2) - 20,
          left: sidebarRect.right + 8,
        };

        // 使用 Zustand 状态管理
        console.log('🔍 调试：准备显示子菜单', { key, menuItem, position });
        showSubMenuPopover(menuItem, position, key);
        console.log('🔍 调试：已调用showSubMenuPopover');

        // 结束性能监控
        endPerformanceTimer(`submenu-hover-${key}`, 'hover');
      }
    }
  };

  // 转换菜单项为Ant Design Menu格式
  const convertMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => {
      const hasChildren = item.children && item.children.length > 0;
      const isActiveParent = activeParentKeys.includes(item.key);

      return {
        key: item.key,
        icon: item.icon,
        className: isActiveParent ? 'sidebar-menu-item-active-parent' : undefined,
        label: hasChildren && collapsed && !isMobile ? (
          // 在收缩状态下，为有子菜单的项目添加事件处理
          <span
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleSubMenuTitleClick(item.key, e as any);
            }}
            style={{ cursor: 'pointer', width: '100%', display: 'block' }}
          >
            {item.label}
          </span>
        ) : item.label,
        children: hasChildren ? convertMenuItems(item.children!) : undefined,
      };
    });
  };



  // 转换菜单项为 Ant Design Menu 格式
  const antdMenuItems = useMemo(() => {
    return convertMenuItems(menuItems);
  }, [menuItems, collapsed, isMobile, activeParentKeys]);

  // 侧边栏内容
  const sidebarContent = (
    <div
      ref={sidebarRef}
      className={`v2-sidebar ${theme} ${collapsed && !isMobile ? 'collapsed hide-text' : ''}`}
      onMouseLeave={handleMouseLeave}
    >
      {/* Logo区域 */}
      <div className="sidebar-logo">
        <div className="logo-content">
          {collapsed && !isMobile ? (
            <span className="logo-icon">A</span>
          ) : (
            <>
              <span className="logo-icon">A</span>
              <span className="logo-text">Admin V2</span>
            </>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="sidebar-menu">
        <Menu
          key={`menu-${collapsed ? 'collapsed' : 'expanded'}`}
          mode="inline"
          theme={theme}
          selectedKeys={selectedKeys}
          openKeys={collapsed && !isMobile ? [] : openKeys}
          onOpenChange={handleOpenChange}
          onClick={handleMenuClick}
          inlineCollapsed={collapsed && !isMobile}
          className="sidebar-menu-component"
          style={{
            width: collapsed && !isMobile ? collapsedWidth : width,
            borderRight: 'none',
          }}
          items={antdMenuItems}
          forceSubMenuRender={false}
        />
      </div>

      {/* 底部区域 */}
      <div className="sidebar-footer">
        {(!collapsed || isMobile) && (
          <div className="footer-content">
            <span className="version-info">V2.0.0</span>
          </div>
        )}
      </div>
    </div>
  );

  // 移动端使用Drawer
  if (isMobile) {
    return (
      <SidebarErrorBoundary
        fallback={<FallbackSidebar menuItems={menuItems} onMenuClick={handlePopoverMenuClick} />}
        onError={(error, errorInfo) => {
          console.error('📱 移动端侧边栏错误:', error, errorInfo);
        }}
      >
        <Drawer
          placement="left"
          closable={false}
          open={!collapsed}
          onClose={() => onCollapse?.(true)}
          styles={{ body: { padding: 0 } }}
          width={width}
          className="v2-sidebar-drawer"
          zIndex={1050}
        >
          {sidebarContent}
        </Drawer>
      </SidebarErrorBoundary>
    );
  }

  // 桌面端flexbox侧边栏
  return (
    <SidebarErrorBoundary
      fallback={<FallbackSidebar menuItems={menuItems} onMenuClick={handlePopoverMenuClick} />}
      onError={(error, errorInfo) => {
        console.error('🖥️ 桌面端侧边栏错误:', error, errorInfo);
      }}
    >
      <div
        className={`v2-sidebar-wrapper ${collapsed ? 'collapsed' : 'expanded'}`}
        onMouseLeave={handleMouseLeave}
      >
        {sidebarContent}
      </div>

      {/* 子菜单弹出组件 - 更新接口 */}
      {(() => {
        console.log('🔍 调试：SubMenuPopover状态', {
          visible: subMenuPopover.visible,
          menuItem: subMenuPopover.menuItem,
          position: subMenuPopover.position
        });
        return null;
      })()}
      <SubMenuPopover
        visible={subMenuPopover.visible}
        onClose={hideSubMenuPopover}
        menuItem={subMenuPopover.menuItem}
        position={subMenuPopover.position}
        onMenuClick={handlePopoverMenuClick}
        theme={theme}
        width={200}
      />
    </SidebarErrorBoundary>
  );
};

export default Sidebar;
