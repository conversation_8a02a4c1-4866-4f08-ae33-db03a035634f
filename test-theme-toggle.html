<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题切换按钮测试</title>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            transition: all 0.3s ease;
        }

        /* 亮色主题 */
        body {
            background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
            color: #333;
        }

        /* 暗色主题 */
        body[data-theme="dark"] {
            background: linear-gradient(135deg, #0f1419 0%, #1a1a1a 100%);
            color: #fff;
        }

        /* 容器样式 */
        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .content {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        body[data-theme="dark"] .content {
            background: rgba(31, 31, 31, 0.9);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        /* 主题切换按钮样式 */
        .login-theme-toggle {
            position: absolute;
            top: 24px;
            right: 24px;
            z-index: 100;
            backdrop-filter: blur(8px);
            transition: all 0.3s ease;
            border-radius: 6px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            font-size: 18px;

            /* 亮色主题样式（默认） */
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #1890ff;
        }

        .login-theme-toggle:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        /* 暗色主题下的主题切换按钮覆盖样式 */
        body[data-theme="dark"] .login-theme-toggle {
            background: rgba(31, 31, 31, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #40a9ff;
        }

        body[data-theme="dark"] .login-theme-toggle:hover {
            background: rgba(31, 31, 31, 1);
            border-color: #40a9ff;
            box-shadow: 0 4px 12px rgba(64, 169, 255, 0.25);
        }

        /* 响应式适配 */
        @media (max-width: 768px) {
            .login-theme-toggle {
                top: 16px;
                right: 16px;
                width: 36px;
                height: 36px;
                font-size: 16px;
            }
        }

        @media (max-width: 480px) {
            .login-theme-toggle {
                top: 12px;
                right: 12px;
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 8px;
            background: rgba(24, 144, 255, 0.1);
            border: 1px solid rgba(24, 144, 255, 0.3);
        }

        body[data-theme="dark"] .status {
            background: rgba(64, 169, 255, 0.1);
            border: 1px solid rgba(64, 169, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 主题切换按钮 -->
        <button class="login-theme-toggle" onclick="toggleTheme()" title="切换主题">
            <span id="theme-icon">🌙</span>
        </button>

        <div class="content">
            <h1>主题切换按钮测试</h1>
            <p>这是一个测试页面，用于验证主题切换按钮的定位和样式。</p>
            <div class="status">
                <strong>当前主题：</strong> <span id="current-theme">亮色</span>
            </div>
            <div class="status" style="margin-top: 10px;">
                <strong>按钮位置：</strong> 右上角固定定位
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');
            const currentThemeText = document.getElementById('current-theme');
            
            if (body.getAttribute('data-theme') === 'dark') {
                body.removeAttribute('data-theme');
                themeIcon.textContent = '🌙';
                currentThemeText.textContent = '亮色';
            } else {
                body.setAttribute('data-theme', 'dark');
                themeIcon.textContent = '☀️';
                currentThemeText.textContent = '暗色';
            }
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                toggleTheme();
            }
        });
    </script>
</body>
</html>
