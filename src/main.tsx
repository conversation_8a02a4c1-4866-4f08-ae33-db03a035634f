import React from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import { ConfigProvider, App as AntApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import App from './App'
import { logEnvConfig, getEnvConfig } from '../utils/envConfig'
import { initViewportPolyfill } from '../utils/viewportPolyfill' // 导入视口Polyfill
import { startPerformanceReporting } from '../utils/performanceReporter' // 导入性能监控
import '../utils/routeDebug' // 导入路由调试工具
import './index.scss'

// 设置dayjs中文语言
dayjs.locale('zh-cn')

// 初始化动态视口单位支持
initViewportPolyfill()

// 打印环境变量配置（开发环境）
logEnvConfig()

// 获取环境变量配置
const envConfig = getEnvConfig()

// 启动性能监控（开发环境）
if (process.env.NODE_ENV === 'development') {
  startPerformanceReporting(3) // 每3分钟生成一次报告
}

// Antd主题配置 - 支持环境变量
const theme = {
  token: {
    colorPrimary: envConfig.theme.primaryColor,
    borderRadius: envConfig.theme.borderRadius,
    wireframe: false,
  },
  components: {
    Layout: {
      bodyBg: '#f5f5f5',
      headerBg: '#ffffff',
      siderBg: '#ffffff',
    },
    Menu: {
      itemBg: 'transparent',
      subMenuItemBg: 'transparent',
      itemSelectedBg: '#e6f7ff',
      itemHoverBg: '#f5f5f5',
    },
  },
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ConfigProvider
      locale={zhCN}
      theme={theme}
    >
      <AntApp>
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <App />
        </BrowserRouter>
      </AntApp>
    </ConfigProvider>
  </React.StrictMode>,
)
