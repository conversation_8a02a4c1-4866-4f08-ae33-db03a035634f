import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import { App as AntApp } from 'antd'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import App from './App'
import { logEnvConfig, getEnvConfig } from '../utils/envConfig'
import { initViewportPolyfill } from '../utils/viewportPolyfill' // 导入视口Polyfill
import '../utils/routeDebug' // 导入路由调试工具
import './index.scss'

// 设置dayjs中文语言
dayjs.locale('zh-cn')

// 初始化动态视口单位支持
initViewportPolyfill()

// 打印环境变量配置（开发环境）
logEnvConfig()

// 获取环境变量配置
const envConfig = getEnvConfig()

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <AntApp>
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true,
        }}
      >
        <App />
      </BrowserRouter>
    </AntApp>
  </React.StrictMode>,
)
