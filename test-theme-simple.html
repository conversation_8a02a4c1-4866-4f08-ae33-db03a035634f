<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化主题切换测试</title>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            transition: all 0.3s ease;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* 亮色主题（默认跟随系统） */
        body {
            background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
            color: #333;
        }

        /* 暗色主题 */
        body[data-theme="dark"] {
            background: linear-gradient(135deg, #0f1419 0%, #1a1a1a 100%);
            color: #fff;
        }

        /* 容器样式 */
        .container {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }

        body[data-theme="dark"] .container {
            background: rgba(31, 31, 31, 0.9);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
        }

        /* 主题切换按钮样式 */
        .theme-toggle {
            position: absolute;
            top: 24px;
            right: 24px;
            z-index: 100;
            backdrop-filter: blur(8px);
            transition: all 0.3s ease;
            border-radius: 6px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: none;
            font-size: 18px;

            /* 亮色主题样式（默认） */
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #1890ff;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }

        /* 暗色主题下的主题切换按钮 */
        body[data-theme="dark"] .theme-toggle {
            background: rgba(31, 31, 31, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #40a9ff;
        }

        body[data-theme="dark"] .theme-toggle:hover {
            background: rgba(31, 31, 31, 1);
            border-color: #40a9ff;
            box-shadow: 0 4px 12px rgba(64, 169, 255, 0.25);
        }

        /* 移动端隐藏主题切换按钮 */
        @media (max-width: 768px) {
            .theme-toggle {
                display: none !important;
            }
            
            .mobile-notice {
                display: block !important;
            }
        }

        .mobile-notice {
            display: none;
            margin-top: 20px;
            padding: 15px;
            background: rgba(24, 144, 255, 0.1);
            border: 1px solid rgba(24, 144, 255, 0.3);
            border-radius: 8px;
            color: #1890ff;
        }

        body[data-theme="dark"] .mobile-notice {
            background: rgba(64, 169, 255, 0.1);
            border: 1px solid rgba(64, 169, 255, 0.3);
            color: #40a9ff;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(24, 144, 255, 0.1);
            border: 1px solid rgba(24, 144, 255, 0.3);
        }

        body[data-theme="dark"] .status {
            background: rgba(64, 169, 255, 0.1);
            border: 1px solid rgba(64, 169, 255, 0.3);
        }

        .feature-list {
            text-align: left;
            margin-top: 20px;
        }

        .feature-list li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #52c41a;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 主题切换按钮 - 仅桌面端显示 -->
    <button class="theme-toggle" onclick="toggleTheme()" title="切换主题">
        <span id="theme-icon">🌙</span>
    </button>

    <div class="container">
        <h1>简化主题切换系统测试</h1>
        <p>这是一个简化的主题切换系统，主要跟随系统主题偏好。</p>
        
        <div class="status">
            <strong>当前主题：</strong> <span id="current-theme">跟随系统</span><br>
            <strong>系统主题：</strong> <span id="system-theme">检测中...</span><br>
            <strong>屏幕宽度：</strong> <span id="screen-width">-</span>px
        </div>

        <div class="feature-list">
            <h3>功能特性：</h3>
            <ul>
                <li>自动检测并跟随系统主题偏好</li>
                <li>桌面端提供手动切换按钮（临时切换）</li>
                <li>移动端完全隐藏切换按钮，依赖系统自动切换</li>
                <li>不保存用户偏好，每次刷新都重新跟随系统</li>
                <li>响应式设计，适配各种屏幕尺寸</li>
            </ul>
        </div>

        <div class="mobile-notice">
            📱 移动端模式：主题切换按钮已隐藏，请使用系统设置切换主题
        </div>
    </div>

    <script>
        let currentTheme = 'system';

        // 检测系统主题
        function getSystemTheme() {
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }

        // 应用主题
        function applyTheme(theme) {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');
            const currentThemeText = document.getElementById('current-theme');
            
            if (theme === 'dark') {
                body.setAttribute('data-theme', 'dark');
                themeIcon.textContent = '☀️';
                currentThemeText.textContent = '暗色（手动）';
            } else {
                body.removeAttribute('data-theme');
                themeIcon.textContent = '🌙';
                currentThemeText.textContent = theme === 'system' ? '跟随系统' : '亮色（手动）';
            }
        }

        // 切换主题（仅桌面端）
        function toggleTheme() {
            if (window.innerWidth <= 768) return; // 移动端不允许手动切换
            
            const systemTheme = getSystemTheme();
            
            if (currentTheme === 'system' || currentTheme === systemTheme) {
                // 当前跟随系统或与系统相同，切换到相反主题
                currentTheme = systemTheme === 'dark' ? 'light' : 'dark';
            } else {
                // 当前是手动设置，回到跟随系统
                currentTheme = 'system';
            }
            
            const actualTheme = currentTheme === 'system' ? systemTheme : currentTheme;
            applyTheme(actualTheme);
        }

        // 更新状态显示
        function updateStatus() {
            const systemTheme = getSystemTheme();
            const screenWidth = window.innerWidth;
            
            document.getElementById('system-theme').textContent = systemTheme === 'dark' ? '暗色' : '亮色';
            document.getElementById('screen-width').textContent = screenWidth;
        }

        // 初始化
        function initialize() {
            const systemTheme = getSystemTheme();
            currentTheme = 'system';
            applyTheme(systemTheme);
            updateStatus();

            // 监听系统主题变化
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                const newSystemTheme = e.matches ? 'dark' : 'light';
                
                // 如果当前跟随系统，自动更新
                if (currentTheme === 'system') {
                    applyTheme(newSystemTheme);
                }
                updateStatus();
            });

            // 监听窗口大小变化
            window.addEventListener('resize', updateStatus);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initialize);

        // 键盘快捷键支持（仅桌面端）
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'T' && window.innerWidth > 768) {
                e.preventDefault();
                toggleTheme();
            }
        });
    </script>
</body>
</html>
