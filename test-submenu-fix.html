<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子菜单弹出层修复测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f0f2f5;
            height: 100vh;
            display: flex;
        }

        /* 模拟侧边栏 */
        .sidebar {
            width: 64px;
            background: #001529;
            height: 100vh;
            position: relative;
            transition: all 0.3s;
        }

        .menu-item {
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.65);
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #1890ff;
        }

        .menu-item.has-submenu::after {
            content: '▶';
            position: absolute;
            right: 8px;
            font-size: 8px;
        }

        /* 弹出层样式 */
        .submenu-popover {
            position: fixed;
            background: white;
            border-radius: 6px;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
            border: 1px solid #d9d9d9;
            min-width: 160px;
            z-index: 9999;
            opacity: 0;
            transform: translateX(-8px) scale(0.95);
            transition: all 0.2s ease-out;
            pointer-events: none;
        }

        .submenu-popover.visible {
            opacity: 1;
            transform: translateX(0) scale(1);
            pointer-events: auto;
        }

        .submenu-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background 0.2s;
            border-bottom: 1px solid #f0f0f0;
        }

        .submenu-item:last-child {
            border-bottom: none;
        }

        .submenu-item:hover {
            background: #f5f5f5;
            color: #1890ff;
        }

        /* 状态指示器 */
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-size: 14px;
        }

        .status-item {
            margin: 4px 0;
        }

        .status-item.active {
            color: #52c41a;
            font-weight: bold;
        }

        .content {
            flex: 1;
            padding: 20px;
        }

        .test-instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .test-instructions h2 {
            color: #1890ff;
            margin-bottom: 16px;
        }

        .test-instructions ol {
            margin-left: 20px;
        }

        .test-instructions li {
            margin: 8px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="sidebar" id="sidebar">
        <div class="menu-item has-submenu" data-key="dashboard">📊</div>
        <div class="menu-item has-submenu" data-key="users">👥</div>
        <div class="menu-item has-submenu" data-key="settings">⚙️</div>
        <div class="menu-item" data-key="help">❓</div>
    </div>

    <div class="submenu-popover" id="popover">
        <div class="submenu-item">工作台</div>
        <div class="submenu-item">分析页</div>
        <div class="submenu-item">监控页</div>
    </div>

    <div class="content">
        <div class="test-instructions">
            <h2>🔧 子菜单弹出层修复测试</h2>
            <p><strong>测试目标：</strong>验证鼠标从侧边栏菜单项移动到弹出层时不会意外关闭</p>
            
            <h3>测试步骤：</h3>
            <ol>
                <li>将鼠标悬浮在左侧侧边栏的菜单项上（📊、👥、⚙️）</li>
                <li>观察弹出层是否正常显示</li>
                <li><strong>关键测试：</strong>缓慢将鼠标从菜单项移动到弹出层内的选项</li>
                <li>验证弹出层在移动过程中是否保持显示</li>
                <li>测试快速移动和慢速移动两种情况</li>
            </ol>

            <h3>预期结果：</h3>
            <ul>
                <li>✅ 鼠标悬浮时弹出层正常显示</li>
                <li>✅ 鼠标移动到弹出层时不会关闭</li>
                <li>✅ 只有完全离开相关区域时才关闭</li>
                <li>✅ 支持各种移动速度</li>
            </ul>
        </div>
    </div>

    <div class="status">
        <div class="status-item" id="hover-status">悬浮状态: 无</div>
        <div class="status-item" id="popover-status">弹出层: 隐藏</div>
        <div class="status-item" id="timer-status">定时器: 无</div>
        <div class="status-item" id="mouse-position">鼠标位置: -</div>
    </div>

    <script>
        const sidebar = document.getElementById('sidebar');
        const popover = document.getElementById('popover');
        const menuItems = document.querySelectorAll('.menu-item.has-submenu');
        
        let closeTimer = null;
        let currentHoverItem = null;

        // 状态更新函数
        function updateStatus() {
            document.getElementById('hover-status').textContent = `悬浮状态: ${currentHoverItem || '无'}`;
            document.getElementById('popover-status').textContent = `弹出层: ${popover.classList.contains('visible') ? '显示' : '隐藏'}`;
            document.getElementById('timer-status').textContent = `定时器: ${closeTimer ? '活动' : '无'}`;
        }

        // 显示弹出层
        function showPopover(rect) {
            popover.style.left = (rect.right + 8) + 'px';
            popover.style.top = (rect.top + rect.height / 2 - 40) + 'px';
            popover.classList.add('visible');
            updateStatus();
        }

        // 隐藏弹出层
        function hidePopover() {
            popover.classList.remove('visible');
            currentHoverItem = null;
            updateStatus();
        }

        // 清除定时器
        function clearCloseTimer() {
            if (closeTimer) {
                clearTimeout(closeTimer);
                closeTimer = null;
                updateStatus();
            }
        }

        // 设置关闭定时器
        function setCloseTimer(delay = 150) {
            clearCloseTimer();
            closeTimer = setTimeout(() => {
                hidePopover();
                closeTimer = null;
                updateStatus();
            }, delay);
            updateStatus();
        }

        // 菜单项事件
        menuItems.forEach(item => {
            item.addEventListener('mouseenter', (e) => {
                clearCloseTimer();
                currentHoverItem = item.getAttribute('data-key');
                showPopover(item.getBoundingClientRect());
            });

            item.addEventListener('mouseleave', (e) => {
                const relatedTarget = e.relatedTarget;
                
                // 检查是否移向弹出层
                if (popover.contains(relatedTarget) || popover === relatedTarget) {
                    return; // 不关闭
                }
                
                setCloseTimer();
            });
        });

        // 弹出层事件
        popover.addEventListener('mouseenter', () => {
            clearCloseTimer();
        });

        popover.addEventListener('mouseleave', (e) => {
            const relatedTarget = e.relatedTarget;
            
            // 检查是否移向侧边栏
            if (sidebar.contains(relatedTarget) || sidebar === relatedTarget) {
                return; // 不关闭
            }
            
            setCloseTimer();
        });

        // 鼠标位置跟踪
        document.addEventListener('mousemove', (e) => {
            document.getElementById('mouse-position').textContent = 
                `鼠标位置: (${e.clientX}, ${e.clientY})`;
        });

        // 初始状态
        updateStatus();
    </script>
</body>
</html>
