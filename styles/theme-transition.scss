// ===== 主题切换动画样式 =====

// 禁用过渡效果 - 避免与 View Transition 冲突
html[disabled-transition] *,
html[disabled-transition] *::before,
html[disabled-transition] *::after {
  transition: none !important;
  animation: none !important;
}

// View Transition 动画样式
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

// 统一层级控制 - new 内容始终在上层
::view-transition-old(root) {
  z-index: 1;
}

::view-transition-new(root) {
  z-index: 9999;
}

// 主题切换按钮动画
.theme-toggle-button {
  transition: opacity 0.2s ease, background-color 0.2s ease;
  
  // 确保图标没有动画
  .anticon {
    transition: none !important;
  }
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0) scale(0.98);
  }
  
  // 禁用状态
  &:disabled,
  &.animating {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

// 用户偏好：减少动画
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  ::view-transition-old(root),
  ::view-transition-new(root) {
    animation: none !important;
  }
  
  .theme-toggle-button {
    transition: none !important;
    
    &:hover,
    &:active {
      transform: none !important;
    }
  }
}