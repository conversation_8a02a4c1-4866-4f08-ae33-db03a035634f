import React, { useEffect } from 'react';
import { Layout as AntLayout, Modal } from 'antd';
import { useLocation, Outlet } from 'react-router-dom';
import { LayoutProps } from '../../types';
import { useLayoutStore, useThemeStore } from '../../stores';
import { useResponsive } from '../../hooks/useResponsiveListener';
import SiderLayout from '../../components/Sidebar/SiderLayout';
import HeaderWithoutTrigger from '../../components/Header/HeaderWithoutTrigger';
import DynamicBreadcrumb from '../../components/DynamicBreadcrumb';
import menuData, { getMenuStateByPath } from '../../utils/menuData';
import authUtils from '../../utils/auth';
import { authAPI } from '../../services/api';
import './style.scss';

const { Content } = AntLayout;

/**
 * 使用 Ant Design 官方 Layout.Sider 的布局组件
 * 提供完整的管理后台布局结构，支持内置的折叠功能和主题切换
 */
const SiderLayoutComponent: React.FC<LayoutProps> = ({
  children,
  sidebarProps = {},
  headerProps = {},
  className = '',
  style = {},
}) => {
  const location = useLocation();
  const { isMobile } = useResponsive();
  const {
    sidebarCollapsed,
    setSidebarCollapsed,
  } = useLayoutStore();

  const { theme } = useThemeStore();

  // 获取当前用户数据
  const currentUser = authUtils.getCurrentUser();
  const user = currentUser ? {
    id: currentUser.userId || '1',
    username: currentUser.username || 'Admin',
    email: '<EMAIL>',
    role: currentUser.role || 'admin',
    avatar: currentUser.avatar || '',
    isSuperAdmin: currentUser.superAdmin || false,
  } : {
    id: '1',
    username: 'Admin',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '',
    isSuperAdmin: false,
  };

  // 面包屑配置
  const breadcrumbConfig = {
    showIcon: true,
    maxItems: 4,
    separator: '/',
    routes: menuData,
  };

  // 处理菜单选择
  const handleMenuSelect = (key: string) => {
    console.log('菜单选择:', key);
  };

  // 处理用户菜单点击
  const handleUserMenuClick = (action: string) => {
    switch (action) {
      case 'profile':
        console.log('查看个人资料');
        break;
      case 'settings':
        console.log('打开设置');
        break;
      case 'logout':
        Modal.confirm({
          title: '确认退出',
          content: '您确定要退出登录吗？',
          onOk: async () => {
            try {
              await authAPI.logout();
              authUtils.clearAuth();
              window.location.href = '/login';
            } catch (error) {
              console.error('退出登录失败:', error);
            }
          },
        });
        break;
      default:
        console.log('未知操作:', action);
    }
  };

  // 监听路由变化，在移动端自动收起侧边栏
  useEffect(() => {
    if (isMobile) {
      setSidebarCollapsed(true);
    }
  }, [location.pathname, isMobile, setSidebarCollapsed]);

  return (
    <AntLayout
      className={`v2-sider-layout-container ${theme} ${className}`}
      style={style}
      data-theme={theme}
    >
      {/* 侧边栏 - 使用官方 Layout.Sider */}
      <SiderLayout
        collapsed={sidebarCollapsed}
        onCollapse={setSidebarCollapsed}
        menuItems={menuData}
        onMenuSelect={handleMenuSelect}
        theme={theme}
        width={240}
        collapsedWidth={80}
        {...sidebarProps}
      />

      {/* 主要内容区域 */}
      <AntLayout className="layout-main">
        {/* 顶部导航栏 - 不包含折叠按钮 */}
        <HeaderWithoutTrigger
          user={user}
          onUserMenuClick={handleUserMenuClick}
          showBreadcrumb={!isMobile}
          breadcrumbComponent={<DynamicBreadcrumb config={breadcrumbConfig} />}
          {...headerProps}
        />

        {/* 内容区域 */}
        <Content className="layout-content">
          <div className="content-wrapper">
            {children || <Outlet />}
          </div>
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default SiderLayoutComponent;
