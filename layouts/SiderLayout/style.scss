@use '../../styles/variables' as *;

// 使用 Ant Design 官方 Layout.Sider 的布局样式
.v2-sider-layout-container {
  min-height: 100vh;
  background: var(--theme-bg-primary);

  // 主要内容区域
  .layout-main {
    background: var(--theme-bg-primary);
    transition: margin-left 0.2s ease;

    // 内容区域
    .layout-content {
      margin: 0;
      padding: 0;
      background: var(--theme-bg-primary);
      min-height: calc(100vh - 64px); // 减去 Header 高度
      overflow: auto;

      .content-wrapper {
        padding: $spacing-lg;
        min-height: 100%;
        background: var(--theme-bg-primary);

        // 移动端内边距优化
        @media (max-width: $breakpoint-sm) {
          padding: $spacing-md;
        }
      }
    }
  }

  // 暗色主题适配
  &.dark {
    background: #001529;

    .layout-main {
      background: #001529;

      .layout-content {
        background: #001529;

        .content-wrapper {
          background: #001529;
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: $breakpoint-lg) {
    .layout-main {
      margin-left: 0 !important;
    }
  }
}

// Header 样式调整（移除折叠按钮后的布局优化）
.v2-sider-layout-container {
  .v2-header-container {
    .v2-header {
      .header-left {
        // 移除折叠按钮后，调整左侧布局
        .header-content {
          margin-left: 0;
        }
      }
    }
  }
}

// 确保 Sider 在移动端的正确显示
@media (max-width: $breakpoint-lg) {
  .v2-sider-layout-container {
    .v2-sider-layout {
      position: fixed !important;
      z-index: 1000;
      height: 100vh;
      left: 0;
      top: 0;
    }

    .layout-main {
      margin-left: 0;
    }
  }
}

// 动画效果
.v2-sider-layout-container {
  .v2-sider-layout,
  .layout-main {
    transition: all 0.2s ease;
  }
}

// 确保内容区域不被 Sider 遮挡
.v2-sider-layout-container {
  &:not(.ant-layout-has-sider) {
    .layout-main {
      margin-left: 0;
    }
  }
}

// 修复可能的样式冲突
.v2-sider-layout-container {
  .ant-layout-sider {
    background: var(--theme-bg-primary) !important;
    
    &.ant-layout-sider-dark {
      background: #001529 !important;
    }
  }

  .ant-layout-content {
    background: var(--theme-bg-primary) !important;
  }
}

// 确保触发器的正确样式
.v2-sider-layout-container {
  .ant-layout-sider-trigger {
    position: relative;
    background: var(--theme-bg-primary) !important;
    color: var(--theme-text-primary) !important;
    border-top: 1px solid var(--theme-border-color-split) !important;
    transition: all 0.2s ease !important;

    &:hover {
      background: var(--theme-bg-secondary) !important;
      color: var(--theme-primary-color) !important;
    }
  }

  &.dark {
    .ant-layout-sider-trigger {
      background: #001529 !important;
      color: rgba(255, 255, 255, 0.85) !important;
      border-top-color: rgba(255, 255, 255, 0.1) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #1890ff !important;
      }
    }
  }
}
