# SCSS 变量到 CSS 变量迁移指南

## 📋 **完整变量映射表**

### 间距变量
```scss
$spacing-xs: 4px;     → var(--theme-spacing-xs)     // 需要添加到 theme.scss
$spacing-sm: 8px;     → var(--theme-spacing-sm)     // 需要添加到 theme.scss
$spacing-md: 16px;    → var(--theme-spacing-md)     ✅ 已定义
$spacing-lg: 24px;    → var(--theme-spacing-lg)     // 需要添加到 theme.scss
$spacing-xl: 32px;    → var(--theme-spacing-xl)     // 需要添加到 theme.scss
```

### 字体大小变量
```scss
$font-size-xs: 11px;  → var(--theme-font-size-xs)   ✅ 已定义
$font-size-sm: 12px;  → var(--theme-font-size-sm)   ✅ 已定义
$font-size-base: 14px;→ var(--theme-font-size-base) ✅ 已定义
$font-size-lg: 16px;  → var(--theme-font-size-lg)   ✅ 已定义
$font-size-xl: 18px;  → var(--theme-font-size-xl)   ✅ 已定义
$font-size-xxl: 20px; → var(--theme-font-size-xxl)  ✅ 已定义
```

### 字体权重变量
```scss
$font-weight-normal: 400;    → var(--theme-font-weight-normal)    ✅ 已定义
$font-weight-medium: 500;    → var(--theme-font-weight-medium)    ✅ 已定义
$font-weight-semibold: 600;  → var(--theme-font-weight-semibold)  ✅ 已定义
$font-weight-bold: 700;      → var(--theme-font-weight-bold)      ✅ 已定义
```

### 圆角变量
```scss
$border-radius-xs: 2px;   → var(--theme-border-radius-xs)   ✅ 已定义
$border-radius-sm: 4px;   → var(--theme-border-radius-sm)   ✅ 已定义
$border-radius-base: 8px; → var(--theme-border-radius-base) ✅ 已定义
$border-radius-md: 6px;   → var(--theme-border-radius-md)   ✅ 已定义
$border-radius-lg: 8px;   → var(--theme-border-radius-lg)   ✅ 已定义
```

### 颜色变量
```scss
$primary-color: #1890ff;        → var(--theme-primary-color)        ✅ 已定义
$primary-color-hover: #40a9ff;  → var(--theme-primary-color-hover)  ✅ 已定义
$primary-color-light: #e6f7ff;  → var(--theme-primary-color-light)  ✅ 已定义
$success-color: #52c41a;        → var(--theme-success-color)        ✅ 已定义
$warning-color: #faad14;        → var(--theme-warning-color)        ✅ 已定义
$error-color: #ff4d4f;          → var(--theme-error-color)          ✅ 已定义
$info-color: #1890ff;           → var(--theme-info-color)           ✅ 已定义

$text-color: rgba(0, 0, 0, 0.85);           → var(--theme-text-primary)
$text-color-secondary: rgba(0, 0, 0, 0.45); → var(--theme-text-secondary)
$text-color-tertiary: rgba(0, 0, 0, 0.25);  → var(--theme-text-tertiary)

$background-color-base: #f0f2f5;    → var(--theme-bg-secondary)
$background-color-light: #fafafa;   → var(--theme-bg-tertiary)

$border-color-base: #d9d9d9;        → var(--theme-border-color)
$border-color-split: #f0f0f0;       → var(--theme-border-color-split)
```

### 阴影变量
```scss
$box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
→ var(--theme-box-shadow-base) ✅ 已定义
```

### 动画变量
```scss
$transition-base: all 0.2s ease; → var(--theme-transition-base) ✅ 已定义
```

### 响应式断点变量
```scss
$breakpoint-xs: 480px;   → var(--theme-breakpoint-xs)   ✅ 已定义
$breakpoint-sm: 768px;   → var(--theme-breakpoint-sm)   ✅ 已定义
$breakpoint-md: 1024px;  → var(--theme-breakpoint-md)   ✅ 已定义
$breakpoint-lg: 1440px;  → var(--theme-breakpoint-lg)   ✅ 已定义
$breakpoint-xl: 1920px;  → var(--theme-breakpoint-xl)   ✅ 已定义
$breakpoint-xxl: 2560px; → var(--theme-breakpoint-xxl)  ✅ 已定义
```

### 布局变量
```scss
$header-height: 48px;         → var(--theme-header-height)         ✅ 已定义
$header-height-mobile: 44px;  → var(--theme-header-height-mobile)  ✅ 已定义
$content-padding: 24px;       → var(--theme-content-padding)       ✅ 已定义
$content-padding-mobile: 16px;→ var(--theme-content-padding-mobile)✅ 已定义
```

### Z-index 变量
```scss
$z-index-sticky: 1020;        → var(--theme-z-index-sticky)        ✅ 已定义
$z-index-fixed: 1030;         → var(--theme-z-index-fixed)         ✅ 已定义
$z-index-modal-backdrop: 1040;→ var(--theme-z-index-modal-backdrop)✅ 已定义
```

## 🔄 **批量替换脚本**

### 需要添加的缺失 CSS 变量

在 `styles/theme.scss` 中添加：

```scss
/* 亮色主题 */
:root {
  /* 间距系统扩展 */
  --theme-spacing-xs: 4px;
  --theme-spacing-sm: 8px;
  --theme-spacing-lg: 24px;
  --theme-spacing-xl: 32px;
}

/* 暗色主题 */
[data-theme='dark'] {
  /* 间距系统扩展 */
  --theme-spacing-xs: 4px;
  --theme-spacing-sm: 8px;
  --theme-spacing-lg: 24px;
  --theme-spacing-xl: 32px;
}
```

## 📋 **待处理文件清单**

### 高优先级（大量 SCSS 变量）
1. **pages/DashboardV2/style.scss** - 32 处变量
2. **pages/AssistantManagement/style.scss** - 估计 20+ 处
3. **components/Sidebar/style.scss** - 估计 25+ 处

### 中优先级
4. **pages/SystemSettings/style.scss** - 估计 15+ 处
5. **components/Header/style.scss** - 估计 15+ 处
6. **pages/UserListManagement/style.scss** - 估计 10+ 处

### 低优先级
7. **components/UserAvatar/style.scss** - 少量变量
8. **components/KnowledgeBaseSelector/style.scss** - 需要检查

## 🎯 **替换策略**

### 1. 自动化替换
可以使用以下正则表达式进行批量替换：

```bash
# 间距变量
s/\$spacing-md/var(--theme-spacing-md)/g
s/\$spacing-lg/var(--theme-spacing-lg)/g

# 字体变量
s/\$font-size-xs/var(--theme-font-size-xs)/g
s/\$font-size-sm/var(--theme-font-size-sm)/g
s/\$font-size-base/var(--theme-font-size-base)/g
s/\$font-size-lg/var(--theme-font-size-lg)/g
s/\$font-size-xl/var(--theme-font-size-xl)/g

# 字体权重
s/\$font-weight-medium/var(--theme-font-weight-medium)/g
s/\$font-weight-semibold/var(--theme-font-weight-semibold)/g
s/\$font-weight-bold/var(--theme-font-weight-bold)/g

# 圆角变量
s/\$border-radius-xs/var(--theme-border-radius-xs)/g
s/\$border-radius-sm/var(--theme-border-radius-sm)/g
s/\$border-radius-base/var(--theme-border-radius-base)/g
s/\$border-radius-md/var(--theme-border-radius-md)/g
s/\$border-radius-lg/var(--theme-border-radius-lg)/g

# 颜色变量
s/\$primary-color-hover/var(--theme-primary-color-hover)/g
s/\$primary-color-light/var(--theme-primary-color-light)/g
s/\$primary-color/var(--theme-primary-color)/g
s/\$success-color/var(--theme-success-color)/g

# 响应式断点
s/\$breakpoint-xs/var(--theme-breakpoint-xs)/g
s/\$breakpoint-sm/var(--theme-breakpoint-sm)/g
s/\$breakpoint-md/var(--theme-breakpoint-md)/g
s/\$breakpoint-lg/var(--theme-breakpoint-lg)/g

# 其他变量
s/\$transition-base/var(--theme-transition-base)/g
s/\$box-shadow-base/var(--theme-box-shadow-base)/g
```

### 2. 手动验证
替换后需要手动验证：
- 确保所有 CSS 变量都已在 theme.scss 中定义
- 测试亮色和暗色主题下的显示效果
- 验证响应式布局是否正常工作

## ✅ **验证清单**

- [ ] 所有 SCSS 变量都已替换为 CSS 变量
- [ ] 所有 CSS 变量都在 theme.scss 中有定义
- [ ] 亮色主题显示正常
- [ ] 暗色主题显示正常
- [ ] 响应式布局正常工作
- [ ] 删除 styles/variables.scss 文件
- [ ] 更新 vite.config.ts 配置
- [ ] 移除所有 @use '../styles/variables' 导入
