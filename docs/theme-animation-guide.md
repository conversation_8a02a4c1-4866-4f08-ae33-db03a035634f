# 主题切换动画功能说明

## 🎉 功能概述

已在现有的主题切换系统基础上添加了炫酷的动画效果！使用了现代浏览器的 **View Transition API** 实现从点击位置向外扩散的圆形过渡动画。

## ✨ 新增特性

### 🎨 动画效果
- **圆形扩散动画**: 从用户点击的位置开始，圆形向外扩散覆盖整个屏幕
- **View Transition API**: 使用浏览器原生的过渡动画技术
- **智能降级**: 不支持 View Transition API 的浏览器自动降级为无动画切换
- **用户偏好**: 自动检测并尊重用户的 `prefers-reduced-motion` 设置

### 🔧 技术实现
- **零破坏性**: 完全基于现有的 `ThemeToggle` 组件实现，不影响现有功能
- **兼容性**: 保持与现有主题系统的完全兼容
- **性能优化**: 动画期间禁用 CSS 过渡以避免冲突

## 🚀 如何体验

1. **打开应用**: 访问 `http://localhost:3006`
2. **找到按钮**: 在页面右上角找到主题切换按钮（太阳/月亮图标）
3. **点击体验**: 点击按钮，享受从点击位置向外扩散的圆形动画
4. **键盘快捷键**: 也可以使用 `Ctrl+Shift+T` (Windows) 或 `Cmd+Shift+T` (Mac)

## 🌐 浏览器支持

### ✅ 完整支持（带动画）
- **Chrome 111+**
- **Edge 111+**
- **Safari 18+** (部分支持)

### 🔄 降级支持（无动画）
- 所有其他现代浏览器
- 当用户设置了减少动画偏好时

## 📁 修改的文件

### 核心组件
- `components/ThemeToggle/index.tsx` - 添加了动画逻辑

### 样式文件
- `styles/theme-transition.scss` - 新增动画支持样式
- `src/index.scss` - 导入动画样式

## 🎯 动画原理

1. **点击检测**: 获取用户点击的精确坐标
2. **兼容性检查**: 检测浏览器是否支持 View Transition API
3. **禁用过渡**: 临时禁用现有的 CSS 过渡效果
4. **启动动画**: 使用 `document.startViewTransition()` 开始过渡
5. **圆形裁剪**: 通过 `clip-path` 创建从点击位置扩散的圆形动画
6. **清理工作**: 动画完成后恢复 CSS 过渡效果

## 🔧 自定义配置

如果您想调整动画参数，可以在 `components/ThemeToggle/index.tsx` 中修改：

```typescript
// 动画持续时间（毫秒）
duration: 500,

// 动画缓动函数
easing: 'ease-in',
```

## 🎮 用户体验

- **加载状态**: 动画期间按钮显示加载图标
- **防重复点击**: 动画进行时禁用按钮防止重复触发
- **无障碍支持**: 完整的键盘导航和屏幕阅读器支持
- **触觉反馈**: 支持设备震动反馈（如果可用）

## 🐛 故障排除

如果动画不工作：

1. **检查浏览器版本**: 确保使用支持 View Transition API 的浏览器
2. **检查动画偏好**: 确认系统未设置为减少动画
3. **查看控制台**: 检查是否有 JavaScript 错误
4. **降级行为**: 即使动画失败，主题切换功能仍然正常工作

---

享受您的炫酷主题切换动画吧！ 🎨✨