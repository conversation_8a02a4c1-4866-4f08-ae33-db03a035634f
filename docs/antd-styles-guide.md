# Ant Design 组件样式定制指南

## 概述

本项目统一使用 Ant Design 官方提供的 API 方式进行组件样式定制，避免使用 CSS 强制覆盖（如 `!important`）。这种方式更符合组件库设计理念，提高可维护性，确保主题兼容性。

## 核心原则

1. **优先使用组件的 `styles` 属性**：利用 Ant Design 5.x 提供的 `styles` API
2. **使用组件的 `variant` 属性**：如 `variant="borderless"` 控制组件变体
3. **避免 CSS 强制覆盖**：移除所有使用 `!important` 的样式规则
4. **使用组件原生属性**：如 `size`、`type`、`shape` 等官方属性
5. **创建可复用的样式配置常量**：统一管理样式配置
6. **保持类型安全**：确保所有样式配置都有完整的 TypeScript 类型支持

## 样式配置常量

所有样式配置常量都定义在 `src/constants/antdStyles.ts` 中：

### Card 组件样式配置

```typescript
// 无内边距配置 - 用于表格容器等
export const CARD_ZERO_PADDING_STYLES = {
  body: {
    padding: 0,
    borderRadius: 0,
  }
} as const;

// 紧凑内边距配置 - 用于较小内边距场景
export const CARD_COMPACT_PADDING_STYLES = {
  body: {
    padding: 12,
  }
} as const;

// 标准内边距配置 - 用于标准内容展示
export const CARD_STANDARD_PADDING_STYLES = {
  body: {
    padding: 24,
  }
} as const;
```

### 组件变体配置

```typescript
// 无边框变体
export const BORDERLESS_VARIANT = 'borderless' as const;

// 组件尺寸
export const COMPONENT_SIZES = {
  SMALL: 'small',
  MIDDLE: 'middle', 
  LARGE: 'large',
} as const;
```

## 使用示例

### Card 组件

```typescript
import { Card } from 'antd';
import { CARD_ZERO_PADDING_STYLES, BORDERLESS_VARIANT } from '../../src/constants/antdStyles';

// ✅ 推荐：使用 API 方式
<Card
  variant={BORDERLESS_VARIANT}
  styles={CARD_ZERO_PADDING_STYLES}
  className="custom-class"
>
  内容
</Card>

// ❌ 避免：CSS 强制覆盖
<Card className="custom-card">
  内容
</Card>
// .custom-card .ant-card-body { padding: 0 !important; }
```

### Table 组件

```typescript
import { Table } from 'antd';
import { TABLE_COMPACT_STYLES, COMPONENT_SIZES } from '../../src/constants/antdStyles';

// ✅ 推荐：使用 API 方式
<Table
  size={COMPONENT_SIZES.SMALL}
  styles={TABLE_COMPACT_STYLES}
  bordered={false}
  data={data}
  columns={columns}
/>
```

### Button 组件

```typescript
import { Button } from 'antd';
import { BUTTON_NO_SHADOW_STYLES, COMPONENT_SIZES } from '../../src/constants/antdStyles';

// ✅ 推荐：使用原生属性
<Button
  type="primary"
  size={COMPONENT_SIZES.LARGE}
  variant="filled"
  style={BUTTON_NO_SHADOW_STYLES}
>
  按钮
</Button>
```

## 迁移指南

### 从 CSS 覆盖迁移到 API 方式

**之前（CSS 覆盖）：**
```scss
.custom-card {
  .ant-card-body {
    padding: 0 !important;
    border-radius: 0 !important;
  }
}
```

```tsx
<Card className="custom-card">内容</Card>
```

**现在（API 方式）：**
```tsx
import { CARD_ZERO_PADDING_STYLES } from '../../src/constants/antdStyles';

<Card styles={CARD_ZERO_PADDING_STYLES}>内容</Card>
```

### 主题兼容性

使用 CSS 变量确保主题兼容：

```typescript
export const THEME_ADAPTIVE_STYLES = {
  backgroundColor: 'var(--theme-bg-primary)',
  borderColor: 'var(--theme-border-color-split)',
  color: 'var(--theme-text-primary)',
} as const;
```

## 最佳实践

1. **导入统一常量**：
   ```typescript
   import { CARD_ZERO_PADDING_STYLES, BORDERLESS_VARIANT } from '../../src/constants/antdStyles';
   ```

2. **组合使用多个属性**：
   ```tsx
   <Card
     variant={BORDERLESS_VARIANT}
     styles={CARD_STANDARD_PADDING_STYLES}
     size="small"
   >
   ```

3. **类型安全**：
   ```typescript
   type CardStylesConfig = typeof CARD_ZERO_PADDING_STYLES;
   ```

4. **条件样式**：
   ```tsx
   <Card
     styles={compact ? CARD_COMPACT_PADDING_STYLES : CARD_STANDARD_PADDING_STYLES}
   >
   ```

## 注意事项

1. **向后兼容**：现有的 CSS 类保留作为后备方案，但新组件应使用 API 方式
2. **性能优化**：API 方式避免了 CSS 选择器匹配，性能更好
3. **主题切换**：API 方式自动适配主题变化，无需额外处理
4. **调试友好**：样式直接应用到组件，调试更容易

## 相关文件

- `src/constants/antdStyles.ts` - 样式配置常量
- `styles/theme.scss` - 主题变量定义
- `src/index.scss` - 全局样式（已最小化）
- `pages/*/style.scss` - 页面级样式（逐步迁移中）
