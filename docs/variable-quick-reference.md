# V2 Admin 变量快速参考

## 🚀 **快速开始**

### **在组件中使用变量**
```scss
// 1. 导入 SCSS 变量
@use '../../styles/variables' as *;

.my-component {
  // 2. 布局和尺寸用 SCSS 变量
  padding: $spacing-md;
  font-size: $font-size-base;
  border-radius: $border-radius-base;
  
  // 3. 颜色和主题用 CSS 变量
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border-color);
}
```

## 📏 **常用 SCSS 变量**

### **间距系统**
```scss
$spacing-xs: 4px;    // 极小间距
$spacing-sm: 8px;    // 小间距  
$spacing-md: 16px;   // 标准间距 ⭐
$spacing-lg: 24px;   // 大间距
$spacing-xl: 32px;   // 极大间距
```

### **字体系统**
```scss
$font-size-xs: 11px;     // 极小字体
$font-size-sm: 12px;     // 小字体
$font-size-base: 14px;   // 基础字体 ⭐
$font-size-lg: 16px;     // 大字体
$font-size-xl: 18px;     // 极大字体

$font-weight-normal: 400;    // 正常
$font-weight-medium: 500;    // 中等 ⭐
$font-weight-semibold: 600;  // 半粗
$font-weight-bold: 700;      // 粗体
```

### **圆角系统**
```scss
$border-radius-xs: 2px;    // 极小圆角
$border-radius-sm: 4px;    // 小圆角
$border-radius-md: 6px;    // 中等圆角 ⭐
$border-radius-base: 8px;  // 基础圆角
$border-radius-lg: 8px;    // 大圆角
```

### **布局尺寸**
```scss
$header-height: 48px;           // 顶部栏高度
$content-padding: 24px;         // 内容区内边距
$menu-item-height: 46px;        // 菜单项高度
```

### **响应式断点**
```scss
$breakpoint-xs: 480px;    // 超小屏
$breakpoint-sm: 768px;    // 小屏 ⭐
$breakpoint-md: 1024px;   // 中屏
$breakpoint-lg: 1440px;   // 大屏
$breakpoint-xl: 1920px;   // 超大屏
```

## 🎨 **常用 CSS 变量**

### **背景颜色**
```css
var(--theme-bg-primary)    /* 主背景 #ffffff/#1f1f1f */
var(--theme-bg-secondary)  /* 次背景 #f5f5f5/#141414 */
var(--theme-bg-tertiary)   /* 三级背景 #fafafa/#262626 */
```

### **文字颜色**
```css
var(--theme-text-primary)    /* 主文字 */
var(--theme-text-secondary)  /* 次文字 */
var(--theme-text-tertiary)   /* 三级文字 */
```

### **边框颜色**
```css
var(--theme-border-color)       /* 基础边框 */
var(--theme-border-color-split) /* 分割线边框 */
```

## 📱 **响应式设计**

### **标准断点使用**
```scss
.responsive-component {
  padding: $spacing-lg;
  
  // 平板及以下
  @media (max-width: $breakpoint-md) {
    padding: $spacing-md;
  }
  
  // 手机端
  @media (max-width: $breakpoint-sm) {
    padding: $spacing-sm;
  }
}
```

## 🎯 **常见使用场景**

### **卡片组件**
```scss
.card-component {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-color);
  border-radius: $border-radius-base;
  padding: $spacing-lg;
  box-shadow: $box-shadow-base;
}
```

### **按钮组件**
```scss
.button-component {
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-sm;
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border: 1px solid var(--theme-border-color);
}
```

### **表单组件**
```scss
.form-component {
  .form-item {
    margin-bottom: $spacing-md;
  }
  
  .form-label {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: var(--theme-text-primary);
    margin-bottom: $spacing-xs;
  }
  
  .form-input {
    padding: $spacing-sm;
    border-radius: $border-radius-sm;
    border: 1px solid var(--theme-border-color);
    background: var(--theme-bg-primary);
  }
}
```

## ⚡ **性能提示**

### **优先使用 SCSS 变量**
- ✅ 编译时优化，性能更好
- ✅ 适用于不需要动态改变的值

### **CSS 变量用于主题**
- ✅ 支持运行时切换
- ✅ 适用于颜色和主题相关样式

### **避免过度嵌套**
```scss
// ❌ 避免
.component {
  .inner {
    .deep {
      .very-deep {
        color: var(--theme-text-primary);
      }
    }
  }
}

// ✅ 推荐
.component-inner-deep {
  color: var(--theme-text-primary);
}
```

## 🔧 **调试技巧**

### **检查变量值**
```scss
// 在浏览器开发者工具中查看计算值
.debug-component {
  // SCSS 变量会被编译为具体值
  padding: $spacing-md; // 显示为 padding: 16px;
  
  // CSS 变量保持原样
  color: var(--theme-text-primary); // 显示为 var(--theme-text-primary)
}
```

### **主题切换测试**
```javascript
// 在浏览器控制台中快速切换主题
document.documentElement.setAttribute('data-theme', 'dark');
document.documentElement.setAttribute('data-theme', 'light');
```

## 📚 **更多资源**

- 📖 [完整变量系统指南](./variable-system-guide.md)
- 🎨 [主题定制指南](../src/constants/antdStyles.ts)
- 🔧 [Ant Design 样式配置](../src/constants/antdStyles.ts)
