# V2 Admin 变量系统使用指南

## 📋 **系统概述**

V2 Admin 采用 **混合变量系统**，结合 SCSS 变量和 CSS 变量的优势：

- **SCSS 变量**：用于静态设计系统（布局、字体、间距等）
- **CSS 变量**：用于动态主题切换（颜色、主题相关样式）

## 🎨 **SCSS 变量系统** (`styles/variables.scss`)

### **使用场景**
- 布局尺寸（header-height、content-padding）
- 字体系统（font-size、font-weight）
- 间距系统（spacing、margin、padding）
- 圆角系统（border-radius）
- 响应式断点（breakpoint）
- 阴影系统（box-shadow）
- Z-index 层级

### **使用方法**
```scss
// 在组件样式文件中导入
@use '../../styles/variables' as *;

// 使用变量
.my-component {
  padding: $spacing-md;
  font-size: $font-size-base;
  border-radius: $border-radius-base;
  
  @media (max-width: $breakpoint-sm) {
    padding: $spacing-sm;
  }
}
```

### **主要变量分类**

#### 布局尺寸
```scss
$header-height: 48px;
$content-padding: 24px;
$menu-item-height: 46px;
```

#### 字体系统
```scss
$font-size-xs: 11px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-weight-medium: 500;
```

#### 间距系统
```scss
$spacing-md: 16px;  // 统一间距标准
```

#### 响应式断点
```scss
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
```

## 🌈 **CSS 变量系统** (`styles/theme.scss`)

### **使用场景**
- 主题颜色（背景色、文字色、边框色）
- 动态主题切换
- 运行时样式调整

### **使用方法**
```scss
.my-component {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border-color: var(--theme-border-color);
}
```

### **主要变量分类**

#### 背景颜色
```css
--theme-bg-primary: #ffffff;    /* 主要背景 */
--theme-bg-secondary: #f5f5f5;  /* 次要背景 */
--theme-bg-tertiary: #fafafa;   /* 第三级背景 */
```

#### 文字颜色
```css
--theme-text-primary: #000000d9;   /* 主要文字 */
--theme-text-secondary: #00000073; /* 次要文字 */
--theme-text-tertiary: #00000040;  /* 第三级文字 */
```

#### 边框颜色
```css
--theme-border-color: #d9d9d9;       /* 基础边框 */
--theme-border-color-split: #f0f0f0; /* 分割线边框 */
```

## 🔄 **主题切换机制**

### **实现原理**
通过 `data-theme` 属性切换主题：
```html
<html data-theme="light">  <!-- 亮色主题 -->
<html data-theme="dark">   <!-- 暗色主题 -->
```

### **在组件中使用**
```tsx
// 获取当前主题
const theme = useTheme();

// 切换主题
const toggleTheme = () => {
  document.documentElement.setAttribute(
    'data-theme', 
    theme === 'light' ? 'dark' : 'light'
  );
};
```

## 📝 **最佳实践**

### **1. 变量选择原则**
- **静态值** → 使用 SCSS 变量
- **主题相关** → 使用 CSS 变量
- **需要运行时修改** → 使用 CSS 变量

### **2. 新组件开发**
```scss
@use '../../styles/variables' as *;

.new-component {
  // 布局使用 SCSS 变量
  padding: $spacing-md;
  font-size: $font-size-base;
  border-radius: $border-radius-base;
  
  // 颜色使用 CSS 变量
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
  border-color: var(--theme-border-color);
  
  // 响应式使用 SCSS 变量
  @media (max-width: $breakpoint-sm) {
    padding: $spacing-sm;
  }
}
```

### **3. 避免的做法**
❌ 不要混用相同用途的变量
```scss
// 错误：同时使用两套间距系统
.bad-example {
  padding: $spacing-md;
  margin: var(--theme-spacing-lg); // 不一致
}
```

✅ 保持一致性
```scss
// 正确：统一使用 SCSS 变量处理间距
.good-example {
  padding: $spacing-md;
  margin: $spacing-lg;
  background: var(--theme-bg-primary); // 主题色用 CSS 变量
}
```

## 🛠️ **维护指南**

### **添加新变量**

#### SCSS 变量
在 `styles/variables.scss` 中添加：
```scss
// 新的布局变量
$new-component-height: 60px;
$new-spacing-xl: 32px;
```

#### CSS 变量
在 `styles/theme.scss` 中添加：
```css
/* 亮色主题 */
:root, [data-theme="light"] {
  --theme-new-color: #1890ff;
}

/* 暗色主题 */
[data-theme="dark"] {
  --theme-new-color: #40a9ff;
}
```

### **变量命名规范**

#### SCSS 变量
- 使用 kebab-case
- 按功能分组：`$font-size-base`、`$spacing-md`
- 包含尺寸信息：`xs`、`sm`、`base`、`lg`、`xl`

#### CSS 变量
- 使用 `--theme-` 前缀
- 按用途分类：`--theme-bg-*`、`--theme-text-*`
- 语义化命名：`primary`、`secondary`、`tertiary`

## 🎯 **总结**

当前的混合变量系统是经过深思熟虑的架构选择：

- ✅ **稳定可靠**：系统运行稳定，无技术债务
- ✅ **开发友好**：提供良好的开发体验
- ✅ **功能完整**：支持主题切换和设计系统
- ✅ **易于维护**：清晰的变量分工和命名规范

**建议继续使用当前系统，无需进行大规模迁移。**
