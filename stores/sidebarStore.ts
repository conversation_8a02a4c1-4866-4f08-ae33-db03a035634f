import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';
import { MenuItem } from '../types';

// 🎯 侧边栏状态接口
interface SidebarState {
  // 基础状态
  collapsed: boolean;
  isMobile: boolean;
  
  // 弹出层状态
  popoverVisible: boolean;
  popoverMenuItem: MenuItem | null;
  popoverPosition: { top: number; left: number };
  
  // 悬浮状态
  hoveredMenuKey: string | null;
  hoveredElement: HTMLElement | null;
  isHovering: boolean;
  
  // 定时器管理
  hoverTimer: NodeJS.Timeout | null;
  closeTimer: NodeJS.Timeout | null;
  
  // 错误状态
  hasError: boolean;
  errorMessage: string | null;
}

// 🎯 Action 类型
type SidebarAction =
  | { type: 'SET_COLLAPSED'; payload: boolean }
  | { type: 'SET_MOBILE'; payload: boolean }
  | { type: 'SHOW_POPOVER'; payload: { menuItem: MenuItem; position: { top: number; left: number } } }
  | { type: 'HIDE_POPOVER' }
  | { type: 'SET_HOVERING'; payload: boolean }
  | { type: 'SET_HOVERED_MENU'; payload: { key: string | null; element: HTMLElement | null } }
  | { type: 'SET_HOVER_TIMER'; payload: NodeJS.Timeout | null }
  | { type: 'SET_CLOSE_TIMER'; payload: NodeJS.Timeout | null }
  | { type: 'CLEAR_TIMERS' }
  | { type: 'SET_ERROR'; payload: { hasError: boolean; message?: string } }
  | { type: 'RESET_STATE' };

// 🎯 初始状态
const initialState: SidebarState = {
  collapsed: false,
  isMobile: false,
  popoverVisible: false,
  popoverMenuItem: null,
  popoverPosition: { top: 0, left: 0 },
  hoveredMenuKey: null,
  hoveredElement: null,
  isHovering: false,
  hoverTimer: null,
  closeTimer: null,
  hasError: false,
  errorMessage: null,
};

// 🎯 Reducer 函数
function sidebarReducer(state: SidebarState, action: SidebarAction): SidebarState {
  try {
    switch (action.type) {
      case 'SET_COLLAPSED':
        return {
          ...state,
          collapsed: action.payload,
          // 收缩时自动隐藏弹出层
          ...(action.payload && {
            popoverVisible: false,
            popoverMenuItem: null,
            hoveredMenuKey: null,
            hoveredElement: null,
            isHovering: false,
          }),
        };

      case 'SET_MOBILE':
        return {
          ...state,
          isMobile: action.payload,
          // 移动端时隐藏弹出层
          ...(action.payload && {
            popoverVisible: false,
            popoverMenuItem: null,
            hoveredMenuKey: null,
            hoveredElement: null,
            isHovering: false,
          }),
        };

      case 'SHOW_POPOVER':
        return {
          ...state,
          popoverVisible: true,
          popoverMenuItem: action.payload.menuItem,
          popoverPosition: action.payload.position,
          hasError: false,
          errorMessage: null,
        };

      case 'HIDE_POPOVER':
        return {
          ...state,
          popoverVisible: false,
          popoverMenuItem: null,
          hoveredMenuKey: null,
          hoveredElement: null,
          isHovering: false,
        };

      case 'SET_HOVERING':
        return {
          ...state,
          isHovering: action.payload,
        };

      case 'SET_HOVERED_MENU':
        return {
          ...state,
          hoveredMenuKey: action.payload.key,
          hoveredElement: action.payload.element,
        };

      case 'SET_HOVER_TIMER':
        // 清理之前的定时器
        if (state.hoverTimer) {
          clearTimeout(state.hoverTimer);
        }
        return {
          ...state,
          hoverTimer: action.payload,
        };

      case 'SET_CLOSE_TIMER':
        // 清理之前的定时器
        if (state.closeTimer) {
          clearTimeout(state.closeTimer);
        }
        return {
          ...state,
          closeTimer: action.payload,
        };

      case 'CLEAR_TIMERS':
        if (state.hoverTimer) {
          clearTimeout(state.hoverTimer);
        }
        if (state.closeTimer) {
          clearTimeout(state.closeTimer);
        }
        return {
          ...state,
          hoverTimer: null,
          closeTimer: null,
        };

      case 'SET_ERROR':
        return {
          ...state,
          hasError: action.payload.hasError,
          errorMessage: action.payload.message || null,
        };

      case 'RESET_STATE':
        // 清理定时器
        if (state.hoverTimer) {
          clearTimeout(state.hoverTimer);
        }
        if (state.closeTimer) {
          clearTimeout(state.closeTimer);
        }
        return {
          ...initialState,
          collapsed: state.collapsed, // 保留基础状态
          isMobile: state.isMobile,
        };

      default:
        return state;
    }
  } catch (error) {
    console.error('Sidebar reducer error:', error);
    return {
      ...state,
      hasError: true,
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 🎯 Context 创建
const SidebarContext = createContext<{
  state: SidebarState;
  dispatch: React.Dispatch<SidebarAction>;
} | null>(null);

// 🎯 Provider 组件
export const SidebarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(sidebarReducer, initialState);

  // 错误边界处理
  React.useEffect(() => {
    if (state.hasError) {
      console.error('Sidebar state error:', state.errorMessage);
      // 可以在这里添加错误上报逻辑
    }
  }, [state.hasError, state.errorMessage]);

  return (
    <SidebarContext.Provider value={{ state, dispatch }}>
      {children}
    </SidebarContext.Provider>
  );
};

// 🎯 Hook 使用状态
export const useSidebarState = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebarState must be used within a SidebarProvider');
  }
  return context.state;
};

// 🎯 Hook 使用 dispatch
export const useSidebarDispatch = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebarDispatch must be used within a SidebarProvider');
  }
  return context.dispatch;
};

// 🎯 Hook 使用操作函数
export const useSidebarActions = () => {
  const dispatch = useSidebarDispatch();
  const state = useSidebarState();

  return {
    setCollapsed: useCallback((collapsed: boolean) => {
      dispatch({ type: 'SET_COLLAPSED', payload: collapsed });
    }, [dispatch]),

    setMobile: useCallback((isMobile: boolean) => {
      dispatch({ type: 'SET_MOBILE', payload: isMobile });
    }, [dispatch]),

    showPopover: useCallback((menuItem: MenuItem, position: { top: number; left: number }) => {
      dispatch({ type: 'SHOW_POPOVER', payload: { menuItem, position } });
    }, [dispatch]),

    hidePopover: useCallback(() => {
      dispatch({ type: 'HIDE_POPOVER' });
    }, [dispatch]),

    setHovering: useCallback((hovering: boolean) => {
      dispatch({ type: 'SET_HOVERING', payload: hovering });
    }, [dispatch]),

    setHoveredMenu: useCallback((key: string | null, element: HTMLElement | null) => {
      dispatch({ type: 'SET_HOVERED_MENU', payload: { key, element } });
    }, [dispatch]),

    setHoverTimer: useCallback((timer: NodeJS.Timeout | null) => {
      dispatch({ type: 'SET_HOVER_TIMER', payload: timer });
    }, [dispatch]),

    setCloseTimer: useCallback((timer: NodeJS.Timeout | null) => {
      dispatch({ type: 'SET_CLOSE_TIMER', payload: timer });
    }, [dispatch]),

    clearAllTimers: useCallback(() => {
      dispatch({ type: 'CLEAR_TIMERS' });
    }, [dispatch]),

    setError: useCallback((hasError: boolean, message?: string) => {
      dispatch({ type: 'SET_ERROR', payload: { hasError, message } });
    }, [dispatch]),

    resetState: useCallback(() => {
      dispatch({ type: 'RESET_STATE' });
    }, [dispatch]),

    // 复合操作
    handleError: useCallback((error: Error) => {
      console.error('Sidebar error:', error);
      dispatch({ type: 'SET_ERROR', payload: { hasError: true, message: error.message } });
      // 可以添加错误恢复逻辑
    }, [dispatch]),
  };
};
