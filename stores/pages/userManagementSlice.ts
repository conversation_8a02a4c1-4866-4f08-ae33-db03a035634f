import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useMemo, useCallback } from 'react';
import dayjs from 'dayjs';
import type { UserData } from '../../pages/UserListManagement/types';
import type { SortingState, PaginationState, ColumnFiltersState } from '@tanstack/react-table';

// 用户管理状态接口
export interface UserManagementState {
  // 用户数据
  users: UserData[];
  loading: boolean;
  error: string | null;
  
  // 筛选状态
  roleFilter: string;
  statusFilter: string;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  
  // 表单状态
  showCreateForm: boolean;
  createFormLoading: boolean;
  
  // 数据初始化状态
  dataInitialized: boolean;
  
  // 最后更新时间
  lastUpdated: number;
  
  // TanStackTable 状态
  userManagementSorting: SortingState;
  userManagementPagination: PaginationState;
  userManagementColumnFilters: ColumnFiltersState;
  userManagementGlobalFilter: string;
  
  // 批量选择状态
  selectedUserIds: Set<number>;
  isAllSelected: boolean;
  isIndeterminate: boolean;
}

// 用户管理操作接口
export interface UserManagementActions {
  // 数据操作
  setUsers: (users: UserData[]) => void;
  addUser: (user: UserData) => void;
  updateUser: (userId: number, updates: Partial<UserData>) => void;
  deleteUser: (userId: number) => void;
  toggleUserStatus: (userId: number, status: boolean) => void;
  
  // 加载状态
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 筛选操作
  setRoleFilter: (role: string) => void;
  setStatusFilter: (status: string) => void;
  setDateRange: (range: [dayjs.Dayjs, dayjs.Dayjs] | null) => void;
  resetFilters: () => void;
  
  // 表单操作
  setShowCreateForm: (show: boolean) => void;
  setCreateFormLoading: (loading: boolean) => void;
  
  // 初始化
  initializeData: (users: UserData[]) => void;
  
  // 重置状态
  reset: () => void;
  
  // TanStackTable 操作
  setUserManagementSorting: (sorting: SortingState) => void;
  setUserManagementPagination: (pagination: PaginationState) => void;
  setUserManagementColumnFilters: (columnFilters: ColumnFiltersState) => void;
  setUserManagementGlobalFilter: (globalFilter: string) => void;
  resetUserManagementTableFilters: () => void;
  
  // 批量选择操作
  toggleUserSelection: (userId: number) => void;
  toggleAllSelection: () => void;
  clearSelection: () => void;
  selectUsers: (userIds: number[]) => void;
  batchDeleteUsers: () => void;
}

// 默认状态
const DEFAULT_STATE: UserManagementState = {
  users: [],
  loading: false,
  error: null,
  roleFilter: 'all',
  statusFilter: 'all',
  dateRange: null,
  showCreateForm: false,
  createFormLoading: false,
  dataInitialized: false,
  lastUpdated: 0,
  userManagementSorting: [],
  userManagementPagination: { pageIndex: 0, pageSize: 10 },
  userManagementColumnFilters: [],
  userManagementGlobalFilter: '',
  selectedUserIds: new Set(),
  isAllSelected: false,
  isIndeterminate: false,
};

// 创建用户管理 store
export const useUserManagementStore = create<UserManagementState & UserManagementActions>()(
  devtools(
    (set, get) => ({
      ...DEFAULT_STATE,

      // 数据操作
      setUsers: (users: UserData[]) => {
        set({
          users,
          lastUpdated: Date.now(),
        });
      },

      addUser: (user: UserData) => {
        const { users } = get();
        set({
          users: [...users, user],
          lastUpdated: Date.now(),
        });
      },

      updateUser: (userId: number, updates: Partial<UserData>) => {
        const { users } = get();
        const updatedUsers = users.map(user =>
          user.id === userId ? { ...user, ...updates } : user
        );
        set({
          users: updatedUsers,
          lastUpdated: Date.now(),
        });
      },

      deleteUser: (userId: number) => {
        const { users, selectedUserIds } = get();
        const filteredUsers = users.filter(user => user.id !== userId);
        // 从选择状态中移除被删除的用户
        const newSelectedUserIds = new Set(selectedUserIds);
        newSelectedUserIds.delete(userId);
        
        set({
          users: filteredUsers,
          selectedUserIds: newSelectedUserIds,
          lastUpdated: Date.now(),
        });
      },

      toggleUserStatus: (userId: number, status: boolean) => {
        const { users } = get();
        const updatedUsers = users.map(user =>
          user.id === userId
            ? { ...user, status: status ? 'active' as const : 'inactive' as const }
            : user
        );
        set({
          users: updatedUsers,
          lastUpdated: Date.now(),
        });
      },

      // 加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // 筛选操作
      setRoleFilter: (roleFilter: string) => {
        set({ roleFilter, lastUpdated: Date.now() });
      },

      setStatusFilter: (statusFilter: string) => {
        set({ statusFilter, lastUpdated: Date.now() });
      },

      setDateRange: (dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
        set({ dateRange, lastUpdated: Date.now() });
      },

      resetFilters: () => {
        set({
          roleFilter: 'all',
          statusFilter: 'all',
          dateRange: null,
          lastUpdated: Date.now(),
        });
      },

      // 表单操作
      setShowCreateForm: (showCreateForm: boolean) => {
        set({ showCreateForm });
      },

      setCreateFormLoading: (createFormLoading: boolean) => {
        set({ createFormLoading });
      },

      // 初始化
      initializeData: (users: UserData[]) => {
        set({
          users,
          dataInitialized: true,
          loading: false,
          error: null,
          lastUpdated: Date.now(),
        });
      },

      // 重置状态
      reset: () => {
        set({
          ...DEFAULT_STATE,
          lastUpdated: Date.now(),
        });
      },

      // TanStackTable 操作
      setUserManagementSorting: (userManagementSorting: SortingState) => {
        set({ userManagementSorting });
      },

      setUserManagementPagination: (userManagementPagination: PaginationState) => {
        set({ userManagementPagination });
      },

      setUserManagementColumnFilters: (userManagementColumnFilters: ColumnFiltersState) => {
        set({ userManagementColumnFilters });
      },

      setUserManagementGlobalFilter: (userManagementGlobalFilter: string) => {
        set({ userManagementGlobalFilter });
      },

      resetUserManagementTableFilters: () => {
        set({
          userManagementSorting: [],
          userManagementPagination: { pageIndex: 0, pageSize: 10 },
          userManagementColumnFilters: [],
          userManagementGlobalFilter: '',
        });
      },

      // 批量选择操作
      toggleUserSelection: (userId: number) => {
        const { selectedUserIds } = get();
        const newSelectedUserIds = new Set(selectedUserIds);
        
        if (newSelectedUserIds.has(userId)) {
          newSelectedUserIds.delete(userId);
        } else {
          newSelectedUserIds.add(userId);
        }
        
        // 获取当前筛选后的用户列表
        const filteredUsers = get().users; // 这里应该使用筛选后的数据，但为了简化先用全部数据
        const totalFilteredUsers = filteredUsers.length;
        const selectedCount = newSelectedUserIds.size;
        
        set({
          selectedUserIds: newSelectedUserIds,
          isAllSelected: selectedCount === totalFilteredUsers && totalFilteredUsers > 0,
          isIndeterminate: selectedCount > 0 && selectedCount < totalFilteredUsers,
        });
      },

      toggleAllSelection: () => {
        const { selectedUserIds, users } = get();
        const filteredUsers = users; // 这里应该使用筛选后的数据，但为了简化先用全部数据
        
        if (selectedUserIds.size === filteredUsers.length) {
          // 全部取消选择
          set({
            selectedUserIds: new Set(),
            isAllSelected: false,
            isIndeterminate: false,
          });
        } else {
          // 全部选择
          const allUserIds = new Set(filteredUsers.map(user => user.id));
          set({
            selectedUserIds: allUserIds,
            isAllSelected: true,
            isIndeterminate: false,
          });
        }
      },

      clearSelection: () => {
        set({
          selectedUserIds: new Set(),
          isAllSelected: false,
          isIndeterminate: false,
        });
      },

      selectUsers: (userIds: number[]) => {
        set({
          selectedUserIds: new Set(userIds),
        });
      },

      batchDeleteUsers: () => {
        const { users, selectedUserIds } = get();
        const filteredUsers = users.filter(user => !selectedUserIds.has(user.id));
        
        set({
          users: filteredUsers,
          selectedUserIds: new Set(),
          isAllSelected: false,
          isIndeterminate: false,
          lastUpdated: Date.now(),
        });
      },
    }),
    {
      name: 'user-management-store',
    }
  )
);

// 简化的选择器 - 避免无限重新渲染
export const useFilteredUsers = () => {
  const users = useUserManagementStore(state => state.users);
  const roleFilter = useUserManagementStore(state => state.roleFilter);
  const statusFilter = useUserManagementStore(state => state.statusFilter);
  const dateRange = useUserManagementStore(state => state.dateRange);

  return useMemo(() => {
    let filteredUsers = users;

    // 角色筛选
    if (roleFilter !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.role === roleFilter);
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.status === statusFilter);
    }

    // 日期范围筛选
    if (dateRange) {
      const [startDate, endDate] = dateRange;
      filteredUsers = filteredUsers.filter(user => {
        const userDate = dayjs(user.createdAt);
        return userDate.isAfter(startDate.startOf('day')) &&
               userDate.isBefore(endDate.endOf('day'));
      });
    }

    return filteredUsers;
  }, [users, roleFilter, statusFilter, dateRange]);
};

export const useUserStats = () => {
  const users = useUserManagementStore(state => state.users);

  return useMemo(() => ({
    total: users.length,
    active: users.filter(user => user.status === 'active').length,
    inactive: users.filter(user => user.status === 'inactive').length,
    roles: new Set(users.map(user => user.role)).size,
  }), [users]);
};

// 选择状态相关的选择器
export const useSelectionState = () => {
  const selectedUserIds = useUserManagementStore(state => state.selectedUserIds);
  const isAllSelected = useUserManagementStore(state => state.isAllSelected);
  const isIndeterminate = useUserManagementStore(state => state.isIndeterminate);
  const filteredUsers = useFilteredUsers();

  return useMemo(() => {
    // 基于当前筛选后的用户重新计算选择状态
    const filteredUserIds = new Set(filteredUsers.map(user => user.id));
    const selectedFilteredCount = [...selectedUserIds].filter(id => filteredUserIds.has(id)).length;
    const totalFilteredCount = filteredUsers.length;
    
    const actualIsAllSelected = totalFilteredCount > 0 && selectedFilteredCount === totalFilteredCount;
    const actualIsIndeterminate = selectedFilteredCount > 0 && selectedFilteredCount < totalFilteredCount;

    return {
      selectedUserIds,
      isAllSelected: actualIsAllSelected,
      isIndeterminate: actualIsIndeterminate,
      selectedCount: selectedUserIds.size,
      selectedFilteredCount,
    };
  }, [selectedUserIds, isAllSelected, isIndeterminate, filteredUsers]);
};

// 添加一个新的 hook 来处理筛选后的选择操作
export const useFilteredSelectionActions = () => {
  const { toggleUserSelection, clearSelection, selectUsers } = useUserManagementStore();
  const filteredUsers = useFilteredUsers();
  
  const toggleAllFilteredSelection = useCallback(() => {
    const { selectedUserIds } = useUserManagementStore.getState();
    const filteredUserIds = filteredUsers.map(user => user.id);
    const selectedFilteredIds = filteredUserIds.filter(id => selectedUserIds.has(id));
    
    if (selectedFilteredIds.length === filteredUserIds.length) {
      // 取消选择所有筛选后的用户
      const newSelectedIds = new Set(selectedUserIds);
      filteredUserIds.forEach(id => newSelectedIds.delete(id));
      useUserManagementStore.setState({
        selectedUserIds: newSelectedIds,
        isAllSelected: false,
        isIndeterminate: newSelectedIds.size > 0,
      });
    } else {
      // 选择所有筛选后的用户
      const newSelectedIds = new Set([...selectedUserIds, ...filteredUserIds]);
      useUserManagementStore.setState({
        selectedUserIds: newSelectedIds,
        isAllSelected: filteredUserIds.length === filteredUsers.length,
        isIndeterminate: false,
      });
    }
  }, [filteredUsers]);

  return {
    toggleUserSelection,
    toggleAllFilteredSelection,
    clearSelection,
    selectUsers,
  };
};
