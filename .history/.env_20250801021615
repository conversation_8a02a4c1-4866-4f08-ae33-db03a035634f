# V2 Admin 独立项目环境变量

# 应用配置
VITE_APP_TITLE=V2 Admin 管理界面
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=V2 管理界面独立项目

# 开发服务器配置
VITE_PORT=3001
VITE_HOST=0.0.0.0

# API配置
VITE_API_BASE_URL=http://**************:8081/api
VITE_API_TIMEOUT=10000

# 跨域配置
# true: 使用代理模式（开发环境推荐）
# false: 直接请求模式（后端已配置CORS时使用）
VITE_USE_PROXY=false

# 代理目标地址（当 VITE_USE_PROXY=true 时使用）
VITE_PROXY_TARGET=http://**************:8081

# 主题配置
VITE_THEME_PRIMARY_COLOR=#1890ff
VITE_THEME_BORDER_RADIUS=6
# 开发模式配置
VITE_DEV_TOOLS=true
VITE_SOURCE_MAP=true
