// 使用现代 CSS 变量系统

// V2 侧边栏样式 - 固定侧边栏布局
.v2-sidebar-wrapper {
  // 使用相对定位参与flexbox布局
  position: relative;
  height: 100vh; // 固定视口高度
  z-index: $z-index-fixed;
  // 🎨 统一过渡时间：0.2s ease
  transition: flex-basis 0.2s ease, width 0.2s ease;
  background: var(--theme-bg-primary);
  // 完全移除边框，使用阴影分隔
  border: none;
  border-right: none;
  // 🎨 统一阴影效果
  box-shadow: var(--theme-shadow-1);
  overflow-y: auto; // 允许侧边栏内容滚动（如果菜单项过多）
  overflow-x: hidden;
  // 防止侧边栏收缩
  flex-shrink: 0;

  // 主题变化通过CSS变量自动处理

  // 使用flex-basis进行flexbox布局中的宽度控制
  &.collapsed {
    flex-basis: 64px !important;
    width: 64px !important;
    min-width: 64px !important;
    max-width: 64px !important;
  }

  &.expanded {
    flex-basis: 240px !important;
    width: 240px !important;
    min-width: 240px !important;
    max-width: 240px !important;
  }
}

// 🎨 活跃父级菜单项样式 - 展开状态下的视觉反馈
.sidebar-menu-item-active-parent {
  &.ant-menu-submenu {
    // 展开状态下的父级菜单活跃样式
    > .ant-menu-submenu-title {
      background-color: rgba(24, 144, 255, 0.08) !important;
      border-left: 3px solid $primary-color !important;
      color: $primary-color !important;

      .ant-menu-item-icon,
      .anticon {
        color: $primary-color !important;
      }
    }
  }

  // 折叠状态下的父级菜单活跃样式（显示为选中状态）
  &.ant-menu-item {
    background-color: $primary-color !important;
    color: white !important;

    .ant-menu-item-icon,
    .anticon {
      color: white !important;
    }

    &::after {
      border-right-color: $primary-color !important;
    }
  }
}

// 主题样式现在通过CSS变量和Ant Design主题配置处理

// 🔧 折叠状态下的强制样式重置 - 修复子菜单残留问题
.v2-sidebar-wrapper.collapsed {
  .ant-menu-title-content,
  .ant-menu-submenu-arrow {
    display: none !important;
  }

  // 🔧 强制隐藏所有子菜单内容和下拉菜单
  .ant-menu-submenu-inline,
  .ant-menu-sub,
  .ant-menu-submenu-popup {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    max-height: 0 !important;
  }

  // 🔧 移除所有展开状态的类名效果
  .ant-menu-submenu-open {
    .ant-menu-submenu-arrow {
      display: none !important;
    }

    .ant-menu-sub {
      display: none !important;
      height: 0 !important;
      max-height: 0 !important;
    }
  }

  // 🔧 确保折叠状态下没有任何子菜单动画或内容
  .ant-menu-submenu {
    .ant-menu-submenu-title {
      .ant-menu-submenu-arrow {
        display: none !important;
      }
    }

    // 强制隐藏任何可能的子菜单内容
    > .ant-menu {
      display: none !important;
      height: 0 !important;
    }
  }

  // 🎨 优化折叠状态的图标居中 - 加强版
  .ant-menu-item,
  .ant-menu-submenu > .ant-menu-submenu-title {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 64px !important;
    min-width: 64px !important;
    text-align: center !important;

    .anticon {
      margin: 0 !important;
      margin-right: 0 !important;
      margin-left: 0 !important;
      margin-inline-end: 0 !important;
      margin-inline-start: 0 !important;
      font-size: 18px !important;
      flex-shrink: 0 !important;
    }

    .ant-menu-title-content {
      display: none !important;
      visibility: hidden !important;
      width: 0 !important;
      height: 0 !important;
      overflow: hidden !important;
      position: absolute !important;
      left: -9999px !important;
    }

    .ant-menu-submenu-arrow {
      display: none !important;
      visibility: hidden !important;
      width: 0 !important;
      height: 0 !important;
      overflow: hidden !important;
      position: absolute !important;
      left: -9999px !important;
    }
  }
}

.v2-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--theme-bg-primary);
  // 🎨 统一过渡时间
  transition: background-color 0.2s ease;

  // 折叠状态
  &.collapsed {
    .sidebar-logo {
      .logo-text {
        opacity: 0;
        width: 0;
        // 🎨 统一过渡时间
        transition: opacity 0.2s ease, width 0.2s ease;
      }
    }
  }

  // Logo区域
  .sidebar-logo {
    height: $header-height;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: 0 20px;
    flex-shrink: 0;

    .logo-content {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      // 🎨 统一过渡时间
      transition: all 0.2s ease;
    }

    .logo-icon {
      width: 32px;
      height: 32px;
      border-radius: $border-radius-lg;
      background: $primary-color;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: $font-weight-bold;
      font-size: 16px;
      flex-shrink: 0;
      // 🎨 统一过渡时间和简洁悬停效果
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }
    }

    .logo-text {
      font-size: $font-size-lg;
      font-weight: $font-weight-semibold;
      color: var(--theme-text-primary);
      // 🎨 统一过渡时间
      transition: all 0.2s ease;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  // 菜单区域
  .sidebar-menu {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: $spacing-md 0;

    // 🔧 修复父菜单项选中状态 - 确保有子菜单的父项不显示选中高亮
    .ant-menu-submenu {
      // 父菜单项永远不显示选中状态
      &.ant-menu-item-selected,
      &.ant-menu-submenu-selected {
        background: transparent !important;
        color: inherit !important;

        .ant-menu-submenu-title {
          background: transparent !important;
          color: inherit !important;

          &::after {
            display: none !important; // 移除选中状态的右边框
          }
        }
      }

      // 🎨 父菜单项悬停状态 - 简化悬停效果
      .ant-menu-submenu-title {
        // 🎨 统一过渡时间
        transition: all 0.2s ease !important;

        &:hover {
          background: rgba(0, 0, 0, 0.04) !important;
          color: inherit !important;
          // 🎨 简洁的悬停效果
          transform: translateY(-1px);
        }
      }
    }

    // 🔧 确保子菜单项的选中状态正常显示
    .ant-menu-item {
      // 🎨 统一过渡时间
      transition: all 0.2s ease !important;

      &.ant-menu-item-selected {
        background: $primary-color !important;
        color: white !important;

        &::after {
          border-right: 3px solid $primary-color !important;
        }

        .anticon {
          color: white !important;
        }
      }

      // 🎨 子菜单项悬停状态 - 简化悬停效果
      &:hover:not(.ant-menu-item-selected) {
        background: rgba(0, 0, 0, 0.04) !important;
        color: inherit !important;
        // 🎨 简洁的悬停效果
        transform: translateY(-1px);
      }
    }

    // 自定义滚动条 - 参考demo项目设计
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: $border-color-base;
      border-radius: $border-radius-base;
      // 🎨 统一过渡时间
      transition: background-color 0.2s ease;

      &:hover {
        background: $border-color-dark;
      }
    }

    .sidebar-menu-component {
      border: none !important;
      border-inline-end: 0 !important;
      border-right: none !important;
      background: transparent;

      // 强制移除Antd Menu的所有边框
      &.ant-menu {
        border: none !important;
        border-right: none !important;
        border-inline-end: none !important;
      }

      &.ant-menu-inline {
        border: none !important;
        border-right: none !important;
        border-inline-end: none !important;
      }

      &.ant-menu-root {
        border: none !important;
        border-right: none !important;
        border-inline-end: none !important;
      }

      // 菜单项样式 - 优化220px宽度显示
      .ant-menu-item {
        height: $menu-item-height;
        line-height: $menu-item-height;
        margin: $menu-item-margin-block $menu-item-margin-inline;
        border-radius: $border-radius-base;
        padding: 0 20px;
        display: flex;
        align-items: center;

        // 图标样式
        .anticon {
          font-size: $menu-icon-size;
          margin-right: 10px;
          flex-shrink: 0;
          // 🎨 统一过渡时间
          transition: all 0.2s ease;
        }

        .menu-item-label {
          flex: 1;
        }

        .menu-item-badge {
          background: $error-color;
          color: white;
          font-size: $font-size-xs;
          padding: 2px 6px;
          border-radius: $border-radius-lg;
          min-width: 16px;
          text-align: center;
        }

        // 🎨 选中状态 - 统一的颜色方案
        &.ant-menu-item-selected {
          background: $primary-color-light;
          color: $primary-color;

          &::after {
            display: none;
          }
        }

        // 🎨 悬停状态 - 简化效果
        &:hover {
          background: $background-color-light;
          color: $primary-color;
        }
      }

      // 子菜单样式 - 优化220px宽度显示
      .ant-menu-submenu {
        .ant-menu-submenu-title {
          height: $menu-item-height;
          line-height: $menu-item-height;
          margin: $menu-item-margin-block $menu-item-margin-inline;
          border-radius: $border-radius-base;
          padding: 0 20px;

          // 图标样式
          .anticon {
            font-size: $menu-icon-size;
            margin-right: 10px;
            flex-shrink: 0;
            // 🎨 统一过渡时间
            transition: all 0.2s ease;
          }

          // 🎨 悬停状态优化
          &:hover {
            background: $background-color-light;
            color: $primary-color;
          }
        }

        // 子菜单内容
        .ant-menu-sub {
          .ant-menu-item {
            padding-left: 50px; // 子菜单项缩进

            // 🎨 悬停状态
            &:hover {
              background: $background-color-light;
              color: $primary-color;
            }
          }
        }

        // 展开状态
        &.ant-menu-submenu-open {
          .ant-menu-submenu-title {
            color: $primary-color;
          }
        }
      }

      // 折叠状态下的样式 - 强制居中对齐
      &.ant-menu-inline-collapsed {
        .ant-menu-item {
          padding: 0 !important;
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;

          .anticon {
            font-size: $menu-icon-size !important;
            margin-right: 0 !important;
            margin-left: 0 !important;
            margin-inline-end: 0 !important;
          }

          .menu-item-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            transform: scale(0.8);
          }

          // 立即隐藏文字内容 - 无过渡动画
          .ant-menu-title-content {
            display: none !important;
          }
        }

        .ant-menu-submenu {
          .ant-menu-submenu-title {
            padding: 0 !important;
            text-align: center !important;
            justify-content: center !important;
            display: flex !important;
            align-items: center !important;
            cursor: pointer;
            flex-basis: 64px !important;
            width: 64px !important; // 保留作为后备

            .anticon {
              font-size: $menu-icon-size !important;
              margin: 0 auto !important;
              margin-right: 0 !important;
              margin-left: 0 !important;
              margin-inline-end: 0 !important;
              margin-inline-start: 0 !important;
            }

            // 立即隐藏文字内容 - 无过渡动画
            .ant-menu-title-content {
              display: none !important;
            }

            // 立即隐藏展开箭头 - 无过渡动画
            .ant-menu-submenu-arrow {
              display: none !important;
            }

            // 强制移除所有可能的间距
            > * {
              margin-left: 0 !important;
              margin-right: 0 !important;
            }
          }
        }

        // 折叠状态下隐藏分组标题 - 参考demo项目设计
        .ant-menu-item-group-title {
          display: none;
        }
      }

      // 展开状态下的文字显示 - 简单直接，无复杂过渡
      &:not(.ant-menu-inline-collapsed) {
        .ant-menu-item,
        .ant-menu-submenu-title {
          .ant-menu-title-content {
            display: block !important;
          }

          .ant-menu-submenu-arrow {
            display: inline-block !important;
          }
        }
      }

      // 子菜单箭头样式 - 参考demo项目设计
      .ant-menu-submenu-arrow {
        &:before,
        &:after {
          height: 1px;
          border-radius: 0;
        }
      }
    }
  }

  // 底部区域
  .sidebar-footer {
    height: 48px;
    border-top: 1px solid var(--theme-border-color-split);
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .footer-content {
      // 🎨 统一过渡时间
      transition: all 0.2s ease;

      .version-info {
        font-size: $font-size-xs;
        color: var(--theme-text-secondary);
        font-weight: 500;
      }
    }
  }
}

// 移动端Drawer样式
.v2-sidebar-drawer {
  // 确保Drawer在Header之上
  z-index: 1050 !important;

  .ant-drawer-content {
    // 确保内容区域从顶部开始显示
    top: 0 !important;
  }

  .ant-drawer-body {
    padding: 0;
    // 确保body区域完整显示
    height: 100% !important;
    overflow-y: auto;
  }

  // 移动端Drawer中的侧边栏应该始终显示为展开状态
  .v2-sidebar {
    // 确保侧边栏高度完整
    height: 100vh !important;

    // Logo区域在移动端的特殊处理
    .sidebar-logo {
      // 确保Logo区域可见且不被遮挡
      position: relative;
      z-index: 1;
      background: inherit;
    }

    // 强制移除collapsed类的影响
    &.collapsed {
      .sidebar-logo {
        .logo-text {
          opacity: 1 !important;
          width: auto !important;
        }
      }

      .sidebar-menu-component {
        &.ant-menu-inline-collapsed,
        &.ant-menu {
          .ant-menu-item {
            padding: 0 16px !important;
            text-align: left !important;
            justify-content: flex-start !important;
            width: 100% !important;
            min-width: auto !important;

            .anticon {
              margin-right: 12px !important;
              margin-inline-end: 12px !important;
            }
          }

          .ant-menu-submenu {
            .ant-menu-submenu-title {
              padding: 0 16px !important;
              text-align: left !important;
              justify-content: flex-start !important;

              .anticon {
                margin-right: 12px !important;
                margin-inline-end: 12px !important;
              }
            }
          }
        }
      }
    }
  }
}

// 🔧 强力的强制居中样式 - 多重选择器确保生效
.v2-sidebar.collapsed {
  .sidebar-menu-component {
    &.ant-menu-inline-collapsed,
    &.ant-menu {
      .ant-menu-item {
        padding: 0 !important;
        margin: 0 !important;
        text-align: center !important;
        justify-content: center !important;
        display: flex !important;
        align-items: center !important;
        width: 64px !important;
        min-width: 64px !important;

        .anticon {
          margin: 0 !important;
          margin-right: 0 !important;
          margin-left: 0 !important;
          margin-inline-end: 0 !important;
          margin-inline-start: 0 !important;
          font-size: 18px !important;
        }

        .ant-menu-title-content {
          display: none !important;
          visibility: hidden !important;
          width: 0 !important;
          height: 0 !important;
          overflow: hidden !important;
        }

        // 强制移除所有可能的子元素间距
        > * {
          margin-left: 0 !important;
          margin-right: 0 !important;
        }
      }

      // 🔧 专门针对有子菜单的项目进行强制居中
      .ant-menu-submenu {
        // 子菜单标题的强制居中
        .ant-menu-submenu-title {
          padding: 0 !important;
          margin: 0 !important;
          text-align: center !important;
          justify-content: center !important;
          display: flex !important;
          align-items: center !important;
          width: 64px !important;
          min-width: 64px !important;
          position: relative !important;

          // 🔧 保持文字span的事件区域，但隐藏文字内容
          > span:not(.anticon) {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: transparent !important;
            color: transparent !important;
            font-size: 0 !important;
            line-height: 0 !important;
            overflow: hidden !important;
            cursor: pointer !important;
            z-index: 2 !important;
            // 保持可点击和悬浮，但不显示文字
            text-indent: -9999px !important;
          }

          // 隐藏箭头
          .ant-menu-submenu-arrow {
            display: none !important;
            visibility: hidden !important;
          }

          // 确保图标居中显示并在下层
          .anticon {
            margin: 0 !important;
            margin-right: 0 !important;
            margin-left: 0 !important;
            margin-inline-end: 0 !important;
            margin-inline-start: 0 !important;
            font-size: 18px !important;
            flex: none !important;
            z-index: 1 !important;
            position: relative !important;
          }
        }

        // 确保子菜单的内容完全隐藏
        .ant-menu-sub {
          display: none !important;
          visibility: hidden !important;
          height: 0 !important;
          overflow: hidden !important;
        }
      }
    }
  }
}

// 🔧 全局强制居中 - 最高优先级，覆盖所有可能的Ant Design样式
.ant-menu-inline-collapsed {
  .ant-menu-item,
  .ant-menu-submenu-title {
    padding: 0 !important;
    text-align: center !important;
    justify-content: center !important;
    display: flex !important;
    align-items: center !important;

    .anticon {
      margin: 0 !important;
      font-size: 18px !important;
    }

    .ant-menu-title-content {
      display: none !important;
    }
  }

  .ant-menu-submenu-arrow {
    display: none !important;
  }
}

// 🔧 超级强力的覆盖 - 针对具体的侧边栏
.v2-sidebar-wrapper.collapsed {
  .ant-menu-item,
  .ant-menu-submenu-title {
    padding: 0 !important;
    margin: 0 !important;
    text-align: center !important;
    justify-content: center !important;
    display: flex !important;
    align-items: center !important;
    width: 64px !important;

    .anticon {
      margin: 0 !important;
      margin-right: 0 !important;
      margin-left: 0 !important;
      margin-inline-end: 0 !important;
      margin-inline-start: 0 !important;
      font-size: 18px !important;
      position: relative !important;
      left: 0 !important;
      transform: none !important;
    }

    .ant-menu-title-content,
    .ant-menu-submenu-arrow {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      width: 0 !important;
      height: 0 !important;
      overflow: hidden !important;
      position: absolute !important;
      left: -9999px !important;
    }
  }
}

// 🔧 终极强制居中 - 使用最高优先级选择器
.v2-sidebar.collapsed .sidebar-menu .sidebar-menu-component.ant-menu-inline-collapsed,
.v2-sidebar.collapsed .sidebar-menu .sidebar-menu-component.ant-menu,
.v2-sidebar-wrapper.collapsed .sidebar-menu-component.ant-menu-inline-collapsed,
.v2-sidebar-wrapper.collapsed .sidebar-menu-component.ant-menu {
  .ant-menu-item {
    padding: 0 !important;
    margin: 0 !important;
    text-align: center !important;
    justify-content: center !important;
    display: flex !important;
    align-items: center !important;
    width: 64px !important;

    .anticon {
      margin: 0 !important;
      margin-right: 0 !important;
      margin-left: 0 !important;
      margin-inline-end: 0 !important;
      margin-inline-start: 0 !important;
      font-size: 18px !important;
      flex-shrink: 0 !important;
    }

    .ant-menu-title-content {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      width: 0 !important;
      height: 0 !important;
      overflow: hidden !important;
      position: absolute !important;
      left: -9999px !important;
    }
  }

  .ant-menu-submenu {
    .ant-menu-submenu-title {
      padding: 0 !important;
      margin: 0 !important;
      text-align: center !important;
      justify-content: center !important;
      display: flex !important;
      align-items: center !important;
      width: 64px !important;

      .anticon {
        margin: 0 !important;
        margin-right: 0 !important;
        margin-left: 0 !important;
        margin-inline-end: 0 !important;
        margin-inline-start: 0 !important;
        font-size: 18px !important;
        flex-shrink: 0 !important;
      }

      .ant-menu-title-content,
      .ant-menu-submenu-arrow {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        position: absolute !important;
        left: -9999px !important;
      }
    }
  }
}

// 🎨 响应式优化
@media (max-width: $breakpoint-sm) {
  .v2-sidebar-wrapper {
    &.collapsed {
      flex-basis: 56px !important;
      width: 56px !important;
      min-width: 56px !important;
      max-width: 56px !important;
    }
  }

  .v2-sidebar {
    .sidebar-logo {
      padding: 0 16px;

      .logo-icon {
        width: 28px;
        height: 28px;
        font-size: 14px;
      }
    }

    .sidebar-menu {
      .sidebar-menu-component {
        .ant-menu-item,
        .ant-menu-submenu-title {
          height: 40px;
          line-height: 40px;
          padding: 0 16px;

          .anticon {
            font-size: 16px;
            margin-right: 8px;
          }
        }
      }
    }
  }
}
