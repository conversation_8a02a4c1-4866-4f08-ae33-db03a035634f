import React, { useEffect, useRef } from 'react';
import { MenuItem } from '../../types';
import './SubMenuPopover.scss';

interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
  trigger?: 'click' | 'hover';
  width?: number | string;
  placement?: 'right' | 'left';
}

/**
 * 侧边栏折叠状态下的子菜单弹出组件
 * 改进版本，参考HeaderPopover的设计模式
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme = 'light',
  trigger = 'hover',
  width = 200,
  placement = 'right',
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理点击外部关闭和ESC键
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      // 延迟添加事件监听器，避免立即触发
      const timer = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleEscKey);
      }, 100);

      return () => {
        clearTimeout(timer);
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleEscKey);
      };
    }

    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [visible, onClose]);

  // 处理鼠标进入弹出菜单
  const handleMouseEnter = () => {
    if (trigger === 'hover' && closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  };

  // 处理鼠标离开弹出菜单
  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      closeTimeoutRef.current = setTimeout(() => {
        onClose();
      }, 300); // 增加延迟时间，提供更好的用户体验
    }
  };

  // 处理菜单项点击
  const handleMenuItemClick = (item: MenuItem) => {
    if (item.path && !item.children?.length) {
      onMenuClick(item.key, item.path);
      onClose();
    }
  };

  // 计算箭头位置
  const getArrowPosition = () => {
    switch (placement) {
      case 'right':
        return { left: -6, top: 16 };
      case 'left':
        return { right: -6, top: 16 };
      default:
        return { left: -6, top: 16 };
    }
  };

  // 计算箭头旋转角度
  const getArrowRotation = () => {
    switch (placement) {
      case 'right':
        return 'rotate(-45deg)';
      case 'left':
        return 'rotate(135deg)';
      default:
        return 'rotate(-45deg)';
    }
  };

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem) => (
    <div
      key={item.key}
      className={`submenu-menu-item ${item.disabled ? 'disabled' : ''}`}
      onClick={() => !item.disabled && handleMenuItemClick(item)}
    >
      {item.icon && <span className="submenu-item-icon">{item.icon}</span>}
      <span className="submenu-item-label">{item.label}</span>
      {item.badge && (
        <span className="submenu-item-badge">{item.badge}</span>
      )}
    </div>
  );

  if (!visible || !menuItem?.children) {
    return null;
  }

  const arrowPosition = getArrowPosition();
  const arrowRotation = getArrowRotation();

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
        backgroundColor: theme === 'dark' ? '#1f1f1f' : '#ffffff',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08)',
        width: typeof width === 'number' ? `${width}px` : width,
        minWidth: '160px',
        maxWidth: '240px',
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div 
        className="submenu-popover-arrow"
        style={{
          ...arrowPosition,
          transform: arrowRotation,
        }}
      />

      <div className="submenu-popover-content">
        <div className="submenu-popover-menu">
          {menuItem.children.map(renderMenuItem)}
        </div>
      </div>
    </div>
  );
};

export default SubMenuPopover;
