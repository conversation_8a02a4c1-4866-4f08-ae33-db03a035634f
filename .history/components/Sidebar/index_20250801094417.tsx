import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Menu, Drawer } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { SidebarProps, MenuItem } from '../../types';
import { getMenuStateByPath } from '../../utils/menuData';
import { useResponsive } from '../../hooks/useResponsiveListener';
import SubMenuPopover from './SubMenuPopover';
import './style.scss';
import './SubMenuPopover.scss';

/**
 * 侧边栏组件 - 基于demo项目的设计
 * 支持响应式布局、主题切换、菜单折叠等功能
 */
const Sidebar: React.FC<SidebarProps> = ({
  collapsed = false,
  onCollapse,
  menuItems = [],
  selectedKey,
  onMenuSelect,
  theme = 'light',
  width = 240,
  collapsedWidth = 80,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile } = useResponsive();

  // 菜单状态
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [activeParentKeys, setActiveParentKeys] = useState<string[]>([]);

  // 子菜单弹出状态
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [popoverMenuItem, setPopoverMenuItem] = useState<MenuItem | null>(null);
  const [popoverPosition, setPopoverPosition] = useState({ top: 0, left: 0 });
  const [hoverTimeoutId, setHoverTimeoutId] = useState<NodeJS.Timeout | null>(null);

  const sidebarRef = useRef<HTMLDivElement>(null);

  // 🔧 简化的状态管理
  const globalTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 🔧 统一的定时器清理
  const clearAllTimers = useCallback(() => {
    if (hoverTimeoutId) {
      clearTimeout(hoverTimeoutId);
      setHoverTimeoutId(null);
    }
    if (globalTimerRef.current) {
      clearTimeout(globalTimerRef.current);
      globalTimerRef.current = null;
    }
  }, [hoverTimeoutId]);

  // 🔧 监听弹出层事件
  useEffect(() => {
    const handlePopoverEnter = () => {
      clearAllTimers();
    };

    const handlePopoverLeave = () => {
      // 弹出层离开，让全局监听器处理
    };

    document.addEventListener('submenu-popover-enter', handlePopoverEnter);
    document.addEventListener('submenu-popover-leave', handlePopoverLeave);

    return () => {
      document.removeEventListener('submenu-popover-enter', handlePopoverEnter);
      document.removeEventListener('submenu-popover-leave', handlePopoverLeave);
      clearAllTimers();
    };
  }, [clearAllTimers]);

  // 🔧 修复子菜单状态：处理侧边栏折叠时强制关闭所有展开的子菜单
  useEffect(() => {
    if (collapsed) {
      // 立即清空所有展开的子菜单状态
      setOpenKeys([]);

      // 强制关闭任何可能的弹出菜单
      setPopoverVisible(false);
      setPopoverMenuItem(null);

      // 清除悬停定时器
      if (hoverTimeoutId) {
        clearTimeout(hoverTimeoutId);
        setHoverTimeoutId(null);
      }

      // 🔧 强制重置Ant Design Menu组件的内部状态
      // 通过短暂的延迟确保DOM更新完成
      setTimeout(() => {
        const menuElement = document.querySelector('.sidebar-menu-component');
        if (menuElement) {
          // 移除所有展开的子菜单类名
          const expandedItems = menuElement.querySelectorAll('.ant-menu-submenu-open');
          expandedItems.forEach(item => {
            item.classList.remove('ant-menu-submenu-open');
          });
        }
      }, 0);
    }
  }, [collapsed, hoverTimeoutId]);

  // 根据当前路径更新菜单状态 - 完整的状态管理
  useEffect(() => {
    const menuState = getMenuStateByPath(location.pathname, collapsed);

    // 使用外部传入的selectedKey或计算出的selectedKeys
    setSelectedKeys(selectedKey ? [selectedKey] : menuState.selectedKeys);
    setActiveParentKeys(menuState.activeParentKeys);

    // 只在非折叠状态下设置展开的keys
    if (!collapsed) {
      setOpenKeys(menuState.openKeys);
    } else {
      setOpenKeys([]);
    }
  }, [location.pathname, selectedKey, collapsed]);

  // 查找菜单项的辅助函数 - 复制index_new.tsx
  const findMenuItem = (items: MenuItem[], key: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === key) {
        return item;
      }
      if (item.children) {
        const found = findMenuItem(item.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  // 🔧 修复菜单点击逻辑 - 只有叶子节点才能被选中和导航
  const handleMenuClick = ({ key }: { key: string }) => {
    const menuItem = findMenuItem(menuItems, key);

    // 🔧 检查是否为叶子节点（没有子菜单的菜单项）
    const hasChildren = menuItem?.children && menuItem.children.length > 0;

    if (menuItem?.path && !hasChildren) {
      // 只有叶子节点才进行导航和选中
      navigate(menuItem.path);
      onMenuSelect?.(key, menuItem.path);
      setSelectedKeys([key]); // 手动设置选中状态
    } else if (hasChildren && !collapsed) {
      // 🔧 父菜单项只在展开状态下处理展开/收起，收缩状态下由HeaderPopover处理
      const isOpen = openKeys.includes(key);
      if (isOpen) {
        setOpenKeys(openKeys.filter(k => k !== key));
      } else {
        setOpenKeys([...openKeys, key]);
      }
    }
  };

  // 处理子菜单展开/收起 - 完全复制index_new.tsx逻辑
  const handleOpenChange = (keys: string[]) => {
    // 在折叠状态下不允许展开子菜单
    if (collapsed && !isMobile) {
      setOpenKeys([]);
      return;
    }

    // 展开状态下允许正常的子菜单展开/收缩
    setOpenKeys(keys);
  };

  // 处理折叠状态下的子菜单点击
  // 处理弹出菜单的菜单项点击
  const handlePopoverMenuClick = (key: string, path?: string) => {
    if (path) {
      navigate(path);
      onMenuSelect?.(key, path);
      handlePopoverClose(); // 关闭弹出菜单

      // 🔧 状态更新将由useEffect处理，基于新的路径
      // 这样确保状态逻辑的一致性
    }
  };

  // 🔧 修复弹出菜单关闭逻辑 - 确保状态完全清理
  const handlePopoverClose = () => {
    // 🔧 清理所有相关的定时器
    if (hoverTimeoutId) {
      clearTimeout(hoverTimeoutId);
      setHoverTimeoutId(null);
    }

    // 🔧 批量更新状态，避免多次渲染
    setPopoverVisible(false);
    setPopoverMenuItem(null);

    // 🔧 重置位置，避免下次显示时的闪烁
    setPopoverPosition({ top: 0, left: 0 });
  };



  // 处理子菜单标题点击（用于折叠状态）
  const handleSubMenuTitleClick = (key: string, event: React.MouseEvent) => {
    if (collapsed && !isMobile) {
      event.preventDefault();
      event.stopPropagation();

      const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
        for (const item of items) {
          if (item.key === targetKey) {
            return item;
          }
          if (item.children) {
            const found = findMenuItem(item.children, targetKey);
            if (found) return found;
          }
        }
        return null;
      };

      const menuItem = findMenuItem(menuItems, key);
      if (menuItem?.children && menuItem.children.length > 0) {
        // 使用自定义SubMenuPopover
        const target = event.currentTarget as HTMLElement;
        if (target && sidebarRef.current) {
          const sidebarRect = sidebarRef.current.getBoundingClientRect();
          const targetRect = target.getBoundingClientRect();

          const position = {
            top: targetRect.top + (targetRect.height / 2) - 20,
            left: sidebarRect.right + 8,
          };

          setPopoverMenuItem(menuItem);
          setPopoverPosition(position);
          setPopoverVisible(true);
        }
      }
    }
  };

  // 🔧 简化的侧边栏鼠标进入处理
  const handleSidebarMouseEnter = useCallback(() => {
    if (collapsed && !isMobile) {
      clearAllTimers();
    }
  }, [collapsed, isMobile, clearAllTimers]);

  // 🔧 简化的鼠标离开处理
  const handleMouseLeave = useCallback((event: React.MouseEvent) => {
    if (!popoverVisible || !collapsed || isMobile) return;

    const relatedTarget = event.relatedTarget as HTMLElement;
    const popoverElement = document.querySelector('.submenu-popover');

    // 如果鼠标移向弹出层，不关闭
    const isMovingToPopover = popoverElement && (
      popoverElement.contains(relatedTarget) ||
      popoverElement === relatedTarget
    );

    if (isMovingToPopover) {
      return;
    }

    // 延迟关闭
    const closeTimer = setTimeout(() => {
      const popover = document.querySelector('.submenu-popover');
      const sidebar = sidebarRef.current;

      if (!popover?.matches(':hover') && !sidebar?.matches(':hover')) {
        handlePopoverClose();
      }
    }, 150);

    setHoverTimeoutId(closeTimer);
  }, [popoverVisible, collapsed, isMobile, handlePopoverClose]);

  // 🔧 修复悬浮事件监听器 - 使用更稳定的事件处理机制
  useEffect(() => {
    if (!collapsed || isMobile) return;

    // 使用 mouseover 替代 mouseenter，更好地处理事件冒泡
    const handleMouseOver = (event: Event) => {
      const target = event.target as HTMLElement;

      // 查找最近的菜单项元素，支持更深层的嵌套
      const menuItem = target.closest('.ant-menu-item, .ant-menu-submenu');

      if (menuItem) {
        // 🔧 使用 Ant Design 的 key 属性进行匹配
        const menuKey = menuItem.getAttribute('data-menu-id') ||
                       menuItem.getAttribute('data-key') ||
                       menuItem.querySelector('[data-key]')?.getAttribute('data-key');

        if (menuKey) {
          // 在菜单数据中查找对应的菜单项
          const menuItemData = findMenuItem(menuItems, menuKey);

          if (menuItemData?.children && menuItemData.children.length > 0) {
            // 有子菜单，显示弹出菜单
            handleSubmenuItemHover(menuItemData.key, menuItemData, menuItem as HTMLElement);
          } else {
            // 无子菜单项，关闭当前弹出菜单
            handlePopoverClose();
          }
        } else {
          // 🔧 回退到索引匹配方法，但增加更多验证
          const allMenuItems = Array.from(menuItem.parentElement?.children || []);
          const itemIndex = allMenuItems.indexOf(menuItem);

          if (itemIndex >= 0 && itemIndex < menuItems.length) {
            const menuItemData = menuItems[itemIndex];

            if (menuItemData?.children && menuItemData.children.length > 0) {
              handleSubmenuItemHover(menuItemData.key, menuItemData, menuItem as HTMLElement);
            } else {
              handlePopoverClose();
            }
          } else {
            handlePopoverClose();
          }
        }
      } else {
        // 鼠标移到非菜单区域，关闭弹出菜单
        handlePopoverClose();
      }
    };

    // 🔧 添加防抖处理，避免频繁触发
    let debounceTimer: NodeJS.Timeout | null = null;
    const debouncedMouseOver = (event: Event) => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      debounceTimer = setTimeout(() => {
        handleMouseOver(event);
      }, 30); // 减少防抖时间，提高响应性
    };

    const menuContainer = document.querySelector('.sidebar-menu-component');
    if (menuContainer) {
      // 使用 mouseover 事件，不使用 capture 模式
      menuContainer.addEventListener('mouseover', debouncedMouseOver);

      return () => {
        menuContainer.removeEventListener('mouseover', debouncedMouseOver);
        if (debounceTimer) {
          clearTimeout(debounceTimer);
        }
      };
    }
  }, [collapsed, isMobile, menuItems, hoverTimeoutId]);

  // 🔧 简化的全局鼠标监听器
  useEffect(() => {
    if (!collapsed || isMobile || !popoverVisible) return;

    const handleGlobalMouseMove = (event: MouseEvent) => {
      const sidebar = sidebarRef.current;
      const popover = document.querySelector('.submenu-popover');

      if (!sidebar || !popover) return;

      // 简单的悬浮检查
      const isOverSidebar = sidebar.matches(':hover');
      const isOverPopover = popover.matches(':hover');

      if (!isOverSidebar && !isOverPopover) {
        if (!globalTimerRef.current) {
          globalTimerRef.current = setTimeout(() => {
            if (!sidebar.matches(':hover') && !popover.matches(':hover')) {
              handlePopoverClose();
            }
            globalTimerRef.current = null;
          }, 400);
        }
      } else {
        if (globalTimerRef.current) {
          clearTimeout(globalTimerRef.current);
          globalTimerRef.current = null;
        }
      }
    };

    // 🔧 优化的节流机制
    let throttleTimer: NodeJS.Timeout | null = null;
    let lastEventTime = 0;
    let lastMousePos = { x: 0, y: 0 };

    const throttledMouseMove = (event: MouseEvent) => {
      const now = Date.now();
      const currentPos = { x: event.clientX, y: event.clientY };

      // 🔧 如果鼠标位置没有显著变化，跳过处理
      const mouseMoved = Math.abs(currentPos.x - lastMousePos.x) > 2 ||
                        Math.abs(currentPos.y - lastMousePos.y) > 2;

      if (!mouseMoved && now - lastEventTime < 100) return;

      // 🔧 动态调整节流延迟
      const sidebar = sidebarRef.current;
      const popover = document.querySelector('.submenu-popover');

      let throttleDelay = 50; // 默认延迟

      if (sidebar && popover) {
        const sidebarRect = sidebar.getBoundingClientRect();
        const popoverRect = popover.getBoundingClientRect();

        // 在关键区域使用更高频率
        const isInCriticalArea = (
          (currentPos.x >= sidebarRect.right - 20 && currentPos.x <= popoverRect.left + 20) ||
          (currentPos.x >= popoverRect.left - 20 && currentPos.x <= popoverRect.right + 20)
        );

        throttleDelay = isInCriticalArea ? 20 : 50;
      }

      if (now - lastEventTime < throttleDelay && throttleTimer) return;

      if (throttleTimer) {
        clearTimeout(throttleTimer);
      }

      throttleTimer = setTimeout(() => {
        handleGlobalMouseMove(event);
        throttleTimer = null;
        lastEventTime = now;
        lastMousePos = currentPos;
      }, throttleDelay);
    };

    document.addEventListener('mousemove', throttledMouseMove, { passive: true });

    return () => {
      document.removeEventListener('mousemove', throttledMouseMove);
      if (globalCloseTimer) {
        clearTimeout(globalCloseTimer);
      }
      if (throttleTimer) {
        clearTimeout(throttleTimer);
      }
    };
  }, [collapsed, isMobile, popoverVisible, hoverTimeoutId]);

  // 🔧 添加最终保护机制 - 定期检查弹出层状态
  useEffect(() => {
    if (!collapsed || isMobile || !popoverVisible) return;

    const intervalCheck = setInterval(() => {
      const popover = document.querySelector('.submenu-popover');
      const sidebar = sidebarRef.current;

      if (popover && sidebar) {
        const isPopoverHovered = popover.matches(':hover');
        const isSidebarHovered = sidebar.matches(':hover');

        // 如果弹出层和侧边栏都没有被悬浮，且没有活动的定时器，则关闭
        if (!isPopoverHovered && !isSidebarHovered && !hoverTimeoutId && debugRef.current.activeTimers.size === 0) {
          debugLog('Interval check: closing orphaned popover');
          handlePopoverClose();
        }
      }
    }, 2000); // 每2秒检查一次

    return () => {
      clearInterval(intervalCheck);
    };
  }, [collapsed, isMobile, popoverVisible, hoverTimeoutId]);

  // 🔧 修复子菜单项悬停处理 - 改进定时器管理和位置计算
  const handleSubmenuItemHover = (_key: string, menuItem: MenuItem, target: HTMLElement) => {
    if (!collapsed || isMobile) return;

    // 🔧 立即清除之前的定时器，避免竞争条件
    if (hoverTimeoutId) {
      clearTimeout(hoverTimeoutId);
      setHoverTimeoutId(null);
    }

    // 🔧 检查是否是同一个菜单项，避免重复触发
    if (popoverMenuItem?.key === menuItem.key && popoverVisible) {
      return; // 已经显示相同的菜单，不需要重新触发
    }

    // 🔧 立即显示子菜单，移除不必要的延迟
    if (menuItem.children && menuItem.children.length > 0 && target && sidebarRef.current) {
      try {
        const sidebarRect = sidebarRef.current.getBoundingClientRect();
        const targetRect = target.getBoundingClientRect();

        // 🔧 改进位置计算，确保弹出菜单在可视区域内
        const position = {
          top: Math.max(10, targetRect.top + (targetRect.height / 2) - 20),
          left: sidebarRect.right + 8,
        };

        // 🔧 检查是否超出屏幕底部
        const maxTop = window.innerHeight - 200; // 预留200px空间
        if (position.top > maxTop) {
          position.top = maxTop;
        }

        // 🔧 立即更新状态，不使用定时器
        setPopoverMenuItem(menuItem);
        setPopoverPosition(position);
        setPopoverVisible(true);
      } catch (error) {
        console.warn('计算弹出菜单位置时出错:', error);
        handlePopoverClose();
      }
    }
  };

  // 转换菜单项为Ant Design Menu格式
  const convertMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => {
      const hasChildren = item.children && item.children.length > 0;
      const isActiveParent = activeParentKeys.includes(item.key);

      return {
        key: item.key,
        icon: item.icon,
        className: isActiveParent ? 'sidebar-menu-item-active-parent' : undefined,
        label: hasChildren && collapsed && !isMobile ? (
          // 在收缩状态下，为有子菜单的项目添加事件处理
          <span
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleSubMenuTitleClick(item.key, e as any);
            }}
            style={{ cursor: 'pointer', width: '100%', display: 'block' }}
          >
            {item.label}
          </span>
        ) : item.label,
        children: hasChildren ? convertMenuItems(item.children!) : undefined,
      };
    });
  };



  // 转换菜单项为 Ant Design Menu 格式
  const antdMenuItems = useMemo(() => {
    return convertMenuItems(menuItems);
  }, [menuItems, collapsed, isMobile, activeParentKeys]);

  // 侧边栏内容
  const sidebarContent = (
    <div
      ref={sidebarRef}
      className={`v2-sidebar ${theme} ${collapsed && !isMobile ? 'collapsed hide-text' : ''}`}
      onMouseEnter={handleSidebarMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Logo区域 */}
      <div className="sidebar-logo">
        <div className="logo-content">
          {collapsed && !isMobile ? (
            <span className="logo-icon">A</span>
          ) : (
            <>
              <span className="logo-icon">A</span>
              <span className="logo-text">Admin V2</span>
            </>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="sidebar-menu">
        <Menu
          key={`menu-${collapsed ? 'collapsed' : 'expanded'}`}
          mode="inline"
          theme={theme}
          selectedKeys={selectedKeys}
          openKeys={collapsed && !isMobile ? [] : openKeys}
          onOpenChange={handleOpenChange}
          onClick={handleMenuClick}
          inlineCollapsed={collapsed && !isMobile}
          className="sidebar-menu-component"
          style={{
            width: collapsed && !isMobile ? collapsedWidth : width,
            borderRight: 'none',
          }}
          items={antdMenuItems}
          forceSubMenuRender={false}
        />
      </div>

      {/* 底部区域 */}
      <div className="sidebar-footer">
        {(!collapsed || isMobile) && (
          <div className="footer-content">
            <span className="version-info">V2.0.0</span>
          </div>
        )}
      </div>
    </div>
  );

  // 移动端使用Drawer
  if (isMobile) {
    return (
      <Drawer
        placement="left"
        closable={false}
        open={!collapsed}
        onClose={() => onCollapse?.(true)}
        styles={{ body: { padding: 0 } }}
        width={width}
        className="v2-sidebar-drawer"
        zIndex={1050}
      >
        {sidebarContent}
      </Drawer>
    );
  }

  // 桌面端flexbox侧边栏
  return (
    <>
      <div
        className={`v2-sidebar-wrapper ${collapsed ? 'collapsed' : 'expanded'}`}
      >
        {sidebarContent}
      </div>

      {/* 子菜单弹出组件 */}
      <SubMenuPopover
        visible={popoverVisible}
        onClose={handlePopoverClose}
        menuItem={popoverMenuItem}
        position={popoverPosition}
        onMenuClick={handlePopoverMenuClick}
        theme={theme}
        width={200}
      />
    </>
  );
};

export default Sidebar;
