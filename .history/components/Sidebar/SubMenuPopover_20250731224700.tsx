import React, { useEffect, useRef } from 'react';
import { Popover, Menu } from 'antd';
import { MenuItem } from '../../types';
import './SubMenuPopover.scss';

interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
  trigger?: 'click' | 'hover';
  width?: number | string;
  placement?: 'right' | 'left';
}

/**
 * 侧边栏折叠状态下的子菜单弹出组件
 * 使用 Ant Design 官方 Popover 和 Menu 组件，解决文字显示和鼠标交互问题
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme = 'light',
  trigger = 'hover',
  width = 200,
  placement = 'right',
}) => {
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  // 处理 Popover 显示状态变化
  const handleOpenChange = (open: boolean) => {
    console.log('🔄 SubMenuPopover handleOpenChange:', {
      open,
      visible,
      menuItemKey: menuItem?.key,
      trigger,
      timestamp: new Date().toLocaleTimeString()
    });

    if (!open) {
      console.log('⏰ SubMenuPopover 准备延迟关闭，设置 200ms 定时器');
      // 延迟关闭，给用户时间重新移动鼠标到菜单区域
      closeTimeoutRef.current = setTimeout(() => {
        console.log('🔴 SubMenuPopover 定时器触发，执行关闭');
        onClose();
      }, 200); // 额外的缓冲时间
    } else {
      console.log('✅ SubMenuPopover 重新打开，清除关闭定时器');
      // 如果重新打开，清除关闭定时器
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
    }
  };

  // 处理菜单项点击
  const handleMenuClick = ({ key }: { key: string }) => {
    console.log('🖱️ SubMenuPopover handleMenuClick:', {
      clickedKey: key,
      menuItemKey: menuItem?.key,
      childrenCount: menuItem?.children?.length || 0,
      timestamp: new Date().toLocaleTimeString()
    });

    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      console.log('🎯 SubMenuPopover 找到点击的菜单项:', {
        clickedItem: clickedItem ? {
          key: clickedItem.key,
          label: clickedItem.label,
          path: clickedItem.path
        } : null
      });

      if (clickedItem?.path) {
        console.log('🚀 SubMenuPopover 执行导航和关闭');
        onMenuClick(key, clickedItem.path);
        onClose();
      }
    }
  };

  // 转换子菜单项为 Ant Design Menu items 格式
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      disabled: item.disabled,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{item.label}</span>
          {item.badge && (
            <span style={{
              background: '#ff4d4f',
              color: 'white',
              fontSize: '12px',
              padding: '2px 6px',
              borderRadius: '10px',
              minWidth: '16px',
              textAlign: 'center',
              marginLeft: '8px'
            }}>
              {item.badge}
            </span>
          )}
        </div>
      ),
    }));
  };

  if (!visible || !menuItem?.children) {
    return null;
  }

  // 处理菜单内容区域的鼠标事件
  const handleMenuMouseEnter = () => {
    console.log('🐭 SubMenuPopover handleMenuMouseEnter:', {
      hasCloseTimeout: !!closeTimeoutRef.current,
      trigger,
      timestamp: new Date().toLocaleTimeString()
    });

    // 鼠标进入菜单内容时，清除关闭定时器
    if (closeTimeoutRef.current) {
      console.log('🚫 SubMenuPopover 清除关闭定时器 (鼠标进入菜单内容)');
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  };

  const handleMenuMouseLeave = () => {
    console.log('🐭 SubMenuPopover handleMenuMouseLeave:', {
      trigger,
      timestamp: new Date().toLocaleTimeString()
    });

    // 鼠标离开菜单内容时，延迟关闭
    if (trigger === 'hover') {
      console.log('⏰ SubMenuPopover 设置 500ms 延迟关闭定时器 (鼠标离开菜单内容)');
      closeTimeoutRef.current = setTimeout(() => {
        console.log('🔴 SubMenuPopover 菜单内容定时器触发，执行关闭');
        onClose();
      }, 500); // 给用户充足时间重新移动鼠标
    }
  };

  // Popover 内容 - 使用 Ant Design Menu 组件确保文字正确显示
  const popoverContent = (
    <div
      onMouseEnter={handleMenuMouseEnter}
      onMouseLeave={handleMenuMouseLeave}
    >
      <Menu
        mode="vertical"
        theme={theme}
        onClick={handleMenuClick}
        items={convertToMenuItems(menuItem.children)}
        style={{
          border: 'none',
          minWidth: typeof width === 'number' ? `${width}px` : width,
          maxWidth: '240px',
        }}
      />
    </div>
  );

  return (
    <div
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
      }}
    >
      <Popover
        content={popoverContent}
        open={visible}
        onOpenChange={handleOpenChange}
        trigger={trigger === 'hover' ? ['hover'] : ['click']}
        placement={placement === 'right' ? 'rightTop' : 'leftTop'}
        arrow={{ pointAtCenter: false }}
        mouseEnterDelay={0.05}  // 快速显示，提升响应性
        mouseLeaveDelay={0.8}   // 增加延迟时间，给用户足够时间移动鼠标
        destroyTooltipOnHide={false}
        getPopupContainer={() => document.body}
        // 确保弹出菜单区域也能正确处理鼠标事件
        overlayStyle={{
          pointerEvents: 'auto',
        }}
      >
        <div
          style={{
            width: 12,  // 增加触发区域大小
            height: 12, // 增加触发区域大小
            background: 'transparent',
            // 确保触发器能正确接收鼠标事件
            pointerEvents: 'auto',
          }}
        />
      </Popover>
    </div>
  );
};

export default SubMenuPopover;
