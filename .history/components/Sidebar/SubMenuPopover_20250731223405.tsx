import React, { useEffect, useRef } from 'react';
import { Popover, Menu } from 'antd';
import { MenuItem } from '../../types';
import './SubMenuPopover.scss';

interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
  trigger?: 'click' | 'hover';
  width?: number | string;
  placement?: 'right' | 'left';
}

/**
 * 侧边栏折叠状态下的子菜单弹出组件
 * 使用 Ant Design 官方 Popover 和 Menu 组件，解决文字显示和鼠标交互问题
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme = 'light',
  trigger = 'hover',
  width = 200,
  placement = 'right',
}) => {
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  // 处理 Popover 显示状态变化
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  // 处理菜单项点击
  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) return item;
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        onMenuClick(key, clickedItem.path);
        onClose();
      }
    }
  };

  // 计算箭头位置
  const getArrowPosition = () => {
    switch (placement) {
      case 'right':
        return { left: -6, top: 16 };
      case 'left':
        return { right: -6, top: 16 };
      default:
        return { left: -6, top: 16 };
    }
  };

  // 计算箭头旋转角度
  const getArrowRotation = () => {
    switch (placement) {
      case 'right':
        return 'rotate(-45deg)';
      case 'left':
        return 'rotate(135deg)';
      default:
        return 'rotate(-45deg)';
    }
  };

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem) => (
    <div
      key={item.key}
      className={`submenu-menu-item ${item.disabled ? 'disabled' : ''}`}
      onClick={() => !item.disabled && handleMenuItemClick(item)}
    >
      {item.icon && <span className="submenu-item-icon">{item.icon}</span>}
      <span className="submenu-item-label">{item.label}</span>
      {item.badge && (
        <span className="submenu-item-badge">{item.badge}</span>
      )}
    </div>
  );

  if (!visible || !menuItem?.children) {
    return null;
  }

  const arrowPosition = getArrowPosition();
  const arrowRotation = getArrowRotation();

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
        backgroundColor: theme === 'dark' ? '#1f1f1f' : '#ffffff',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08)',
        width: typeof width === 'number' ? `${width}px` : width,
        minWidth: '160px',
        maxWidth: '240px',
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div 
        className="submenu-popover-arrow"
        style={{
          ...arrowPosition,
          transform: arrowRotation,
        }}
      />

      <div className="submenu-popover-content">
        <div className="submenu-popover-menu">
          {menuItem.children.map(renderMenuItem)}
        </div>
      </div>
    </div>
  );
};

export default SubMenuPopover;
