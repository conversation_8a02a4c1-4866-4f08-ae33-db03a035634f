import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Menu, Drawer } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { SidebarProps, MenuItem } from '../../types';
import { getMenuStateByPath } from '../../utils/menuData';
import { useResponsive } from '../../hooks/useResponsiveListener';
import { useSidebarState, useSidebarActions } from '../../stores/sidebarStore';
import { createStableMouseHandler, AdvancedHoverDetector } from '../../utils/debounce';
import SubMenuPopover from './SubMenuPopover';
import './style.scss';
import './SubMenuPopover.scss';

/**
 * 侧边栏组件 - 基于demo项目的设计
 * 支持响应式布局、主题切换、菜单折叠等功能
 */
const Sidebar: React.FC<SidebarProps> = ({
  collapsed = false,
  onCollapse,
  menuItems = [],
  selectedKey,
  onMenuSelect,
  theme = 'light',
  width = 240,
  collapsedWidth = 80,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile } = useResponsive();

  // 🎯 使用全局状态管理
  const sidebarState = useSidebarState();
  const sidebarActions = useSidebarActions();

  // 本地菜单状态（保留用于 Ant Design Menu 组件）
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [activeParentKeys, setActiveParentKeys] = useState<string[]>([]);

  const sidebarRef = useRef<HTMLDivElement>(null);
  const mouseHandlerRef = useRef<ReturnType<typeof createStableMouseHandler> | null>(null);

  // 🔧 同步外部状态到全局状态
  useEffect(() => {
    sidebarActions.setCollapsed(collapsed);
  }, [collapsed, sidebarActions]);

  useEffect(() => {
    sidebarActions.setMobile(isMobile);
  }, [isMobile, sidebarActions]);

  // 🔧 修复子菜单状态：处理侧边栏折叠时强制关闭所有展开的子菜单
  useEffect(() => {
    if (collapsed) {
      // 立即清空所有展开的子菜单状态
      setOpenKeys([]);

      // 🎯 使用全局状态管理清理弹出层
      sidebarActions.hidePopover();
      sidebarActions.clearAllTimers();

      // 🔧 强制重置Ant Design Menu组件的内部状态
      // 通过短暂的延迟确保DOM更新完成
      setTimeout(() => {
        const menuElement = document.querySelector('.sidebar-menu-component');
        if (menuElement) {
          // 移除所有展开的子菜单类名
          const expandedItems = menuElement.querySelectorAll('.ant-menu-submenu-open');
          expandedItems.forEach(item => {
            item.classList.remove('ant-menu-submenu-open');
          });
        }
      }, 0);
    }
  }, [collapsed, sidebarActions]);

  // 根据当前路径更新菜单状态 - 完整的状态管理
  useEffect(() => {
    const menuState = getMenuStateByPath(location.pathname, collapsed);

    // 使用外部传入的selectedKey或计算出的selectedKeys
    setSelectedKeys(selectedKey ? [selectedKey] : menuState.selectedKeys);
    setActiveParentKeys(menuState.activeParentKeys);

    // 只在非折叠状态下设置展开的keys
    if (!collapsed) {
      setOpenKeys(menuState.openKeys);
    } else {
      setOpenKeys([]);
    }
  }, [location.pathname, selectedKey, collapsed]);

  // 查找菜单项的辅助函数 - 复制index_new.tsx
  const findMenuItem = (items: MenuItem[], key: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === key) {
        return item;
      }
      if (item.children) {
        const found = findMenuItem(item.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  // 🔧 修复菜单点击逻辑 - 只有叶子节点才能被选中和导航
  const handleMenuClick = ({ key }: { key: string }) => {
    const menuItem = findMenuItem(menuItems, key);

    // 🔧 检查是否为叶子节点（没有子菜单的菜单项）
    const hasChildren = menuItem?.children && menuItem.children.length > 0;

    if (menuItem?.path && !hasChildren) {
      // 只有叶子节点才进行导航和选中
      navigate(menuItem.path);
      onMenuSelect?.(key, menuItem.path);
      setSelectedKeys([key]); // 手动设置选中状态
    } else if (hasChildren && !collapsed) {
      // 🔧 父菜单项只在展开状态下处理展开/收起，收缩状态下由HeaderPopover处理
      const isOpen = openKeys.includes(key);
      if (isOpen) {
        setOpenKeys(openKeys.filter(k => k !== key));
      } else {
        setOpenKeys([...openKeys, key]);
      }
    }
  };

  // 处理子菜单展开/收起 - 完全复制index_new.tsx逻辑
  const handleOpenChange = (keys: string[]) => {
    // 在折叠状态下不允许展开子菜单
    if (collapsed && !isMobile) {
      setOpenKeys([]);
      return;
    }

    // 展开状态下允许正常的子菜单展开/收缩
    setOpenKeys(keys);
  };

  // 处理折叠状态下的子菜单点击
  // 🎯 高级鼠标事件处理 - 使用全局状态管理
  const handleMenuHover = useCallback((menuItem: MenuItem, element: HTMLElement) => {
    if (!sidebarState.collapsed || sidebarState.isMobile || !menuItem.children?.length) return;

    try {
      // 计算弹出层位置
      const sidebarRect = sidebarRef.current?.getBoundingClientRect();
      const targetRect = element.getBoundingClientRect();

      if (sidebarRect && targetRect) {
        const position = {
          top: Math.max(10, targetRect.top + (targetRect.height / 2) - 20),
          left: sidebarRect.right + 8,
        };

        // 检查是否超出屏幕
        const maxTop = window.innerHeight - 200;
        if (position.top > maxTop) {
          position.top = maxTop;
        }

        // 使用全局状态管理显示弹出层
        sidebarActions.showPopover(menuItem, position);
        sidebarActions.setHoveredMenu(menuItem.key, element);
        sidebarActions.setHovering(true);
      }
    } catch (error) {
      sidebarActions.handleError(error as Error);
    }
  }, [sidebarState.collapsed, sidebarState.isMobile, sidebarActions]);

  const handleMenuLeave = useCallback(() => {
    try {
      sidebarActions.setHovering(false);

      // 延迟关闭，给用户时间移动到弹出层
      const timer = setTimeout(() => {
        if (!sidebarState.isHovering) {
          sidebarActions.hidePopover();
        }
      }, 450);

      sidebarActions.setCloseTimer(timer);
    } catch (error) {
      sidebarActions.handleError(error as Error);
    }
  }, [sidebarState.isHovering, sidebarActions]);

  // 处理弹出菜单的菜单项点击
  const handlePopoverMenuClick = (key: string, path?: string) => {
    if (path) {
      navigate(path);
      onMenuSelect?.(key, path);
      sidebarActions.hidePopover(); // 🎯 使用全局状态管理

      // 🔧 状态更新将由useEffect处理，基于新的路径
      // 这样确保状态逻辑的一致性
    }
  };



  // 处理子菜单标题点击（用于折叠状态）
  const handleSubMenuTitleClick = (key: string, event: React.MouseEvent) => {
    if (collapsed && !isMobile) {
      event.preventDefault();
      event.stopPropagation();

      const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
        for (const item of items) {
          if (item.key === targetKey) {
            return item;
          }
          if (item.children) {
            const found = findMenuItem(item.children, targetKey);
            if (found) return found;
          }
        }
        return null;
      };

      const menuItem = findMenuItem(menuItems, key);
      if (menuItem?.children && menuItem.children.length > 0) {
        // 使用自定义SubMenuPopover
        const target = event.currentTarget as HTMLElement;
        if (target && sidebarRef.current) {
          const sidebarRect = sidebarRef.current.getBoundingClientRect();
          const targetRect = target.getBoundingClientRect();

          const position = {
            top: targetRect.top + (targetRect.height / 2) - 20,
            left: sidebarRect.right + 8,
          };

          setPopoverMenuItem(menuItem);
          setPopoverPosition(position);
          setPopoverVisible(true);
        }
      }
    }
  };

  // 处理侧边栏悬停进入
  const handleSidebarMouseEnter = () => {
    if (collapsed && !isMobile) {
      // 清除之前的定时器
      if (hoverTimeoutId) {
        clearTimeout(hoverTimeoutId);
        setHoverTimeoutId(null);
      }
    }
  };

  // 处理鼠标移出侧边栏区域
  const handleMouseLeave = () => {
    // 清除悬停定时器
    if (hoverTimeoutId) {
      clearTimeout(hoverTimeoutId);
      setHoverTimeoutId(null);
    }

    // 处理SubMenuPopover关闭
    if (popoverVisible && collapsed && !isMobile) {
      // 延迟关闭，给用户时间移动到弹出菜单
      setTimeout(() => {
        // 检查鼠标是否在弹出菜单上
        const popoverElement = document.querySelector('.submenu-popover');
        if (popoverElement && popoverElement.matches(':hover')) {
          return; // 如果鼠标在弹出菜单上，不关闭
        }
        if (popoverVisible) {
          handlePopoverClose();
        }
      }, 300);
    }
  };

  // 🎯 高级鼠标事件处理器初始化
  useEffect(() => {
    if (!sidebarState.collapsed || sidebarState.isMobile) {
      // 清理现有的处理器
      if (mouseHandlerRef.current) {
        mouseHandlerRef.current.destroy();
        mouseHandlerRef.current = null;
      }
      return;
    }

    try {
      // 创建稳定的鼠标处理器
      const mouseHandler = createStableMouseHandler(
        (element: HTMLElement) => {
          // 悬浮处理
          const allMenuItems = Array.from(element.parentElement?.children || []);
          const itemIndex = allMenuItems.indexOf(element);

          if (itemIndex >= 0 && itemIndex < menuItems.length) {
            const menuItemData = menuItems[itemIndex];
            if (menuItemData?.children && menuItemData.children.length > 0) {
              handleMenuHover(menuItemData, element);
            }
          }
        },
        () => {
          // 离开处理
          handleMenuLeave();
        },
        {
          hoverDelay: 180,
          leaveDelay: 450,
          throttleInterval: 50,
          enableFastSwipe: true
        }
      );

      mouseHandlerRef.current = mouseHandler;

      const menuContainer = document.querySelector('.sidebar-menu-component');
      if (menuContainer) {
        menuContainer.addEventListener('mouseover', mouseHandler.handleMouseOver, true);
        menuContainer.addEventListener('mousemove', mouseHandler.handleMouseMove, true);

        return () => {
          menuContainer.removeEventListener('mouseover', mouseHandler.handleMouseOver, true);
          menuContainer.removeEventListener('mousemove', mouseHandler.handleMouseMove, true);
          mouseHandler.destroy();
        };
      }
    } catch (error) {
      sidebarActions.handleError(error as Error);
    }

    return () => {
      if (mouseHandlerRef.current) {
        mouseHandlerRef.current.destroy();
        mouseHandlerRef.current = null;
      }
    };
  }, [sidebarState.collapsed, sidebarState.isMobile, menuItems, handleMenuHover, handleMenuLeave, sidebarActions]);

  // 处理子菜单项悬停
  const handleSubmenuItemHover = (_key: string, menuItem: MenuItem, target: HTMLElement) => {
    if (collapsed && !isMobile) {
      // 清除之前的定时器
      if (hoverTimeoutId) {
        clearTimeout(hoverTimeoutId);
        setHoverTimeoutId(null);
      }

      // 立即显示子菜单
      const timeoutId = setTimeout(() => {
        if (menuItem.children && menuItem.children.length > 0) {
          if (target && sidebarRef.current) {
            const sidebarRect = sidebarRef.current.getBoundingClientRect();
            const targetRect = target.getBoundingClientRect();

            const position = {
              top: targetRect.top + (targetRect.height / 2) - 20,
              left: sidebarRect.right + 8,
            };

            setPopoverMenuItem(menuItem);
            setPopoverPosition(position);
            setPopoverVisible(true);
          }
        }
      }, 0);

      setHoverTimeoutId(timeoutId);
    }
  };

  // 转换菜单项为Ant Design Menu格式
  const convertMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => {
      const hasChildren = item.children && item.children.length > 0;
      const isActiveParent = activeParentKeys.includes(item.key);

      return {
        key: item.key,
        icon: item.icon,
        className: isActiveParent ? 'sidebar-menu-item-active-parent' : undefined,
        label: hasChildren && collapsed && !isMobile ? (
          // 在收缩状态下，为有子菜单的项目添加事件处理
          <span
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleSubMenuTitleClick(item.key, e as any);
            }}
            style={{ cursor: 'pointer', width: '100%', display: 'block' }}
          >
            {item.label}
          </span>
        ) : item.label,
        children: hasChildren ? convertMenuItems(item.children!) : undefined,
      };
    });
  };



  // 转换菜单项为 Ant Design Menu 格式
  const antdMenuItems = useMemo(() => {
    return convertMenuItems(menuItems);
  }, [menuItems, collapsed, isMobile, activeParentKeys]);

  // 侧边栏内容
  const sidebarContent = (
    <div
      ref={sidebarRef}
      className={`v2-sidebar ${theme} ${collapsed && !isMobile ? 'collapsed hide-text' : ''}`}
      onMouseEnter={handleSidebarMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Logo区域 */}
      <div className="sidebar-logo">
        <div className="logo-content">
          {collapsed && !isMobile ? (
            <span className="logo-icon">A</span>
          ) : (
            <>
              <span className="logo-icon">A</span>
              <span className="logo-text">Admin V2</span>
            </>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="sidebar-menu">
        <Menu
          key={`menu-${collapsed ? 'collapsed' : 'expanded'}`}
          mode="inline"
          theme={theme}
          selectedKeys={selectedKeys}
          openKeys={collapsed && !isMobile ? [] : openKeys}
          onOpenChange={handleOpenChange}
          onClick={handleMenuClick}
          inlineCollapsed={collapsed && !isMobile}
          className="sidebar-menu-component"
          style={{
            width: collapsed && !isMobile ? collapsedWidth : width,
            borderRight: 'none',
          }}
          items={antdMenuItems}
          forceSubMenuRender={false}
        />
      </div>

      {/* 底部区域 */}
      <div className="sidebar-footer">
        {(!collapsed || isMobile) && (
          <div className="footer-content">
            <span className="version-info">V2.0.0</span>
          </div>
        )}
      </div>
    </div>
  );

  // 移动端使用Drawer
  if (isMobile) {
    return (
      <Drawer
        placement="left"
        closable={false}
        open={!collapsed}
        onClose={() => onCollapse?.(true)}
        styles={{ body: { padding: 0 } }}
        width={width}
        className="v2-sidebar-drawer"
        zIndex={1050}
      >
        {sidebarContent}
      </Drawer>
    );
  }

  // 桌面端flexbox侧边栏
  return (
    <>
      <div
        className={`v2-sidebar-wrapper ${collapsed ? 'collapsed' : 'expanded'}`}
        onMouseLeave={handleMouseLeave}
      >
        {sidebarContent}
      </div>

      {/* 子菜单弹出组件 */}
      <SubMenuPopover
        visible={popoverVisible}
        onClose={handlePopoverClose}
        menuItem={popoverMenuItem}
        position={popoverPosition}
        onMenuClick={handlePopoverMenuClick}
        theme={theme}
        width={200}
      />
    </>
  );
};

export default Sidebar;
