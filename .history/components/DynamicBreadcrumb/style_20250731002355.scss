// 动态面包屑导航样式
@import '../../styles/variables.scss';

.dynamic-breadcrumb {
  // 容器样式
  padding: 0;
  margin: 0;

  .breadcrumb-nav {
    margin: 0;
    padding: 0;
    font-size: 13px;
    line-height: 1.5;

    // 重写 Ant Design 默认样式
    &.ant-breadcrumb {
      color: $text-color-tertiary;

      // 面包屑项容器
      .ant-breadcrumb-link,
      .ant-breadcrumb-separator {
        color: inherit;
      }

      // 分隔符样式优化
      .ant-breadcrumb-separator {
        margin: 0 6px;
        color: $text-color-disabled;
        font-weight: 400;
        user-select: none;
      }
    }

    // 面包屑项样式
    .breadcrumb-item {
      display: inline-flex;
      align-items: center;

      .breadcrumb-text {
        color: $text-color-tertiary;
        font-size: 13px;
        font-weight: 400;
        cursor: default;
        user-select: none;
        transition: none; // 移除所有过渡效果

        // 确保没有任何可点击的视觉提示
        &:hover {
          color: $text-color-tertiary;
          text-decoration: none;
        }
      }
    }

    // 省略号样式
    .breadcrumb-ellipsis {
      color: $text-color-disabled;
      font-weight: 400;
      font-size: 13px;
      user-select: none;
      cursor: default;
    }
  }

  // 响应式设计
  @media (max-width: $breakpoint-md) {
    .breadcrumb-nav {
      font-size: 12px;

      .ant-breadcrumb-separator {
        margin: 0 4px;
      }

      .breadcrumb-item {
        .breadcrumb-text {
          font-size: 12px;
        }
      }

      .breadcrumb-ellipsis {
        font-size: 12px;
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .breadcrumb-nav {
      font-size: 11px;

      .ant-breadcrumb-separator {
        margin: 0 3px;
      }

      .breadcrumb-item {
        .breadcrumb-text {
          font-size: 11px;
        }
      }

      .breadcrumb-ellipsis {
        font-size: 11px;
      }
    }
  }

  // 暗色主题适配
  .v2-basic-layout.dark & {
    .breadcrumb-nav {
      &.ant-breadcrumb {
        color: rgba(255, 255, 255, 0.45);

        .ant-breadcrumb-separator {
          color: rgba(255, 255, 255, 0.25);
        }
      }

      .breadcrumb-item {
        .breadcrumb-text {
          color: rgba(255, 255, 255, 0.45);

          &:hover {
            color: rgba(255, 255, 255, 0.45);
          }
        }
      }

      .breadcrumb-ellipsis {
        color: rgba(255, 255, 255, 0.25);
      }
    }
  }
}
