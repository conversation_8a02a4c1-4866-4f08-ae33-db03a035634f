// 主题切换组件样式 - 简化版
.theme-toggle-button,
.theme-toggle-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 6px;

  &:hover {
    background-color: var(--ant-color-fill-tertiary);
  }

  .anticon {
    // 保持图标的默认样式，不强制覆盖动画
  }

  // 自定义图标样式
  .custom-sun-icon,
  .custom-moon-icon,
  .custom-loading-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--ant-color-text);
    transition: color 0.2s ease;

    // 确保图标完全静态，无任何动画
    animation: none !important;
    transform: none !important;

    // 在不同主题下的颜色适配
    [data-theme="dark"] & {
      color: var(--ant-color-text);
    }

    [data-theme="light"] & {
      color: var(--ant-color-text);
    }
  }

  // 太阳图标特定样式
  .custom-sun-icon {
    color: #faad14; // 金黄色，代表太阳

    [data-theme="dark"] & {
      color: #fadb14; // 暗色主题下稍微亮一些
    }
  }

  // 月亮图标特定样式
  .custom-moon-icon {
    color: #722ed1; // 紫色，代表夜晚

    [data-theme="light"] & {
      color: #531dab; // 亮色主题下稍微深一些
    }
  }

  // 加载图标样式
  .custom-loading-icon {
    color: var(--ant-color-primary);
  }
}

.theme-toggle-dropdown {
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

// 主题选项样式
.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-width: 120px;

  .theme-option-icon {
    display: flex;
    align-items: center;
    width: 16px;
    color: var(--ant-color-text-secondary);
  }

  .theme-option-label {
    flex: 1;
    color: var(--ant-color-text);
  }

  .theme-option-check {
    color: var(--ant-color-primary);
    font-size: 12px;
  }
}

// ===== 所有主题切换动画已移除 =====
// 现在使用直接的主题切换，无动画效果

// 响应式设计
@media (max-width: 768px) {
  .theme-toggle-button,
  .theme-toggle-trigger {
    padding: 4px 8px;

    .ant-btn-icon {
      font-size: 14px;
    }
  }
}

// ===== 主题特定样式已简化 =====
// 现在使用统一的CSS变量，无需特定主题样式
