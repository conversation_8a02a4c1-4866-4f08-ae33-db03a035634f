import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFilteredRowModel,
  flexRender,
  type ColumnDef,
  type SortingState,
  type PaginationState,
  type ColumnFiltersState,
} from '@tanstack/react-table';
import { Button, Spin, Select, Empty } from 'antd';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import {
  BUTTON_UNIFIED_STYLES,
  SELECT_CUSTOM_STYLES
} from '../../src/constants/antdStyles';
import { TanStackTableProps, TableDimensions, HeightStrategy } from './types';
import { useTableState, useTableConfig } from './hooks';
import {
  fuzzyFilter,
  fuzzySort,
  dateRangeFilter,
  calculateTableHeight,
  calculateTotalHeight,
  debounce,
  SIZE_PRESETS,
  getSizePreset,
  getAvailableSizes
} from './utils';
import './style.scss';

const TanStackTable = <TData extends Record<string, any> = any>({
  data,
  columns,
  loading = false,
  globalFilter,
  columnFilters,
  sorting,
  pagination,
  config,
  events,
  className = '',
  style,
  emptyContent,
  showPagination = true,
  filterFns,
}: TanStackTableProps<TData>) => {
  // 使用自定义 hooks
  const tableState = useTableState({
    sorting,
    pagination,
    columnFilters,
    globalFilter,
  });
  
  const tableConfig = useTableConfig(config);

  // DOM 测量相关的 refs 和状态
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const tableHeaderRef = useRef<HTMLTableSectionElement>(null);
  const tableRowRef = useRef<HTMLTableRowElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  const [measuredDimensions, setMeasuredDimensions] = useState<TableDimensions>({
    containerHeight: 0,
    headerHeight: 0,
    rowHeight: 0,
    availableHeight: 0,
  });

  const [scrollState, setScrollState] = useState({
    canScrollLeft: false,
    canScrollRight: false,
    isScrolling: false,
  });

  // 合并筛选函数
  const mergedFilterFns = useMemo(() => ({
    fuzzy: fuzzyFilter,
    dateRange: dateRangeFilter,
    ...filterFns,
  }), [filterFns]);

  // 创建表格实例
  const table = useReactTable({
    data,
    columns,
    filterFns: mergedFilterFns,
    state: {
      sorting: sorting || tableState.sorting,
      pagination: pagination || tableState.pagination,
      columnFilters: columnFilters || tableState.columnFilters,
      globalFilter: globalFilter || tableState.globalFilter,
    },
    onSortingChange: (updater) => {
      const newSorting = typeof updater === 'function' 
        ? updater(sorting || tableState.sorting)
        : updater;
      
      if (events?.onSortingChange) {
        events.onSortingChange(newSorting);
      } else {
        tableState.setSorting(newSorting);
      }
    },
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function'
        ? updater(pagination || tableState.pagination)
        : updater;
      
      if (events?.onPageChange) {
        events.onPageChange(newPagination.pageIndex, newPagination.pageSize);
      } else {
        tableState.setPagination(newPagination);
      }
    },
    onColumnFiltersChange: (updater) => {
      const newFilters = typeof updater === 'function'
        ? updater(columnFilters || tableState.columnFilters)
        : updater;
      
      if (events?.onColumnFiltersChange) {
        events.onColumnFiltersChange(newFilters);
      } else {
        tableState.setColumnFilters(newFilters);
      }
    },
    onGlobalFilterChange: (updater) => {
      const newGlobalFilter = typeof updater === 'function'
        ? updater(globalFilter || tableState.globalFilter)
        : updater;
      
      if (events?.onGlobalFilterChange) {
        events.onGlobalFilterChange(newGlobalFilter);
      } else {
        tableState.setGlobalFilter(newGlobalFilter);
      }
    },
    globalFilterFn: fuzzyFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: false,
    manualSorting: false,
    autoResetPageIndex: false, // 禁用自动重置页面索引
    autoResetExpanded: false, // 禁用自动重置展开状态
    autoResetGlobalFilter: false, // 禁用自动重置全局过滤器
    autoResetColumnFilters: false, // 禁用自动重置列过滤器
    autoResetSortBy: false, // 禁用自动重置排序
  });



  // 防抖的测量函数
  const debouncedMeasure = useMemo(
    () => debounce(() => {
      if (tableContainerRef.current && tableHeaderRef.current) {
        const container = tableContainerRef.current;
        const header = tableHeaderRef.current;

        const viewportHeight = window.innerHeight;
        const containerRect = container.getBoundingClientRect();
        const availableHeight = viewportHeight - containerRect.top - 50;

        // 测量表头高度
        const headerHeight = header.getBoundingClientRect().height;

        // 测量行高度
        let rowHeight = tableConfig.rowHeight;
        if (tableRowRef.current) {
          const measuredRowHeight = tableRowRef.current.getBoundingClientRect().height;
          if (measuredRowHeight > 0) {
            rowHeight = measuredRowHeight;
          }
        }

        const newDimensions: TableDimensions = {
          containerHeight: availableHeight,
          headerHeight,
          rowHeight,
          availableHeight: Math.max(availableHeight - headerHeight, 200),
        };

        setMeasuredDimensions(newDimensions);
      }
    }, 100),
    [tableConfig.rowHeight]
  );

  // 动态高度计算
  const getTableHeight = useCallback(() => {
    const currentPageRowCount = table.getRowModel().rows.length;
    const pageSize = (pagination || tableState.pagination).pageSize;
    const totalDataCount = table.getFilteredRowModel().rows.length;

    // 空状态
    if (totalDataCount === 0) {
      return `${tableConfig.emptyStateHeight}px`;
    }

    // 使用测量的行高或默认值
    const effectiveRowHeight = measuredDimensions.rowHeight || tableConfig.rowHeight;
    // 使用测量的表头高度或默认值
    const effectiveHeaderHeight = measuredDimensions.headerHeight || tableConfig.headerHeight;

    // 对于 10 行分页，使用 DOM 测量的表头高度 + 行高 × 10
    if (pageSize === 10) {
      const bodyHeight = effectiveRowHeight * pageSize;
      const totalHeight = effectiveHeaderHeight + bodyHeight;
      return `${totalHeight}px`;
    }

    // 其他分页大小也包含表头高度
    return calculateTableHeight(currentPageRowCount, pageSize, {
      rowHeight: effectiveRowHeight,
      headerHeight: effectiveHeaderHeight,
      includeHeader: true,
      emptyStateHeight: tableConfig.emptyStateHeight,
    });
  }, [
    table,
    pagination,
    tableState.pagination,
    tableConfig,
    measuredDimensions.rowHeight,
    measuredDimensions.headerHeight,
  ]);

  // 获取包含分页组件的总高度
  const getTotalHeightWithPagination = useCallback(() => {
    const currentPageRowCount = table.getRowModel().rows.length;
    const pageSize = (pagination || tableState.pagination).pageSize;
    const totalDataCount = table.getFilteredRowModel().rows.length;

    // 空状态
    if (totalDataCount === 0) {
      return `${tableConfig.emptyStateHeight + tableConfig.paginationHeight}px`;
    }

    // 使用测量的行高或默认值
    const effectiveRowHeight = measuredDimensions.rowHeight || tableConfig.rowHeight;
    // 使用测量的表头高度或默认值
    const effectiveHeaderHeight = measuredDimensions.headerHeight || tableConfig.headerHeight;
    // 使用配置的分页高度
    const effectivePaginationHeight = tableConfig.paginationHeight;

    return calculateTotalHeight(currentPageRowCount, pageSize, {
      rowHeight: effectiveRowHeight,
      headerHeight: effectiveHeaderHeight,
      paginationHeight: effectivePaginationHeight,
      includeHeader: true,
      includePagination: true,
      emptyStateHeight: tableConfig.emptyStateHeight,
    });
  }, [
    table,
    pagination,
    tableState.pagination,
    tableConfig,
    measuredDimensions.rowHeight,
    measuredDimensions.headerHeight,
  ]);

  // 获取当前高度策略信息
  const getCurrentHeightStrategy = useCallback((): HeightStrategy => {
    const currentPageRowCount = table.getRowModel().rows.length;
    const pageSize = (pagination || tableState.pagination).pageSize;
    const totalDataCount = table.getFilteredRowModel().rows.length;

    if (totalDataCount === 0) {
      return {
        type: 'empty',
        description: '空状态固定高度',
        height: tableConfig.emptyStateHeight,
        useDomMeasurement: false,
      };
    }

    const effectiveRowHeight = measuredDimensions.rowHeight || tableConfig.rowHeight;
    const effectiveHeaderHeight = measuredDimensions.headerHeight || tableConfig.headerHeight;
    const useDomMeasurement = measuredDimensions.rowHeight > 0 && measuredDimensions.headerHeight > 0;

    if (pageSize === 10) {
      const bodyHeight = effectiveRowHeight * pageSize;
      const totalHeight = effectiveHeaderHeight + bodyHeight;
      return {
        type: 'fixed',
        description: `固定高度: 表头${effectiveHeaderHeight}px + ${pageSize} × ${effectiveRowHeight}px = ${totalHeight}px ${useDomMeasurement ? '(DOM测量)' : '(默认值)'}`,
        height: totalHeight,
        useDomMeasurement,
      };
    }

    const minBodyHeight = effectiveRowHeight * tableConfig.minRowsForDynamic;
    const actualBodyHeight = effectiveRowHeight * currentPageRowCount;
    const finalBodyHeight = Math.max(actualBodyHeight, minBodyHeight);
    const totalHeight = effectiveHeaderHeight + finalBodyHeight;

    return {
      type: 'dynamic',
      description: `动态高度: 表头${effectiveHeaderHeight}px + max(${currentPageRowCount} × ${effectiveRowHeight}px, ${tableConfig.minRowsForDynamic} × ${effectiveRowHeight}px) = ${totalHeight}px ${useDomMeasurement ? '(DOM测量)' : '(默认值)'}`,
      height: totalHeight,
      useDomMeasurement,
    };
  }, [
    table,
    pagination,
    tableState.pagination,
    tableConfig,
    measuredDimensions.rowHeight,
  ]);

  // 计算表格的动态最小宽度
  const getTableMinWidth = useCallback(() => {
    // 如果配置了 minTableWidth，使用配置值
    if (tableConfig.minTableWidth) {
      return tableConfig.minTableWidth;
    }

    // 否则根据列宽计算
    const totalColumnWidth = table.getAllColumns().reduce((total, column) => {
      const size = column.getSize();
      return total + (size || 150); // 默认列宽 150px
    }, 0);

    // 至少保证 800px 的最小宽度
    return Math.max(totalColumnWidth, 800);
  }, [table, tableConfig.minTableWidth]);



  // 处理滚动事件 - 优化响应性
  const handleScroll = useCallback(() => {
    setScrollState(prev => ({ ...prev, isScrolling: true }));

    // 减少延迟，提高滚动响应性
    const timer = setTimeout(() => {
      const container = scrollContainerRef.current;
      if (container) {
        const { scrollLeft, scrollWidth, clientWidth } = container;
        const canScrollLeft = scrollLeft > 0;
        const canScrollRight = scrollLeft < scrollWidth - clientWidth - 1;

        setScrollState({
          canScrollLeft,
          canScrollRight,
          isScrolling: false,
        });
      }
    }, 50);

    return () => clearTimeout(timer);
  }, []); // 移除依赖，使用内联逻辑

  // 判断是否需要显示垂直滚动条
  const shouldShowVerticalScrollbar = useMemo(() => {
    const currentPageRowCount = table.getRowModel().rows.length;
    const pageSize = (pagination || tableState.pagination).pageSize;
    const totalDataCount = table.getFilteredRowModel().rows.length;

    if (pageSize === 10) {
      return totalDataCount > pageSize;
    } else {
      return currentPageRowCount >= pageSize;
    }
  }, [table, pagination, tableState.pagination]);

  // 渲染排序指示器
  const renderSortIndicator = useCallback((column: any) => {
    if (!column.getCanSort()) return null;

    const sorted = column.getIsSorted();
    
    return (
      <span className={`sort-indicator ${sorted ? 'active' : ''}`}>
        {sorted === 'desc' ? (
          <CaretDownOutlined />
        ) : sorted === 'asc' ? (
          <CaretUpOutlined />
        ) : (
          <CaretUpOutlined style={{ opacity: 0.3 }} />
        )}
      </span>
    );
  }, []);

  // 处理行点击事件
  const handleRowClick = useCallback((row: TData) => {
    if (events?.onRowClick) {
      events.onRowClick(row);
    }
  }, [events]);

  // 处理行双击事件
  const handleRowDoubleClick = useCallback((row: TData) => {
    if (events?.onRowDoubleClick) {
      events.onRowDoubleClick(row);
    }
  }, [events]);

  // 处理分页变化
  const handlePaginationChange = useCallback((page: number, pageSize: number) => {
    const newPagination = {
      pageIndex: page - 1,
      pageSize,
    };

    if (events?.onPageChange) {
      events.onPageChange(newPagination.pageIndex, pageSize);
    } else {
      tableState.setPagination(newPagination);
    }
  }, [events, tableState]);

  // DOM 测量和窗口大小变化监听
  useEffect(() => {
    const performMeasurement = () => {
      setTimeout(() => {
        if (tableContainerRef.current && tableHeaderRef.current) {
          const container = tableContainerRef.current;
          const header = tableHeaderRef.current;

          const viewportHeight = window.innerHeight;
          const containerRect = container.getBoundingClientRect();
          const availableHeight = viewportHeight - containerRect.top - 50;

          const headerHeight = header.offsetHeight;
          const rowHeight = tableConfig.rowHeight;
          const paginationHeight = showPagination ? tableConfig.paginationHeight : 0;

          const newDimensions = {
            containerHeight: availableHeight,
            headerHeight,
            rowHeight,
            paginationHeight,
            availableHeight: availableHeight - headerHeight - paginationHeight,
          };

          setMeasuredDimensions(newDimensions);
        }
      }, 100);
    };

    performMeasurement();

    const handleResize = () => {
      debouncedMeasure();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [debouncedMeasure]);

  // 当数据变化时重新测量
  useEffect(() => {
    if (table.getRowModel().rows.length > 0) {
      setTimeout(() => {
        // 内联测量逻辑，避免依赖函数引用
        if (tableContainerRef.current && tableHeaderRef.current) {
          const container = tableContainerRef.current;
          const header = tableHeaderRef.current;

          const viewportHeight = window.innerHeight;
          const containerRect = container.getBoundingClientRect();
          const availableHeight = viewportHeight - containerRect.top - 50;

          // 测量表头高度
          const headerHeight = header.getBoundingClientRect().height;

          // 测量行高度
          let rowHeight = tableConfig.rowHeight;
          if (tableRowRef.current) {
            const measuredRowHeight = tableRowRef.current.getBoundingClientRect().height;
            if (measuredRowHeight > 0) {
              rowHeight = measuredRowHeight;
            }
          }

          const newDimensions: TableDimensions = {
            containerHeight: availableHeight,
            headerHeight,
            rowHeight,
            availableHeight: Math.max(availableHeight - headerHeight, 200),
          };

          setMeasuredDimensions(newDimensions);
        }

        // 内联滚动状态检查
        const container = scrollContainerRef.current;
        if (container) {
          const { scrollLeft, scrollWidth, clientWidth } = container;
          const canScrollLeft = scrollLeft > 0;
          const canScrollRight = scrollLeft < scrollWidth - clientWidth - 1;

          setScrollState({
            canScrollLeft,
            canScrollRight,
            isScrolling: false,
          });
        }
      }, 50);
    }
  }, [table.getRowModel().rows.length, tableConfig.rowHeight]);

  // 监听窗口大小变化，更新滚动状态
  useEffect(() => {
    const handleResize = () => {
      setTimeout(() => {
        const container = scrollContainerRef.current;
        if (container) {
          const { scrollLeft, scrollWidth, clientWidth } = container;
          const canScrollLeft = scrollLeft > 0;
          const canScrollRight = scrollLeft < scrollWidth - clientWidth - 1;

          setScrollState({
            canScrollLeft,
            canScrollRight,
            isScrolling: false,
          });
        }
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []); // 移除依赖，使用内联逻辑

  const currentPagination = pagination || tableState.pagination;
  const filteredRowCount = table.getFilteredRowModel().rows.length;
  const currentPageRows = table.getRowModel().rows;

  return (
    <div className={`tanstack-table ${className}`} style={style}>
      {/* 表格容器 */}
      <div
        ref={tableContainerRef}
        className="table-container"
      >
        <Spin spinning={loading} tip="加载中...">
          <div className="table-wrapper">
            <div
              ref={scrollContainerRef}
              className={`table-scroll-container ${
                shouldShowVerticalScrollbar ? 'has-vertical-scroll' : 'no-vertical-scroll'
              }`}
              style={{
                height: getTableHeight(),
                minHeight: getTableHeight(),
              }}
              onScroll={handleScroll}
            >
              <table
                className="data-table"
                style={{
                  minWidth: getTableMinWidth(),
                }}
              >
                {/* 表头 */}
                <thead ref={tableHeaderRef}>
                  {table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map(header => (
                        <th
                          key={header.id}
                          className={header.column.getCanSort() ? 'sortable' : ''}
                          style={{
                            width: header.getSize(),
                            height: tableConfig.headerHeight,
                            position: 'sticky',
                            top: 0,
                            zIndex: 1,
                          }}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          <div className="header-content">
                            <span>
                              {header.isPlaceholder
                                ? null
                                : flexRender(header.column.columnDef.header, header.getContext())
                              }
                            </span>
                            {renderSortIndicator(header.column)}
                          </div>
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>

                {/* 表体 */}
                <tbody>
                  {currentPageRows.length === 0 ? (
                    <tr>
                      <td
                        colSpan={columns.length}
                        className="empty-row"
                        style={{ height: tableConfig.emptyStateHeight }}
                      >
                        <div className="empty-content">
                          {emptyContent || (
                            <Empty
                              description="暂无数据"
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                            />
                          )}
                        </div>
                      </td>
                    </tr>
                  ) : (
                    currentPageRows.map((row, index) => (
                      <tr
                        key={row.id}
                        ref={index === 0 ? tableRowRef : undefined}
                        className={index % 2 === 0 ? 'even' : 'odd'}
                        style={{ height: tableConfig.rowHeight }}
                        onClick={() => handleRowClick(row.original)}
                        onDoubleClick={() => handleRowDoubleClick(row.original)}
                      >
                        {row.getVisibleCells().map(cell => (
                          <td key={cell.id}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </td>
                        ))}
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </Spin>
      </div>

      {/* 分页组件 */}
      {showPagination && (
        <div
          className="pagination-container"
          style={{
            '--pagination-height': `${tableConfig.paginationHeight}px`,
            height: tableConfig.paginationHeight,
            minHeight: tableConfig.paginationHeight,
          } as React.CSSProperties}
        >
          <div className="pagination-info">
            <span className="record-range">
              第 {Math.min((currentPagination.pageIndex * currentPagination.pageSize) + 1, filteredRowCount)}-{Math.min((currentPagination.pageIndex + 1) * currentPagination.pageSize, filteredRowCount)} 条，共 {filteredRowCount} 条记录
            </span>
            {filteredRowCount !== data.length && (
              <span className="filter-info">
                (已筛选，原始数据 {data.length} 条)
              </span>
            )}
          </div>

          <div className="pagination-controls">
            <Select
              size="small"
              value={currentPagination.pageSize}
              onChange={(value) => handlePaginationChange(1, value)}
              style={{ width: 80, marginRight: 8 }}
            >
              {tableConfig.pageSizeOptions.map(size => (
                <Select.Option key={size} value={size}>
                  {size}条
                </Select.Option>
              ))}
            </Select>
            <Button
              size="small"
              disabled={!table.getCanPreviousPage()}
              onClick={() => table.previousPage()}
            >
              上一页
            </Button>
            <span className="page-info">
              第 {currentPagination.pageIndex + 1} 页，共 {table.getPageCount()} 页
            </span>
            <Button
              size="small"
              disabled={!table.getCanNextPage()}
              onClick={() => table.nextPage()}
            >
              下一页
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TanStackTable;

// 导出尺寸预设相关功能
export {
  SIZE_PRESETS,
  getSizePreset,
  getAvailableSizes,
  calculateTotalHeight,
} from './utils';

export type {
  TableSize,
  SizePresetConfig,
  TableConfig,
  TanStackTableProps,
  TableDimensions,
  HeightStrategy,
} from './types';