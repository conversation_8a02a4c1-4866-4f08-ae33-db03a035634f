import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>ton, Breadcrumb, Space, Badge, Tooltip, notification, Card } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { HeaderProps } from '../../types';
import { useResponsive } from '../../hooks/useResponsiveListener';
import UserAvatar from '../UserAvatar';
import TabsHistory from '../TabsHistory';
import ThemeToggle from '../ThemeToggle';

import './style.scss';

/**
 * 包含折叠按钮、面包屑、用户信息、通知等功能
 */
  const Header: React.FC<HeaderProps> = ({
    collapsed = false,
    onCollapse,
    user,
    title = 'Admin V2',
    showBreadcrumb = true,
    breadcrumbItems = [],
    breadcrumbComponent,
    actions
  }) => {
    const { isMobile } = useResponsive();
    const [api, contextHolder] = notification.useNotification();

    // 模拟通知数据
    const mockNotifications = [
      {
        id: 1,
        type: 'success' as const,
        title: '系统更新成功',
        message: '系统已成功更新至 v2.1.0，新增多项功能优化',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
        duration: 4.5,
      },
      {
        id: 2,
        type: 'info' as const,
        title: '新用户注册',
        message: '用户 "张三" 已成功注册，等待管理员审核',
        icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
        duration: 4.5,
      },
      {
        id: 3,
        type: 'warning' as const,
        title: '存储空间不足',
        message: '当前存储使用率已达85%，建议及时清理数据',
        icon: <WarningOutlined style={{ color: '#faad14' }} />,
        duration: 6,
      },
      {
        id: 4,
        type: 'error' as const,
        title: '任务执行失败',
        message: '数据同步任务执行失败，请检查网络连接状态',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
        duration: 6,
      },
      {
        id: 5,
        type: 'info' as const,
        title: '定期备份提醒',
        message: '建议每周进行数据备份，确保数据安全',
        icon: <InfoCircleOutlined style={{ color: '#722ed1' }} />,
        duration: 4.5,
      },
    ];

    // 处理通知按钮点击 - 显示所有通知
  const handleNotificationClick = () => {
      // 延迟显示通知，创建瀑布式效果
      mockNotifications.forEach((notif, index) => {
        setTimeout(() => {
          api[notif.type]({
            message: notif.title,
            description: notif.message,
            icon: notif.icon,
            placement: 'topRight',
            duration: notif.duration,
          });
        }, index * 300); // 每个通知间隔300ms
      });
  };





  // 渲染面包屑
  const renderBreadcrumb = () => {
    if (!showBreadcrumb) {
      return null;
    }

    // 优先使用传入的面包屑组件
    if (breadcrumbComponent) {
      return (
        <div className="breadcrumb-container">
          {breadcrumbComponent}
        </div>
      );
    }

    // 兜底使用传统的breadcrumbItems
    if (breadcrumbItems.length === 0) {
      return null;
    }

    // 转换为新的items格式 - 只有首页可以点击
    const breadcrumbItemsFormatted = breadcrumbItems.map((item, index) => ({
      key: index,
      title: (
        <span>
          {item.icon && <span className="breadcrumb-icon">{item.icon}</span>}
          {item.path && item.key === 'home' ? (
            <Link to={item.path} className="breadcrumb-link">{item.title}</Link>
          ) : (
            <span className="breadcrumb-text">{item.title}</span>
          )}
        </span>
      )
    }));

    return (
      <div className="breadcrumb-container">
        {/* 面包屑导航 */}
        <Breadcrumb
          className="header-breadcrumb"
          items={breadcrumbItemsFormatted}
        />
      </div>
    );
  };

  // 处理用户退出登录
  const handleLogout = async () => {
    try {
      // 清除本地存储的用户信息和token
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('user');

      // 重定向到登录页
      window.location.href = '/login';
    } catch (error) {
      console.error('退出登录失败:', error);
      throw error; // 重新抛出错误，让UserAvatar组件显示错误信息
    }
  };

  // 处理用户菜单点击
  const handleUserMenuClick = (key: string) => {
    console.log('用户菜单点击:', key);
    // 这里可以添加路由跳转或其他逻辑
  };

  // 渲染用户信息 - 使用独立的UserAvatar组件
  const renderUserInfo = () => {
    return (
      <UserAvatar
        user={user}
        onLogout={handleLogout}
        onMenuClick={handleUserMenuClick}
      />
    );
  };

  // 渲染操作按钮
  const renderActions = () => {
    return (
      <Space size="small" className="header-actions">
        {/* 通知按钮 - 使用弹窗式通知 */}
        <Tooltip title="通知">
          <Badge dot>
            <Button
              type="text"
              icon={<BellOutlined />}
              className="action-button"
              onClick={handleNotificationClick}
            />
          </Badge>
        </Tooltip>

        {/* 主题切换按钮 */}
        <ThemeToggle className="action-button" />

        {/* 设置按钮 */}
        <Tooltip title="设置">
          <Button
            type="text"
            icon={<SettingOutlined />}
            className="action-button"
          />
        </Tooltip>

        {/* 自定义操作 */}
        {actions}

        {/* 用户信息 */}
        {renderUserInfo()}
      </Space>
    );
  };

  return (
    <Card title="导航栏" variant="borderless">
      {contextHolder}
      {/* 顶部导航栏 */}
      <div className="v2-header">
        <div className="header-left">
          {/* 折叠按钮 */}
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => onCollapse?.(!collapsed)}
            className="collapse-button"
          />

          {/* 标题和面包屑 */}
          <div className="header-content">
            {isMobile ? (
              <span className="header-title">{title}</span>
            ) : (
              renderBreadcrumb()
            )}
          </div>
        </div>

        <div className="header-right">
          {renderActions()}
        </div>
      </div>

      {/* 独立的标签页历史记录区域 */}
      <div className="tabs-history-area">
        <TabsHistory />
      </div>
    </Card>
  );
};

export default Header;