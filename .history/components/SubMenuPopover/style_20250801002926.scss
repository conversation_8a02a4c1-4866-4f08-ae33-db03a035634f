/**
 * V2 Admin 子菜单弹出组件样式
 * 独立的、可重用的组件样式
 */
@use '../../styles/variables' as *;

.submenu-popover {
  position: fixed !important;
  z-index: 9999 !important;
  background: $background-color-white;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  border: 1px solid $border-color-split;
  min-width: 160px;
  max-width: 240px;
  animation: submenuFadeIn 0.2s ease-out;
  display: block !important;
  overflow: visible; // 确保箭头可见

  // 暗色主题
  &.dark {
    background: $dark-component-background;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.2);

    .submenu-popover-arrow {
      &::before {
        background: $dark-component-background;
        border-color: rgba(255, 255, 255, 0.1);
        box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.2);
      }

      &::after {
        background: $dark-component-background;
      }
    }
  }

  // 箭头指示器 - 改进设计，更加一体化
  .submenu-popover-arrow {
    position: absolute;
    left: -6px;
    top: 16px;
    width: 12px;
    height: 12px;
    z-index: 1;

    // 使用伪元素创建更精致的箭头
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 12px;
      height: 12px;
      background: $background-color-white;
      border: 1px solid $border-color-split;
      border-right: none;
      border-bottom: none;
      transform: rotate(-45deg);
      box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05);
    }

    // 遮盖多余的边框
    &::after {
      content: '';
      position: absolute;
      left: 2px;
      top: 0;
      width: 10px;
      height: 12px;
      background: $background-color-white;
      z-index: 2;
    }
  }

  .submenu-popover-content {
    padding: 0;
    overflow: hidden;
    position: relative;
    z-index: 2;
  }

  .submenu-popover-menu {
    border: none !important;
    background: transparent !important;
    padding: 4px 0;

    .ant-menu-item {
      height: 40px;
      line-height: 40px;
      margin: 0;
      padding: 0 16px;
      border-radius: 0;
      display: flex;
      align-items: center;

      // 图标样式
      .anticon {
        font-size: 16px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .submenu-item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .submenu-item-label {
        flex: 1;
        font-size: $font-size-base;
      }

      .submenu-item-badge {
        background: $error-color;
        color: white;
        font-size: $font-size-sm;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        margin-left: 8px;
        flex-shrink: 0;
      }

      // 悬停状态
      &:hover {
        background: $item-hover-bg;
        color: $primary-color;
      }

      // 选中状态
      &.ant-menu-item-selected {
        background: $primary-color-bg;
        color: $primary-color;

        &::after {
          display: none;
        }
      }
    }
  }

  // 暗色主题下的菜单项
  &.dark .submenu-popover-menu {
    .ant-menu-item {
      color: rgba(255, 255, 255, 0.85);

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        color: $primary-color;
      }

      &.ant-menu-item-selected {
        background: rgba(24, 144, 255, 0.2);
        color: $primary-color;
      }
    }
  }
}

// 动画效果 - 改进版本，更加流畅和专业
@keyframes submenuFadeIn {
  from {
    opacity: 0;
    transform: translateX(-8px) scale(0.95);
    filter: blur(1px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

// 响应式适配
@media (max-width: $breakpoint-sm) {
  .submenu-popover {
    display: none; // 移动端不显示弹出菜单
  }
}
