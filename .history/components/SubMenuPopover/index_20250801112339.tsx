import React, { useEffect, useRef } from 'react';
import { Menu } from 'antd';
import { MenuItem } from '../../types';
import './style.scss';

export interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
}

/**
 * 独立的子菜单弹出组件
 * 用于侧边栏折叠状态下显示子菜单选项
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme,
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [clickedItemKey, setClickedItemKey] = useState<string | null>(null);

  // 处理点击外部关闭和ESC键关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [visible, onClose]);

  // 处理鼠标进入弹出菜单 - 取消关闭定时器
  // 🎯 优化的鼠标进入处理
  const handleMouseEnter = useCallback(() => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
    // 通知 Zustand 鼠标在弹出层内
    setIsHovering(true);
  }, [setIsHovering]);

  // 🎯 优化的鼠标离开处理 - 增加延迟时间
  const handleMouseLeave = useCallback(() => {
    setIsHovering(false);
    closeTimeoutRef.current = setTimeout(() => {
      onClose();
    }, 450); // 🎯 增加延迟时间，与侧边栏保持一致
  }, [onClose, setIsHovering]);

  // 处理菜单项点击
  const handleMenuItemClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) {
          return item;
        }
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        onMenuClick(key, clickedItem.path);
        onClose();
      }
    }
  };

  // 转换子菜单项为Ant Design Menu items格式
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      disabled: item.disabled,
      label: (
        <div className="submenu-item-content">
          <span className="submenu-item-label">{item.label}</span>
          {item.badge && (
            <span className="submenu-item-badge">{item.badge}</span>
          )}
        </div>
      ),
    }));
  };

  if (!visible || !menuItem?.children) {
    return null;
  }

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div className="submenu-popover-arrow"></div>

      <div className="submenu-popover-content">
        <Menu
          mode="vertical"
          theme={theme}
          className="submenu-popover-menu"
          onClick={handleMenuItemClick}
          items={convertToMenuItems(menuItem.children)}
          style={{
            border: 'none',
            background: 'transparent',
            padding: '4px 0'
          }}
        />
      </div>
    </div>
  );
};

export default SubMenuPopover;
