import React, { useEffect, useRef, useState } from 'react';
import { Menu } from 'antd';
import { MenuItem } from '../../types';
import './style.scss';

export interface SubMenuPopoverProps {
  visible: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  position: { top: number; left: number };
  onMenuClick: (key: string, path?: string) => void;
  theme: 'light' | 'dark';
}

/**
 * 独立的子菜单弹出组件
 * 用于侧边栏折叠状态下显示子菜单选项
 */
const SubMenuPopover: React.FC<SubMenuPopoverProps> = ({
  visible,
  onClose,
  menuItem,
  position,
  onMenuClick,
  theme,
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 🎯 添加点击状态管理
  const [clickedItemKey, setClickedItemKey] = useState<string | null>(null);
  const [isClosing, setIsClosing] = useState(false);

  // 🔧 添加鼠标状态跟踪
  const [isMouseInside, setIsMouseInside] = useState(false);
  const mouseInsideRef = useRef(false);

  // 🔧 调试系统
  const debugRef = useRef({
    eventCount: 0,
    lastEvent: '',
    timers: new Set<NodeJS.Timeout>()
  });

  const debugLog = (event: string, details?: any) => {
    if (process.env.NODE_ENV === 'development') {
      debugRef.current.eventCount++;
      debugRef.current.lastEvent = event;
      console.log(`[SubMenuPopover] ${debugRef.current.eventCount}: ${event}`, details);
    }
  };

  // 处理点击外部关闭和ESC键关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [visible, onClose]);

  // 🔧 添加内部元素事件监听器 - 解决事件冒泡问题
  useEffect(() => {
    if (!visible || !popoverRef.current) return;

    const popoverElement = popoverRef.current;

    // 🔧 监听所有内部鼠标事件，确保鼠标在弹出层内时不会关闭
    const handleInternalMouseEnter = (event: Event) => {
      event.stopPropagation();
      mouseInsideRef.current = true;
      setIsMouseInside(true);

      // 清除任何关闭定时器
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }

      // 通知父组件鼠标在弹出层内
      const customEvent = new CustomEvent('submenu-popover-enter', {
        detail: {
          timestamp: Date.now(),
          source: 'internal'
        }
      });
      document.dispatchEvent(customEvent);
    };

    const handleInternalMouseLeave = (event: Event) => {
      const mouseEvent = event as MouseEvent;
      const relatedTarget = mouseEvent.relatedTarget as HTMLElement;

      // 检查是否真的离开了弹出层
      if (relatedTarget && popoverElement.contains(relatedTarget)) {
        // 仍在弹出层内，不处理
        return;
      }

      mouseInsideRef.current = false;
      setIsMouseInside(false);

      // 延迟检查，确保不是快速移动导致的误触发
      setTimeout(() => {
        if (!mouseInsideRef.current) {
          // 真正离开了弹出层
          const customEvent = new CustomEvent('submenu-popover-leaving', {
            detail: {
              timestamp: Date.now(),
              source: 'internal'
            }
          });
          document.dispatchEvent(customEvent);
        }
      }, 50);
    };

    // 🔧 使用事件委托监听所有内部元素
    popoverElement.addEventListener('mouseenter', handleInternalMouseEnter, true);
    popoverElement.addEventListener('mouseleave', handleInternalMouseLeave, true);

    // 🔧 额外监听 mouseover 和 mouseout 事件作为备用
    popoverElement.addEventListener('mouseover', handleInternalMouseEnter, true);
    popoverElement.addEventListener('mouseout', handleInternalMouseLeave, true);

    return () => {
      popoverElement.removeEventListener('mouseenter', handleInternalMouseEnter, true);
      popoverElement.removeEventListener('mouseleave', handleInternalMouseLeave, true);
      popoverElement.removeEventListener('mouseover', handleInternalMouseEnter, true);
      popoverElement.removeEventListener('mouseout', handleInternalMouseLeave, true);
    };
  }, [visible]);

  // 🔧 修复鼠标进入弹出菜单 - 立即取消所有关闭定时器
  const handleMouseEnter = () => {
    mouseInsideRef.current = true;
    setIsMouseInside(true);

    // 清除弹出层自己的关闭定时器
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }

    // 🔧 关键修复：通知父组件取消其关闭定时器
    const event = new CustomEvent('submenu-popover-enter', {
      detail: {
        timestamp: Date.now(),
        source: 'container'
      }
    });
    document.dispatchEvent(event);
  };

  // 🔧 修复鼠标离开弹出菜单 - 增强的精确检测
  const handleMouseLeave = (event: React.MouseEvent) => {
    const relatedTarget = event.relatedTarget as HTMLElement;

    // 🔧 首先检查内部状态
    if (mouseInsideRef.current) {
      // 如果内部监听器认为鼠标还在内部，不处理
      return;
    }

    mouseInsideRef.current = false;
    setIsMouseInside(false);

    // 检查鼠标是否移向侧边栏或其子元素
    const sidebar = document.querySelector('.v2-sidebar');
    const isMovingToSidebar = sidebar && (
      sidebar.contains(relatedTarget) ||
      sidebar === relatedTarget
    );

    // 检查是否移向弹出层内的其他元素
    const popover = popoverRef.current;
    const isMovingWithinPopover = popover && (
      popover.contains(relatedTarget) ||
      popover === relatedTarget
    );

    // 🔧 检查是否移向连接区域
    let isMovingToConnectionArea = false;
    if (relatedTarget && sidebar && popover) {
      const sidebarRect = sidebar.getBoundingClientRect();
      const popoverRect = popover.getBoundingClientRect();
      const mouseX = event.clientX;
      const mouseY = event.clientY;

      // 扩大连接区域范围
      isMovingToConnectionArea = mouseX >= sidebarRect.right - 5 &&
                               mouseX <= popoverRect.left + 10 &&
                               mouseY >= Math.min(sidebarRect.top, popoverRect.top) - 30 &&
                               mouseY <= Math.max(sidebarRect.bottom, popoverRect.bottom) + 30;
    }

    // 只有当鼠标真正离开所有相关区域时才关闭
    if (!isMovingToSidebar && !isMovingWithinPopover && !isMovingToConnectionArea) {
      // 🔧 双重延迟检查机制
      setTimeout(() => {
        if (!mouseInsideRef.current) {
          // 通知全局监听器弹出层即将关闭
          const customEvent = new CustomEvent('submenu-popover-leaving', {
            detail: {
              timestamp: Date.now(),
              source: 'container'
            }
          });
          document.dispatchEvent(customEvent);

          closeTimeoutRef.current = setTimeout(() => {
            // 最终检查
            if (!mouseInsideRef.current) {
              onClose();
            }
          }, 100);
        }
      }, 50); // 第一层延迟
    }
  };

  // 🎯 优化菜单项点击处理 - 添加延迟关闭和视觉反馈
  const handleMenuItemClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[], targetKey: string): MenuItem | null => {
      for (const item of items) {
        if (item.key === targetKey) {
          return item;
        }
        if (item.children) {
          const found = findMenuItem(item.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    if (menuItem?.children) {
      const clickedItem = findMenuItem(menuItem.children, key);
      if (clickedItem?.path) {
        // 🎯 立即设置点击状态，提供视觉反馈
        setClickedItemKey(key);
        setIsClosing(true);

        // 🎯 清除任何现有的关闭定时器
        if (closeTimeoutRef.current) {
          clearTimeout(closeTimeoutRef.current);
          closeTimeoutRef.current = null;
        }

        // 🎯 立即执行导航
        onMenuClick(key, clickedItem.path);

        // 🎯 延迟关闭弹出层，给用户足够的视觉反馈时间
        closeTimeoutRef.current = setTimeout(() => {
          onClose();
          // 重置状态
          setClickedItemKey(null);
          setIsClosing(false);
        }, 400); // 400ms 延迟，提供良好的用户体验
      }
    }
  };

  // 🎯 优化子菜单项转换 - 添加点击状态和加载反馈
  const convertToMenuItems = (items: MenuItem[]) => {
    return items.map(item => {
      const isClicked = clickedItemKey === item.key;
      const isItemDisabled = item.disabled || isClosing;

      return {
        key: item.key,
        icon: item.icon,
        disabled: isItemDisabled,
        className: `submenu-menu-item ${isClicked ? 'submenu-item-clicked' : ''} ${isClosing && !isClicked ? 'submenu-item-dimmed' : ''}`,
        label: (
          <div className="submenu-item-content">
            <span className="submenu-item-label">{item.label}</span>
            {item.badge && (
              <span className="submenu-item-badge">{item.badge}</span>
            )}
            {/* 🎯 添加加载指示器 */}
            {isClicked && (
              <span className="submenu-item-loading">
                <span className="loading-dot"></span>
                <span className="loading-dot"></span>
                <span className="loading-dot"></span>
              </span>
            )}
          </div>
        ),
      };
    });
  };

  if (!visible || !menuItem?.children) {
    return null;
  }

  return (
    <div
      ref={popoverRef}
      className={`submenu-popover ${theme} ${isClosing ? 'submenu-popover-closing' : ''}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div className="submenu-popover-arrow"></div>

      <div className="submenu-popover-content">
        <Menu
          mode="vertical"
          theme={theme}
          className="submenu-popover-menu"
          onClick={handleMenuItemClick}
          items={convertToMenuItems(menuItem.children)}
          style={{
            border: 'none',
            background: 'transparent',
            padding: '4px 0'
          }}
        />
      </div>
    </div>
  );
};

export default SubMenuPopover;
