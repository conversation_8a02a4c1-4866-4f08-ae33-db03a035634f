import React from 'react';
import {
  DashboardOutlined,
  DatabaseOutlined,
  UserOutlined,
  SettingOutlined,
  MonitorOutlined,
  BookOutlined,
  RobotOutlined,
  BarChartOutlined,
  TeamOutlined,
  SafetyOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  EditOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import { RouteConfig } from '../types';

// 懒加载组件
const DashboardV2 = React.lazy(() => import('../pages/DashboardV2'));
const SubMenuTest = React.lazy(() => import('../pages/SubMenuTest'));
const LoginLogsPage = React.lazy(() => import('../pages/LoginLogsPage'));
const OperationLogsPage = React.lazy(() => import('../pages/OperationLogsPage'));
const SystemLogsPage = React.lazy(() => import('../pages/SystemLogsPage'));
const AssistantManagement = React.lazy(() => import('../pages/AssistantManagement'));
const KnowledgeBaseManagement = React.lazy(() => import('../pages/KnowledgeBaseManagement'));
const UserListManagement = React.lazy(() => import('../pages/UserListManagement'));
const OnlineUsersManagement = React.lazy(() => import('../pages/OnlineUsersManagement'));
const ModelManagementV2 = React.lazy(() => import('../pages/ModelManagementV2'));
const FAQManagement = React.lazy(() => import('../pages/SystemSettings/FAQManagement'));
const Redirect = React.lazy(() => import('../pages/Redirect'));
const FileMiddlewareSettings = React.lazy(() => import('../pages/SystemSettings/FileMiddlewareSettings'));
const GeneralSettings = React.lazy(() => import('../pages/SystemSettings/GeneralSettings'));
const TitleGenerationSettings = React.lazy(() => import('../pages/SystemSettings/TitleGenerationSettings'));
const CaptchaSettings = React.lazy(() => import('../pages/SystemSettings/CaptchaSettings'));
const SystemStatusPage = React.lazy(() => import('../pages/SystemStatusPage'));

// 临时占位页面组件
const PlaceholderPage: React.FC<{ title: string }> = ({ title }) => (
  <div style={{ 
    padding: 24, 
    background: '#fff', 
    borderRadius: 8,
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    minHeight: 400,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    gap: 16
  }}>
    <h1 style={{ fontSize: 24, margin: 0, color: '#262626' }}>{title}</h1>
    <p style={{ color: '#8c8c8c', margin: 0 }}>
      此页面正在开发中，将在后续版本中完善功能
    </p>
  </div>
);

/**
 * 层次化路由配置
 * 使用嵌套结构组织路由，与菜单结构保持一致
 */
export const routeConfig: RouteConfig[] = [
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/dashboard',
    element: DashboardV2,
    meta: {
      title: '仪表板',
      icon: <DashboardOutlined />,
      description: '系统概览和数据统计',
      keywords: ['dashboard', '仪表板', '概览'],
    },
  },
  {
    path: '/models',
    element: ModelManagementV2,
    meta: {
      title: '模型管理',
      icon: <DatabaseOutlined />,
      description: 'AI模型配置和管理',
      keywords: ['model', '模型', 'AI'],
    },
  },
  // 系统监控父级路由
  {
    path: '/monitoring',
    meta: {
      title: '系统监控',
      icon: <MonitorOutlined />,
      description: '系统状态监控和日志管理',
    },
  },
  // 系统监控子路由
  {
    path: '/monitoring/system-status',
    element: SystemStatusPage,
    meta: {
      title: '系统状态',
      icon: <BarChartOutlined />,
      description: '系统运行状态监控',
    },
  },
  {
    path: '/monitoring/login-logs',
    element: LoginLogsPage,
    meta: {
      title: '登录日志',
      icon: <UserOutlined />,
      description: '用户登录日志查询',
    },
  },
  {
    path: '/monitoring/operation-logs',
    element: OperationLogsPage,
    meta: {
      title: '操作日志',
      icon: <FileTextOutlined />,
      description: '用户操作日志记录',
    },
  },
  {
    path: '/monitoring/system-logs',
    element: SystemLogsPage,
    meta: {
      title: '系统日志',
      icon: <FileTextOutlined />,
      description: '系统运行日志',
    },
  },
  // 用户管理父级路由
  {
    path: '/users',
    meta: {
      title: '用户管理',
      icon: <UserOutlined />,
      description: '用户信息和权限管理',
    },
  },
  // 用户管理子路由
  {
    path: '/users/list',
    element: UserListManagement,
    meta: {
      title: '用户管理',
      icon: <TeamOutlined />,
      description: '基于 TanStack Table 的现代化用户管理界面',
    },
  },
  {
    path: '/users/online',
    element: OnlineUsersManagement,
    meta: {
      title: '在线用户',
      icon: <UserOutlined />,
      description: '在线用户监控',
    },
  },


  {
    path: '/assistants',
    element: AssistantManagement,
    meta: {
      title: '助手管理',
      icon: <RobotOutlined />,
      description: 'AI助手配置和管理',
      keywords: ['assistant', '助手', 'AI'],
    },
  },
  {
    path: '/knowledge-base',
    element: KnowledgeBaseManagement,
    meta: {
      title: '知识库管理',
      icon: <BookOutlined />,
      description: '知识库内容管理',
      keywords: ['knowledge', '知识库', '文档'],
    },
  },
  // 系统设置父级路由
  {
    path: '/settings',
    meta: {
      title: '系统设置',
      icon: <SettingOutlined />,
      description: '系统配置和参数设置',
    },
  },
  // 系统设置子路由
  {
    path: '/settings/general',
    element: GeneralSettings,
    meta: {
      title: '基础设置',
      icon: <SettingOutlined />,
      description: '系统基础配置',
    },
  },
  {
    path: '/settings/title-generation',
    element: TitleGenerationSettings,
    meta: {
      title: '标题生成',
      icon: <EditOutlined />,
      description: '标题生成配置',
    },
  },
  {
    path: '/settings/captcha',
    element: CaptchaSettings,
    meta: {
      title: '验证码设置',
      icon: <SafetyCertificateOutlined />,
      description: '验证码配置',
    },
  },
  {
    path: '/settings/security',
    element: () => <PlaceholderPage title="安全设置" />,
    meta: {
      title: '安全设置',
      icon: <SafetyOutlined />,
      description: '系统安全配置',
    },
  },
  {
    path: '/settings/faq',
    element: FAQManagement,
    meta: {
      title: 'FAQ管理',
      icon: <QuestionCircleOutlined />,
      description: '常见问题管理',
    },
  },
  {
    path: '/settings/file',
    element: FileMiddlewareSettings,
    meta: {
      title: '文件设置',
      icon: <FileTextOutlined />,
      description: '文件中间件配置',
    },
  },
  // 重定向路由 - 用于刷新标签页
  {
    path: '/redirect/*',
    element: Redirect,
    meta: {
      title: '重定向',
      hideInMenu: true,
    },
  },
];



export default routeConfig;
