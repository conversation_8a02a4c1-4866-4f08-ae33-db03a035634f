import React, { useEffect } from "react";
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Space,
  Row,
  Col,
  Spin,
  Alert,
  Select,
  Tooltip,
  Card,
} from "antd";
import {
  InfoCircleOutlined,
  SaveOutlined,
  SettingOutlined,
  EditOutlined,
  ThunderboltOutlined
} from "@ant-design/icons";
import { useTitleGenerationSettingsStore } from "../../stores";
import type { TitleGenerationSettingsData } from "../../stores/settings/titleGenerationSettingsSlice";
import { CARD_STANDARD_PADDING_STYLES } from "../../src/constants/antdStyles";
import "./style.scss";

const { TextArea } = Input;
const { Option } = Select;

interface TitleGenerationSettingsProps {
  onFormChange?: () => void;
  onSave?: () => void;
  saving?: boolean;
}

/**
 * 标题生成设置组件 - V2 Admin版本
 * 适配v2-admin设计系统
 */
const TitleGenerationSettingsComponent: React.FC<
  TitleGenerationSettingsProps
> = ({ onFormChange, onSave, saving = false }) => {
  const [form] = Form.useForm<TitleGenerationSettingsData>();

  // 使用 Zustand store
  const {
    titleGenerationSettings,
    titleGenerationLoading,
    titleGenerationSaving,
    titleGenerationError,
    titleGenerationIsDirty,
    models,
    loadingModels,
    testResult,
    testing,
    loadTitleGenerationSettings,
    updateTitleGenerationSettings,
    markTitleGenerationDirty,
    getTitleGenerationDefaultSettings,
    loadModels,
    testTitleGeneration,
  } = useTitleGenerationSettingsStore();

  // 保存设置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      await updateTitleGenerationSettings(values);
      markTitleGenerationDirty(false);

      if (onFormChange) {
        onFormChange();
      }
    } catch (error: any) {
      if (error.errorFields) {
        // Ant Design 表单验证错误
        const { message } = await import('antd');
        message.error("请检查表单输入");
      }
      // API 错误已在 store 中处理
    }
  };

  // 测试标题生成
  const handleTestTitleGeneration = async () => {
    const testContent = "用户：你好，我想了解一下人工智能的发展历史。\n助手：人工智能的发展历史可以追溯到20世纪50年代...";
    await testTitleGeneration(testContent);
  };

  // 表单值变化时标记为脏数据
  const handleFormChange = () => {
    if (!titleGenerationIsDirty) {
      markTitleGenerationDirty(true);
    }
    if (onFormChange) {
      onFormChange();
    }
  };

  // 组件挂载时加载设置和模型
  useEffect(() => {
    loadTitleGenerationSettings();
    loadModels();
  }, [loadTitleGenerationSettings, loadModels]);

  // 当设置加载完成时，更新表单值
  useEffect(() => {
    if (titleGenerationSettings) {
      form.setFieldsValue(titleGenerationSettings);
      markTitleGenerationDirty(false);
    }
  }, [titleGenerationSettings, form, markTitleGenerationDirty]);

  return (
    <div className="title-generation-settings">
      <Spin spinning={titleGenerationLoading}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          initialValues={getTitleGenerationDefaultSettings()}
        >
          {/* 基础设置 */}
          <Card
            title={
              <Space>
                <SettingOutlined />
                基础设置
              </Space>
            }
            className="responsive-card-spacing"
            styles={CARD_STANDARD_PADDING_STYLES}
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="启用标题生成"
                  name="enabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  开启后，系统将自动为对话生成标题
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      模型选择
                      <Tooltip title="选择用于生成标题的模型，留空则使用会话模型">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="model_id"
                >
                  <Select
                    placeholder="选择标题生成模型（可选）"
                    allowClear
                    showSearch
                    loading={loadingModels}
                    filterOption={(input, option) =>
                      (option?.label as string)
                        ?.toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  >
                    {models.map((model) => (
                      <Option key={model.id} value={model.id}>
                        {model.name} ({model.provider})
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <div className="setting-description">
                  留空则使用当前会话的模型
                </div>
              </Col>
            </Row>
          </Card>

          {/* 提示词配置 */}
          <Card
            title={
              <Space>
                <EditOutlined />
                提示词配置
              </Space>
            }
            className="responsive-card-spacing"
            styles={CARD_STANDARD_PADDING_STYLES}
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Form.Item
                  label={
                    <Space>
                      提示词模板
                      <Tooltip title="用于生成标题的提示词模板，支持变量：{user_message}, {ai_response}">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="prompt_template"
                  rules={[{ required: true, message: "请输入提示词模板" }]}
                >
                  <TextArea
                    rows={8}
                    placeholder="请输入标题生成的提示词模板"
                  />
                </Form.Item>
                <div className="setting-description">
                  支持变量：{"{user_message}"} 用户消息，{"{ai_response}"} AI回复
                </div>
              </Col>
            </Row>
          </Card>

          {/* 生成参数 */}
          <Card
            title={
              <Space>
                <ThunderboltOutlined />
                生成参数
              </Space>
            }
            className="responsive-card-spacing"
            styles={CARD_STANDARD_PADDING_STYLES}
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      温度参数
                      <Tooltip title="控制生成文本的随机性，值越小越确定">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="temperature"
                  rules={[
                    { required: true, message: "请输入温度参数" },
                    { type: "number", min: 0, max: 2, message: "温度参数必须在0-2之间" }
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={2}
                    step={0.1}
                    placeholder="0.3"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：0.3，较低的值生成更稳定的标题
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      最大Token数
                      <Tooltip title="生成标题的最大Token数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="max_length"
                  rules={[
                    { required: true, message: "请输入最大Token数" },
                    { type: "number", min: 10, max: 200, message: "Token数必须在10-200之间" }
                  ]}
                >
                  <InputNumber
                    min={10}
                    max={200}
                    placeholder="50"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：50，足够生成简洁的标题
                </div>
              </Col>
            </Row>
          </Card>

          {/* 测试功能 */}
          <Card
            title={
              <Space>
                <ThunderboltOutlined />
                测试功能
              </Space>
            }
            className="responsive-card-spacing"
            styles={CARD_STANDARD_PADDING_STYLES}
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button
                    type="default"
                    icon={<ThunderboltOutlined />}
                    onClick={handleTestTitleGeneration}
                    loading={testing}
                    disabled={!titleGenerationSettings?.enabled || !titleGenerationSettings?.model_id}
                  >
                    测试标题生成
                  </Button>
                  {testResult && (
                    <Alert
                      message="测试结果"
                      description={`生成的标题：${testResult}`}
                      type="success"
                      showIcon
                    />
                  )}
                </Space>
              </Col>
            </Row>
          </Card>

          <div style={{ textAlign: "right" }}>
            <Space>
              {titleGenerationError && (
                <span style={{ color: '#ff4d4f', marginRight: 16 }}>
                  {titleGenerationError}
                </span>
              )}
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={onSave || handleSave}
                loading={saving || titleGenerationSaving}
                disabled={!titleGenerationIsDirty}
              >
                {titleGenerationIsDirty ? '保存设置' : '已保存'}
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </div>
  );
};

export default TitleGenerationSettingsComponent;
