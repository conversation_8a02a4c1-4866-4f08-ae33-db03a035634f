import React, { useEffect } from "react";
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Space,
  Row,
  Col,
  Spin,
  Tooltip,
  Card,
} from "antd";
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import { CARD_STANDARD_PADDING_STYLES } from '../../src/constants/antdStyles';
import {
  SaveOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { useSystemSettingsStore } from "../../stores";
import "./style.scss";

/**
 * 基础设置页面 - 合并基础设置、性能设置、超时设置
 */
const GeneralSettings: React.FC = () => {
  const [form] = Form.useForm();

  // 使用 Zustand store
  const {
    settings,
    loading,
    saving,
    error,
    isDirty,
    loadSettings,
    updateSettings,
    getDefaultSettings,
    markDirty,
  } = useSystemSettingsStore();

  // 保存设置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      await updateSettings(values);
      markDirty(false);
    } catch (error: any) {
      console.error("保存基础设置失败:", error);
      if (error.errorFields) {
        // Ant Design 表单验证错误
        const { message } = await import('antd');
        message.error("请检查表单输入");
      }
      // API 错误已在 store 中处理
    }
  };

  // 表单值变化时标记为脏数据
  const handleFormChange = () => {
    if (!isDirty) {
      markDirty(true);
    }
  };

  // 组件挂载时加载设置
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // 当设置加载完成时，更新表单值
  useEffect(() => {
    if (settings) {
      form.setFieldsValue(settings);
      markDirty(false);
    }
  }, [settings, form, markDirty]);

  // 页面动画配置
  const pageTransition = usePageTransition({
    type: AnimationType.FADE_IN_UP,
    duration: 0.6,
    delay: 0,
  });

  return (
    <motion.div
      {...pageTransition}
      className="general-settings"
    >
        <Spin spinning={loading}>

        <Form
          form={form}
          layout="vertical"
          initialValues={getDefaultSettings()}
          onValuesChange={handleFormChange}
        >
          {/* 系统基本信息 */}
          <Card
            title={
              <Space>
                <SettingOutlined />
                系统基本信息
              </Space>
            }
            className="responsive-card-spacing"
            styles={CARD_STANDARD_PADDING_STYLES}
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="系统名称"
                  name="system_name"
                  rules={[{ required: true, message: "请输入系统名称" }]}
                >
                  <Input placeholder="请输入系统名称" />
                </Form.Item>
                <div className="setting-description">
                  显示在系统界面中的名称
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="系统版本"
                  name="system_version"
                  rules={[{ required: true, message: "请输入系统版本" }]}
                >
                  <Input placeholder="请输入系统版本" />
                </Form.Item>
                <div className="setting-description">
                  当前系统的版本号
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="启用负载均衡"
                  name="load_balancing_enabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  启用后系统将自动分配请求到多个服务器
                </div>
              </Col>
            </Row>
          </Card>

          {/* 性能配置 */}
          <Card
            title={
              <Space>
                <ThunderboltOutlined />
                性能配置
              </Space>
            }
            className="unified-card responsive-card-spacing"
          >

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      默认页面大小
                      <Tooltip title="列表页面默认显示的记录数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="default_page_size"
                  rules={[
                    { required: true, message: "请输入默认页面大小" },
                    { type: "number", min: 1, max: 100, message: "默认页面大小必须在1-100之间" }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    placeholder="20"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：20，平衡性能和用户体验
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      最大页面大小
                      <Tooltip title="单次查询允许的最大记录数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="max_page_size"
                  rules={[
                    { required: true, message: "请输入最大页面大小" },
                    { type: "number", min: 1, max: 1000, message: "最大页面大小必须在1-1000之间" }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                    placeholder="100"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：100，防止单次查询数据过多
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      模型刷新间隔(秒)
                      <Tooltip title="自动刷新模型列表的时间间隔">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="model_refresh_interval"
                  rules={[
                    { required: true, message: "请输入模型刷新间隔" },
                    { type: "number", min: 30, max: 3600, message: "刷新间隔必须在30-3600秒之间" }
                  ]}
                >
                  <InputNumber
                    min={30}
                    max={3600}
                    placeholder="300"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：300秒（5分钟），避免频繁刷新
                </div>
              </Col>
            </Row>
          </Card>

          {/* 超时配置 */}
          <Card
            title={
              <Space>
                <ClockCircleOutlined />
                超时配置
              </Space>
            }
            className="unified-card responsive-card-spacing"
          >

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      API超时时间(秒)
                      <Tooltip title="API请求的超时时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="api_timeout"
                  rules={[
                    { required: true, message: "请输入API超时时间" },
                    { type: "number", min: 5, max: 300, message: "API超时时间必须在5-300秒之间" }
                  ]}
                >
                  <InputNumber
                    min={5}
                    max={300}
                    placeholder="30"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：30秒，适合大多数API调用
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      HTTP客户端超时(秒)
                      <Tooltip title="HTTP客户端请求的超时时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="http_client_timeout"
                  rules={[
                    { required: true, message: "请输入HTTP客户端超时时间" },
                    { type: "number", min: 5, max: 300, message: "HTTP客户端超时时间必须在5-300秒之间" }
                  ]}
                >
                  <InputNumber
                    min={5}
                    max={300}
                    placeholder="30"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：30秒，与API超时保持一致
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      流式上下文超时(秒)
                      <Tooltip title="流式响应的上下文超时时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="stream_context_timeout"
                  rules={[
                    { required: true, message: "请输入流式上下文超时时间" },
                    { type: "number", min: 30, max: 600, message: "流式上下文超时时间必须在30-600秒之间" }
                  ]}
                >
                  <InputNumber
                    min={30}
                    max={600}
                    placeholder="120"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：120秒，适合长时间的流式响应
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      模型列表超时(秒)
                      <Tooltip title="获取模型列表的超时时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="model_list_timeout"
                  rules={[
                    { required: true, message: "请输入模型列表超时时间" },
                    { type: "number", min: 5, max: 60, message: "模型列表超时时间必须在5-60秒之间" }
                  ]}
                >
                  <InputNumber
                    min={5}
                    max={60}
                    placeholder="10"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：10秒，快速获取模型列表
                </div>
              </Col>
            </Row>
          </Card>

          <div style={{ textAlign: "right" }}>
            <Space>
              {error && (
                <span style={{ color: '#ff4d4f', marginRight: 16 }}>
                  {error}
                </span>
              )}
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={saving}
                disabled={!isDirty}
              >
                {isDirty ? '保存设置' : '已保存'}
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </motion.div>
  );
};

export default GeneralSettings;
