import React from 'react';
import { Card, Progress, Tag, Divider, Statistic } from 'antd';
import {
  CARD_STANDARD_PADDING_STYLES,
  STATISTIC_CUSTOM_STYLES,
  PROGRESS_COMPACT_STYLES,
  TAG_CHECKABLE_STYLES,
  BORDERLESS_VARIANT
} from '../../src/constants/antdStyles';
import {
  RiseOutlined,
  FallOutlined,
  RobotOutlined,
  BookOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  PieChartOutlined,
  LineChartOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  ApiOutlined,
  // UserOutlined // 未使用
} from '@ant-design/icons';
import './style.scss';

/**
 * AI助手管理系统仪表板 - 基于flex布局的现代化仪表板
 */
const DashboardV2: React.FC = () => {
  // AI助手管理系统数据
  const statsData = [
    {
      title: '活跃助手',
      value: '24',
      icon: <RobotOutlined />,
      trend: { type: 'up', value: '15%', label: '本月新增' },
      subValue: '今日新增 3 个',
      color: '#1890ff'
    },
    {
      title: '知识库数量',
      value: '156',
      icon: <BookOutlined />,
      trend: { type: 'up', value: '8%', label: '本周新增' },
      subValue: '文档总数 12,456',
      color: '#52c41a'
    },
    {
      title: '对话总数',
      value: '28,560',
      icon: <MessageOutlined />,
      trend: { type: 'up', value: '23%', label: '日均增长' },
      subValue: '今日对话 1,234',
      color: '#faad14'
    },
    {
      title: '系统可用性',
      value: '99.8%',
      icon: <CheckCircleOutlined />,
      progress: 99.8,
      color: '#722ed1'
    }
  ];

  const assistantRankingData = [
    { rank: 1, name: '智能客服助手', usage: '15,234' },
    { rank: 2, name: '技术支持助手', usage: '12,456' },
    { rank: 3, name: '产品咨询助手', usage: '9,876' },
    { rank: 4, name: '文档问答助手', usage: '8,234' },
    { rank: 5, name: '代码助手', usage: '6,789' },
    { rank: 6, name: '营销助手', usage: '5,432' }
  ];

  const knowledgeBaseData = [
    { name: '技术文档', value: 35, color: '#1890ff' },
    { name: '产品手册', value: 28, color: '#52c41a' },
    { name: 'FAQ问答', value: 22, color: '#faad14' },
    { name: '政策法规', value: 15, color: '#f5222d' }
  ];

  return (
    <div className="dashboard-v2">
      {/* 顶部统计卡片区域 - 使用Statistic组件统一设计 */}
      <section className="stats-section">
        <div className="stats-grid">
          {statsData.map((stat, index) => (
            <Card
              key={index}
              className="stats-card"
              hoverable
              styles={CARD_STANDARD_PADDING_STYLES}
            >
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: stat.color }}
                styles={STATISTIC_CUSTOM_STYLES}
              />

              {/* 额外信息显示 */}
              <div className="stat-extra-info">
                {stat.progress !== undefined ? (
                  <div className="stat-progress">
                    <Progress
                      percent={stat.progress}
                      showInfo={false}
                      strokeColor={stat.color}
                      trailColor="#f0f0f0"
                      size="small"
                    />
                  </div>
                ) : (
                  <>
                    {stat.trend && (
                      <div className={`trend trend-${stat.trend.type}`} style={{ color: stat.color }}>
                        {stat.trend.type === 'up' ? <RiseOutlined /> : <FallOutlined />}
                        <span>{stat.trend.label} {stat.trend.value}</span>
                      </div>
                    )}
                    {stat.subValue && (
                      <div className="sub-value" style={{ color: '#8c8c8c' }}>
                        {stat.subValue}
                      </div>
                    )}
                  </>
                )}
              </div>
            </Card>
          ))}
        </div>
      </section>

      {/* 中间图表区域 - 响应式Grid布局 */}
      <section className="charts-section">
        <div className="charts-grid">
          {/* 主图表区域 */}
          <Card className="main-chart-card">
              <div className="chart-header">
                <div className="chart-title">
                  <LineChartOutlined />
                  <span>对话量趋势</span>
                </div>
                <div className="chart-controls">
                  <div className="chart-tabs">
                    <Tag color="blue">对话量</Tag>
                    <Tag>成功率</Tag>
                  </div>
                  <div className="time-filters">
                    <Tag.CheckableTag checked={true}>今日</Tag.CheckableTag>
                    <Tag.CheckableTag checked={false}>本周</Tag.CheckableTag>
                    <Tag.CheckableTag checked={false}>本月</Tag.CheckableTag>
                    <Tag.CheckableTag checked={false}>本年</Tag.CheckableTag>
                  </div>
                </div>
              </div>
              <Divider style={{ margin: '16px 0' }} />
              <div className="chart-content">
                <div className="chart-placeholder">
                  {/* 这里将来集成图表组件 */}
                  <div className="chart-mock">
                    <div className="chart-bars">
                      {[...Array(12)].map((_, i) => (
                        <div 
                          key={i} 
                          className="bar" 
                          style={{ height: `${20 + Math.random() * 60}%` }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </Card>

          {/* 排行榜 */}
          <Card className="ranking-card">
              <div className="card-header">
                <div className="card-title">
                  <BarChartOutlined />
                  <span>助手使用排行榜</span>
                </div>
              </div>
              <Divider style={{ margin: '16px 0' }} />
              <div className="ranking-content">
                {assistantRankingData.map((item, index) => (
                  <div key={index} className="ranking-item">
                    <div className={`rank-badge rank-${index < 3 ? index + 1 : 'other'}`}>
                      {item.rank}
                    </div>
                    <div className="store-info">
                      <div className="store-name">{item.name}</div>
                      <div className="store-sales">{item.usage} 次对话</div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
        </div>
      </section>

      {/* 底部数据区域 - 响应式Grid布局 */}
      <section className="bottom-section">
        <div className="bottom-grid">
          {/* 知识库类型分布 */}
          <Card className="category-card">
              <div className="card-header">
                <div className="card-title">
                  <PieChartOutlined />
                  <span>知识库类型分布</span>
                </div>
                <div className="channel-tabs">
                  <Tag color="blue">全部类型</Tag>
                  <Tag>文档型</Tag>
                  <Tag>问答型</Tag>
                </div>
              </div>
              <Divider style={{ margin: '16px 0' }} />
              <div className="category-content">
                <div className="pie-chart-placeholder">
                  {/* 这里将来集成饼图组件 */}
                  <div className="pie-mock">
                    <div className="pie-center">
                      <div className="total-label">总知识库</div>
                      <div className="total-value">156 个</div>
                    </div>
                  </div>
                </div>
                <div className="category-legend">
                  {knowledgeBaseData.map((item, index) => (
                    <div key={index} className="legend-item">
                      <div
                        className="legend-color"
                        style={{ backgroundColor: item.color }}
                      />
                      <span className="legend-name">{item.name}</span>
                      <span className="legend-value">{item.value}%</span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

          {/* AI模型状态监控 */}
          <Card className="online-stores-card">
              <div className="card-header">
                <div className="card-title">
                  <DatabaseOutlined />
                  <span>AI模型状态监控</span>
                </div>
                <div className="card-actions">
                  <Tag color="green">18 在线</Tag>
                  <Tag color="orange">3 维护中</Tag>
                </div>
              </div>
              <Divider style={{ margin: '16px 0' }} />
              <div className="stores-content">
                <div className="stores-grid">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="store-card">
                      <div className="store-avatar">
                        <ApiOutlined />
                      </div>
                      <div className="store-details">
                        <div className="store-name">GPT-{i + 1} 模型</div>
                        <div className="store-status">
                          <Tag color={i % 3 === 0 ? 'green' : i % 3 === 1 ? 'orange' : 'blue'}>
                            {i % 3 === 0 ? '运行中' : i % 3 === 1 ? '维护中' : '待部署'}
                          </Tag>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
        </div>
      </section>
    </div>
  );
};

export default DashboardV2;