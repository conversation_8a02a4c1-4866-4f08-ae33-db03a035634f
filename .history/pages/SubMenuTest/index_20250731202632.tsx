import React, { useState } from 'react';
import { Card, Button, Space, Typography, Alert, Switch } from 'antd';
import { 
  UserOutlined, 
  SettingOutlined, 
  TeamOutlined,
  SafetyOutlined,
  DatabaseOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import SubMenuPopover from '../../components/Sidebar/SubMenuPopover';
import { MenuItem } from '../../types';

const { Title, Paragraph } = Typography;

/**
 * SubMenuPopover 测试页面
 * 用于测试重构后的 SubMenuPopover 组件
 */
const SubMenuTest: React.FC = () => {
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState({ top: 200, left: 300 });
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [placement, setPlacement] = useState<'right' | 'left'>('right');

  // 测试菜单数据
  const testMenuItem: MenuItem = {
    key: 'user-management',
    label: '用户管理',
    icon: <UserOutlined />,
    children: [
      {
        key: 'user-list',
        label: '用户列表',
        icon: <TeamOutlined />,
        path: '/users/list',
      },
      {
        key: 'user-roles',
        label: '角色管理',
        icon: <SafetyOutlined />,
        path: '/users/roles',
        badge: '3',
      },
      {
        key: 'user-permissions',
        label: '权限管理',
        icon: <DatabaseOutlined />,
        path: '/users/permissions',
      },
      {
        key: 'user-logs',
        label: '操作日志',
        icon: <FileTextOutlined />,
        path: '/users/logs',
        disabled: true,
      },
    ],
  };

  // 处理菜单点击
  const handleMenuClick = (key: string, path?: string) => {
    console.log('菜单点击:', { key, path });
    alert(`点击了菜单项: ${key}, 路径: ${path}`);
  };

  // 处理按钮点击，显示 Popover
  const handleShowPopover = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setPopoverPosition({
      top: rect.top,
      left: placement === 'right' ? rect.right + 10 : rect.left - 10,
    });
    setPopoverVisible(true);
  };

  return (
    <div style={{ padding: '24px', background: theme === 'dark' ? '#141414' : '#f5f5f5', minHeight: '100vh' }}>
      <Card>
        <Title level={2}>SubMenuPopover 组件测试</Title>
        
        <Alert
          message="组件重构完成"
          description="SubMenuPopover 已基于 Ant Design 官方 Popover 组件重构，解决了自定义箭头的问题。"
          type="success"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 控制面板 */}
          <Card title="控制面板" size="small">
            <Space wrap>
              <div>
                <span style={{ marginRight: '8px' }}>主题:</span>
                <Switch
                  checked={theme === 'dark'}
                  onChange={(checked) => setTheme(checked ? 'dark' : 'light')}
                  checkedChildren="暗黑"
                  unCheckedChildren="明亮"
                />
              </div>
              <div>
                <span style={{ marginRight: '8px' }}>位置:</span>
                <Switch
                  checked={placement === 'left'}
                  onChange={(checked) => setPlacement(checked ? 'left' : 'right')}
                  checkedChildren="左侧"
                  unCheckedChildren="右侧"
                />
              </div>
            </Space>
          </Card>

          {/* 测试区域 */}
          <Card title="测试区域">
            <Paragraph>
              点击下面的按钮来测试 SubMenuPopover 组件的效果：
            </Paragraph>
            
            <Space>
              <Button 
                type="primary" 
                icon={<UserOutlined />}
                onClick={handleShowPopover}
              >
                显示子菜单 ({placement})
              </Button>
              
              <Button 
                onClick={() => setPopoverVisible(false)}
              >
                隐藏子菜单
              </Button>
            </Space>
          </Card>

          {/* 改进说明 */}
          <Card title="改进说明">
            <Space direction="vertical">
              <div>
                <Title level={4}>✅ 解决的问题</Title>
                <ul>
                  <li><strong>自定义箭头问题</strong>：使用 Ant Design 官方 Popover 的内置箭头</li>
                  <li><strong>主题兼容性</strong>：自动适配明亮/暗黑主题</li>
                  <li><strong>定位精确性</strong>：利用 Ant Design 的定位算法</li>
                  <li><strong>响应式支持</strong>：自动调整位置避免遮挡</li>
                </ul>
              </div>
              
              <div>
                <Title level={4}>🚀 技术优势</Title>
                <ul>
                  <li><strong>官方支持</strong>：使用 Ant Design 官方 API</li>
                  <li><strong>更少代码</strong>：移除了自定义箭头相关的复杂逻辑</li>
                  <li><strong>更好维护</strong>：跟随 Ant Design 版本自动更新</li>
                  <li><strong>无样式冲突</strong>：避免自定义样式与主题系统冲突</li>
                </ul>
              </div>
            </Space>
          </Card>
        </Space>
      </Card>

      {/* SubMenuPopover 组件 */}
      <SubMenuPopover
        visible={popoverVisible}
        onClose={() => setPopoverVisible(false)}
        menuItem={testMenuItem}
        position={popoverPosition}
        onMenuClick={handleMenuClick}
        theme={theme}
        trigger="hover"
        placement={placement}
        width={200}
      />
    </div>
  );
};

export default SubMenuTest;
