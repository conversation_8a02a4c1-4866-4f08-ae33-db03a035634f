// 使用现代 CSS 变量系统

// ===== 模型管理页面主样式 =====
.model-management-v2-pro {
  padding: 0;
  display: flex;
  flex-direction: column;

  // ===== 统一容器样式 =====
  .unified-container {
    background: var(--theme-bg-primary);
    border-radius: 8px;
    border: 1px solid var(--theme-border-color-split);
    box-shadow: var(--theme-shadow-1);
    margin-bottom: 24px;
    overflow: visible;
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;

    // ===== 现代标签页设计 =====
    .modern-tabs.ant-tabs {
      background: transparent;
      border: none;
      margin: 0;

      .ant-tabs-nav {
        background: transparent;
        border-bottom: 1px solid var(--theme-border-color-split);
        margin: 0;
        padding: 0 20px;

        .ant-tabs-nav-wrap {
          .ant-tabs-nav-list {
            .ant-tabs-tab {
              position: relative;
              padding: 0;
              margin: 0;
              border: none;
              background: transparent;

              .tab-trigger {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 16px 20px;
                font-weight: 500;
                color: var(--theme-text-secondary);
                transition: all 0.3s ease;
                border-radius: 0;
                position: relative;

                .anticon {
                  font-size: 16px;
                }
              }

              &:hover .tab-trigger {
                color: $primary-color;
                background: transparent;
              }

              &.ant-tabs-tab-active .tab-trigger {
                color: $primary-color;
                background: transparent;
                border-radius: 8px 8px 0 0;
                box-shadow: none;

                &::after {
                  content: '';
                  position: absolute;
                  bottom: -1px;
                  left: 0;
                  right: 0;
                  height: 2px;
                  background: $primary-color;
                }
              }
            }
          }
        }
      }

      .ant-tabs-content-holder {
        padding: 0;
        background: transparent;
        overflow: visible;
      }

      .ant-tabs-tabpane {
        padding: 0;
        background: transparent;
        overflow: visible;

        // ===== 标签页内容区域 =====
        .tab-content {
          display: flex;
          flex-direction: column;
          overflow: hidden;

          // ===== 紧凑操作区域设计 =====
          .compact-action-section {
            padding: 12px 20px;
            // background: #96989a;
            border-bottom: 1px solid var(--theme-border-color-split);
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;

            .stats-summary {
              flex: 1;

              .summary-items {
                display: flex;
                // gap: 24px;

                .summary-item {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  font-size: 13px;
                  color: var(--theme-text-secondary);

                  .summary-icon {
                    font-size: 14px;

                    &.enabled {
                      color: $success-color;
                    }
                  }

                  strong {
                    color: var(--theme-text-primary);
                    font-weight: 600;
                  }
                }
              }
            }

            .action-controls {
              display: flex;
              align-items: center;
              gap: 8px;
              flex-shrink: 0;

              .compact-search {
                height: 32px;
                border-radius: $border-radius-base;
                border: 1px solid var(--theme-border-color);
                background: var(--theme-bg-primary);
                transition: all 0.3s ease;

                &:hover {
                  border-color: var(--theme-text-tertiary);
                }

                &:focus-within {
                  border-color: $primary-color;
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
                }

                .ant-input {
                  font-size: 13px;
                  color: var(--theme-text-primary);

                  &::placeholder {
                    color: var(--theme-text-tertiary);
                  }
                }

                .anticon {
                  color: var(--theme-text-tertiary);
                  font-size: 12px;
                }
              }

              .ant-select {
                height: 32px;

                .ant-select-selector {
                  height: 32px !important;
                  border-radius: $border-radius-base;
                  border: 1px solid var(--theme-border-color);
                  font-size: 13px;
                  background: var(--theme-bg-primary);

                  .ant-select-selection-item {
                    line-height: 30px;
                    color: var(--theme-text-primary);
                  }

                  .ant-select-selection-placeholder {
                    line-height: 30px;
                    color: var(--theme-text-tertiary);
                  }
                }

                &:hover .ant-select-selector {
                  border-color: var(--theme-text-tertiary);
                }

                &.ant-select-focused .ant-select-selector {
                  border-color: $primary-color !important;
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
                }
              }

              .ant-btn {
                height: 32px;
                border-radius: $border-radius-base;
                font-size: 14px;
                font-weight: 500;

                &.ant-btn-sm {
                  height: 32px;
                }

                &.ant-btn-lg {
                  height: 32px;
                }

                &.ant-btn-icon-only {
                  width: 32px;
                  height: 32px;
                }
              }
            }
          }

          // ===== 表格区域统一设计 =====
          .table-section {
            background: transparent;
            overflow: visible;

            .tanstack-table {
              background: transparent !important;
              border: none !important;
              box-shadow: none !important;
              border-radius: 0;

              .pagination-container {
                background: transparent !important;
              }

              .table-container {
                overflow: visible !important;

                .table-wrapper {
                  overflow: visible !important;

                  .table-scroll-container {
                    overflow-x: auto !important;
                    overflow-y: auto !important;
                    min-width: 100% !important;
                    scroll-behavior: auto !important;
                    will-change: scroll-position;

                    .data-table {
                      min-width: 1200px !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // ===== 统一按钮样式 =====
  .ant-btn {
    height: 32px;
    border-radius: $border-radius-base;
    font-size: 14px;
    font-weight: 500;

    &.ant-btn-sm {
      height: 32px;
    }

    &.ant-btn-lg {
      height: 32px;
    }

    &.ant-btn-text {
      height: auto;
      padding: 4px 8px;
    }
  }
}

// ===== 表格特定样式增强 =====
.model-management-table,
.api-source-management-table {
  .table-container {
    overflow: visible !important;

    .table-wrapper {
      overflow: visible !important;

      .table-scroll-container {
        overflow-x: auto !important;
        overflow-y: auto !important;
        scroll-behavior: auto !important;
        will-change: scroll-position;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--theme-text-tertiary);
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background: var(--theme-text-secondary);
          }
        }

        &::-webkit-scrollbar-track {
          background: var(--theme-bg-secondary);
          border-radius: 4px;
        }
      }
    }
  }
}