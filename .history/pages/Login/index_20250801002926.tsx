import React, { useState, useEffect, useRef } from "react";
import {
  Form,
  Input,
  Button,
  message,
  ConfigProvider,
  Row,
  Col,
  Image,
  Tooltip,
  Typography,
} from "antd";
import {
  UserOutlined,
  LockOutlined,
  SafetyOutlined,
  ReloadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  ArrowRightOutlined,
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { authAPI } from "../../services/api";
import authUtils from "../../utils/auth";
import "./style.scss";

const { Title, Text } = Typography;

// 定义登录响应接口
interface LoginErrorResponse {
  message: string;
  needCaptcha: boolean;
  isLocked: boolean;
  isDisabled: boolean;
  lockRemaining: number;
}

// 动态点阵地图组件
const DotMap: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // 路由动画点
  const routes = [
    {
      start: { x: 100, y: 150, delay: 0 },
      end: { x: 200, y: 80, delay: 2 },
      color: "#1890ff",
    },
    {
      start: { x: 200, y: 80, delay: 2 },
      end: { x: 260, y: 120, delay: 4 },
      color: "#1890ff",
    },
    {
      start: { x: 50, y: 50, delay: 1 },
      end: { x: 150, y: 180, delay: 3 },
      color: "#1890ff",
    },
    {
      start: { x: 280, y: 60, delay: 0.5 },
      end: { x: 180, y: 180, delay: 2.5 },
      color: "#1890ff",
    },
  ];

  // 生成点阵地图
  const generateDots = (width: number, height: number) => {
    const dots = [];
    const gap = 12;
    const dotRadius = 1;

    for (let x = 0; x < width; x += gap) {
      for (let y = 0; y < height; y += gap) {
        // 简化的世界地图形状
        const isInMapShape =
          // 亚洲区域
          ((x < width * 0.7 && x > width * 0.45) &&
           (y < height * 0.5 && y > height * 0.1)) ||
          // 欧洲区域
          ((x < width * 0.45 && x > width * 0.3) &&
           (y < height * 0.35 && y > height * 0.15)) ||
          // 美洲区域
          ((x < width * 0.25 && x > width * 0.05) &&
           (y < height * 0.8 && y > height * 0.1));

        if (isInMapShape && Math.random() > 0.3) {
          dots.push({
            x,
            y,
            radius: dotRadius,
            opacity: Math.random() * 0.5 + 0.1,
          });
        }
      }
    }
    return dots;
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeObserver = new ResizeObserver(entries => {
      const { width, height } = entries[0].contentRect;
      setDimensions({ width, height });
      canvas.width = width;
      canvas.height = height;
    });

    resizeObserver.observe(canvas.parentElement as Element);
    return () => resizeObserver.disconnect();
  }, []);

  useEffect(() => {
    if (!dimensions.width || !dimensions.height) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const dots = generateDots(dimensions.width, dimensions.height);
    let animationFrameId: number;
    let startTime = Date.now();

    const animate = () => {
      ctx.clearRect(0, 0, dimensions.width, dimensions.height);

      // 绘制背景点（浅色主题优化）
      dots.forEach(dot => {
        ctx.beginPath();
        ctx.arc(dot.x, dot.y, dot.radius, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(24, 144, 255, ${dot.opacity * 0.6})`;
        ctx.fill();
      });

      // 绘制动画路线
      const currentTime = (Date.now() - startTime) / 1000;

      routes.forEach(route => {
        const elapsed = currentTime - route.start.delay;
        if (elapsed <= 0) return;

        const duration = 3;
        const progress = Math.min(elapsed / duration, 1);

        const x = route.start.x + (route.end.x - route.start.x) * progress;
        const y = route.start.y + (route.end.y - route.start.y) * progress;

        // 绘制路线
        ctx.beginPath();
        ctx.moveTo(route.start.x, route.start.y);
        ctx.lineTo(x, y);
        ctx.strokeStyle = route.color;
        ctx.lineWidth = 1.5;
        ctx.stroke();

        // 绘制起点
        ctx.beginPath();
        ctx.arc(route.start.x, route.start.y, 3, 0, Math.PI * 2);
        ctx.fillStyle = route.color;
        ctx.fill();

        // 绘制移动点
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fillStyle = "#40a9ff";
        ctx.fill();

        // 发光效果
        ctx.beginPath();
        ctx.arc(x, y, 6, 0, Math.PI * 2);
        ctx.fillStyle = "rgba(64, 169, 255, 0.3)";
        ctx.fill();
      });

      // 重置动画
      if (currentTime > 15) {
        startTime = Date.now();
      }

      animationFrameId = requestAnimationFrame(animate);
    };

    animate();

    return () => cancelAnimationFrame(animationFrameId);
  }, [dimensions]);

  return (
    <div className="dot-map-container">
      <canvas ref={canvasRef} className="dot-map-canvas" />
    </div>
  );
};

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [captchaImage, setCaptchaImage] = useState("");
  const [captchaId, setCaptchaId] = useState("");
  const [captchaLoading, setCaptchaLoading] = useState(false);
  const [isProcessingError, setIsProcessingError] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const errorTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 显示错误消息的统一函数
  const showErrorMessage = (
    content: string,
    key: string,
    duration: number = 3,
  ) => {
    if (isProcessingError) {
      console.log("已有错误正在处理，忽略新错误:", content);
      return;
    }

    setIsProcessingError(true);

    if (errorTimerRef.current) {
      clearTimeout(errorTimerRef.current);
    }

    message.destroy();

    setTimeout(() => {
      message.error({
        content,
        key,
        duration,
      });

      errorTimerRef.current = setTimeout(
        () => {
          setIsProcessingError(false);
        },
        duration * 1000 + 500,
      );
    }, 100);
  };

  // 获取验证码
  const fetchCaptcha = async (
    showCaptchaAfterFetch = false,
    skipErrorMessage = false,
  ) => {
    try {
      setCaptchaLoading(true);
      const response = await authAPI.getCaptcha();
      if (response.data) {
        setCaptchaId(response.data.captchaId);
        setCaptchaImage(response.data.captchaImage);
        if (showCaptchaAfterFetch) {
          setShowCaptcha(true);
        }
      }
    } catch (error) {
      console.error("获取验证码失败:", error);
      if (!skipErrorMessage) {
        showErrorMessage(
          "获取验证码失败，请刷新页面重试",
          "captcha-fetch-error",
        );
      }
    } finally {
      setCaptchaLoading(false);
    }
  };

  // 组件挂载时获取验证码
  useEffect(() => {
    // 检查是否已经登录
    if (authUtils.isAuthenticated() && authUtils.isAdmin()) {
      navigate('/dashboard');
      return;
    }

    fetchCaptcha(true, false);

    return () => {
      if (errorTimerRef.current) {
        clearTimeout(errorTimerRef.current);
      }
    };
  }, [navigate]);

  const onFinish = async (values: {
    username: string;
    password: string;
    captcha?: string;
  }) => {
    if (isProcessingError) {
      console.log("正在处理之前的错误，忽略新的登录请求");
      return;
    }

    try {
      setLoading(true);

      const loginData = {
        username: values.username,
        password: values.password,
        captchaId: captchaId,
        captcha: values.captcha,
      };

      console.log("登录请求数据:", loginData);

      try {
        const response = await authAPI.login(loginData);

        console.log("登录响应数据:", response.data);

        // 检查响应格式并提取用户数据
        let userData;
        if (response.data.data) {
          // 包装格式：{success: true, data: {...}}
          userData = response.data.data;
          console.log("提取的用户数据:", userData);
        } else {
          // 直接格式：{token: "...", username: "..."}
          userData = response.data;
        }

        // 保存认证信息
        authUtils.saveAuthData(userData);

        // 获取用户详细信息（包含头像）
        try {
          const profileRes = await authAPI.getProfile();
          if (profileRes.data && profileRes.data.avatar) {
            localStorage.setItem("avatar", profileRes.data.avatar);
          }
        } catch (profileError) {
          console.error("获取用户详情失败:", profileError);
        }

        // 检查用户角色，只允许管理员登录
        if (userData.role !== "admin") {
          showErrorMessage(
            "该系统仅供管理员使用，普通用户请使用客户端登录",
            "non-admin-error",
            3,
          );

          setTimeout(() => {
            authUtils.clearAuthAndRedirect(false, "", 0);
          }, 3500);
          return;
        }

        message.success("登录成功");
        navigate("/dashboard");
      } catch (loginError: any) {
        console.error("登录失败:", loginError);

        if (loginError.data) {
          const errorData = loginError.data as LoginErrorResponse;

          if (errorData.isDisabled) {
            showErrorMessage(
              "您的账户已被禁用，如需恢复使用请联系系统管理员",
              "login-disabled",
              3,
            );
            return;
          }

          if (errorData.isLocked) {
            const minutes = Math.ceil(errorData.lockRemaining / 60);
            showErrorMessage(
              `由于多次输入错误密码，账户已临时锁定，请在${minutes}分钟后重试`,
              "login-locked",
              3,
            );
            return;
          }

          if (errorData.needCaptcha) {
            setShowCaptcha(true);

            try {
              setCaptchaLoading(true);
              form.setFieldsValue({ captcha: "" });

              const captchaResponse = await authAPI.getCaptcha();
              if (captchaResponse.data) {
                setCaptchaId(captchaResponse.data.captchaId);
                setCaptchaImage(captchaResponse.data.captchaImage);
              }

              showErrorMessage(
                errorData.message || "登录失败，请输入验证码",
                "captcha-required",
              );
            } catch (captchaError) {
              console.error("验证码刷新失败:", captchaError);
              showErrorMessage("登录失败，请输入验证码", "captcha-required");
            } finally {
              setCaptchaLoading(false);
            }
            return;
          }

          showErrorMessage(
            errorData.message || "登录失败，请检查用户名和密码",
            "login-failed",
          );
        } else {
          showErrorMessage(
            "登录失败，请检查用户名和密码",
            "login-error-generic",
          );
        }
      }
    } catch (error) {
      console.error("登录过程中发生未知错误:", error);
      showErrorMessage("登录失败，请稍后重试", "login-unknown-error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: "#1890ff",
          borderRadius: 8,
        },
      }}
    >
      <div className="modern-login-container">
        <div className="login-wrapper">
          {/* 左侧 - 动态地图区域 */}
          <div className="login-left-panel">
            <div className="map-background">
              <DotMap />

              {/* Logo和标题覆盖层 */}
              <div className="brand-overlay">
                <div className="brand-logo">
                  <div className="logo-icon">
                    <ArrowRightOutlined />
                  </div>
                </div>
                <Title level={2} className="brand-title">
                  V2 Admin
                </Title>
                <Text className="brand-subtitle">
                  登录管理平台，连接全球数据中心
                </Text>
              </div>
            </div>
          </div>

          {/* 右侧 - 登录表单区域 */}
          <div className="login-right-panel">
            <div className="login-form-container">
              <div className="form-header">
                <Title level={2} className="welcome-title">欢迎回来</Title>
                <Text className="welcome-subtitle">登录您的管理账户</Text>
              </div>

              <Form
                name="login"
                form={form}
                onFinish={onFinish}
                layout="vertical"
                className="modern-login-form"
                size="large"
              >
                <Form.Item
                  label="用户名"
                  name="username"
                  rules={[{ required: true, message: "请输入用户名" }]}
                >
                  <Input
                    prefix={<UserOutlined className="input-icon" />}
                    placeholder="请输入您的用户名"
                    autoComplete="username"
                    className="modern-input"
                  />
                </Form.Item>

                <Form.Item
                  label="密码"
                  name="password"
                  rules={[{ required: true, message: "请输入密码" }]}
                >
                  <Input
                    prefix={<LockOutlined className="input-icon" />}
                    type={passwordVisible ? "text" : "password"}
                    placeholder="请输入您的密码"
                    autoComplete="current-password"
                    className="modern-input"
                    suffix={
                      <button
                        type="button"
                        className="password-toggle"
                        onClick={() => setPasswordVisible(!passwordVisible)}
                      >
                        {passwordVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                      </button>
                    }
                  />
                </Form.Item>

                {showCaptcha && (
                  <Form.Item
                    label="验证码"
                    name="captcha"
                    rules={[{ required: true, message: "请输入验证码" }]}
                  >
                    <Row gutter={8}>
                      <Col span={16}>
                        <Input
                          prefix={<SafetyOutlined className="input-icon" />}
                          placeholder="请输入验证码"
                          className="modern-input"
                        />
                      </Col>
                      <Col span={8}>
                        <div className="modern-captcha-container">
                          {captchaLoading ? (
                            <div className="captcha-loading">
                              加载中...
                            </div>
                          ) : (
                            <div
                              className="captcha-image-wrapper"
                              onClick={() => {
                                if (!captchaLoading && !isProcessingError) {
                                  fetchCaptcha(false, false);
                                }
                              }}
                            >
                              <Image
                                src={captchaImage}
                                alt="验证码"
                                preview={false}
                                className="captcha-image"
                              />
                              <Tooltip title="点击刷新验证码">
                                <ReloadOutlined className="captcha-refresh" />
                              </Tooltip>
                            </div>
                          )}
                        </div>
                      </Col>
                    </Row>
                  </Form.Item>
                )}

                <Form.Item className="login-button-item">
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    className={`modern-login-button ${isHovered ? 'hovered' : ''}`}
                    disabled={isProcessingError}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                  >
                    <span className="button-content">
                      登录
                      <ArrowRightOutlined className="button-arrow" />
                    </span>
                  </Button>
                </Form.Item>

                <div className="login-footer">
                  <Text className="default-account-tip">
                    默认账户：admin / admin
                  </Text>
                </div>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default Login;
