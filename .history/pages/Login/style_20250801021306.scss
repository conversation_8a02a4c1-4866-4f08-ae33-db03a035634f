/**
 * V2 Admin 现代化登录页面样式
 * 支持明亮和暗色主题的专业设计，专为管理员登录优化
 */

// 主容器
.modern-login-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
  padding: 16px;
  position: relative;
  overflow: hidden;

  // 添加微妙的背景纹理
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(24, 144, 255, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(24, 144, 255, 0.02) 0%, transparent 50%);
    pointer-events: none;
  }
}

// 登录包装器 - CSS Grid布局
.login-wrapper {
  width: 100%;
  max-width: 1000px;
  height: 600px;
  display: grid;
  grid-template-columns: 1fr 1fr; // 50%/50%左右分割
  grid-template-rows: 1fr; // 单行布局
  grid-gap: 0; // 无间距，保持紧密布局
  place-items: stretch; // 子元素拉伸填满网格区域
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;

  // 悬停效果
  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.12),
      0 0 0 1px rgba(24, 144, 255, 0.1);
  }

  transition: all 0.3s ease;
}

// 左侧面板 - 地图区域 (Grid第一列)
.login-left-panel {
  grid-column: 1;
  grid-row: 1;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e1f5fe 100%);
  border-right: 1px solid #e8e8e8;
  overflow: hidden;

  .map-background {
    position: relative;
    width: 100%;
    height: 100%;

    .dot-map-container {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;

      .dot-map-canvas {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        opacity: 0.6; // 降低地图透明度以适配浅色主题
      }
    }

    // 品牌覆盖层
    .brand-overlay {
      position: absolute;
      inset: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 32px;
      z-index: 10;

      .brand-logo {
        margin-bottom: 24px;

        .logo-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
          box-shadow: 0 8px 16px rgba(24, 144, 255, 0.25);
        }
      }

      .brand-title {
        color: transparent !important;
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 32px !important;
        font-weight: 700 !important;
        margin: 0 0 8px 0 !important;
        text-align: center;
      }

      .brand-subtitle {
        color: rgba(0, 0, 0, 0.65) !important;
        font-size: 14px;
        text-align: center;
        max-width: 280px;
        line-height: 1.5;
      }
    }
  }
}

// 右侧面板 - 表单区域 (Grid第二列)
.login-right-panel {
  grid-column: 2;
  grid-row: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;

  .login-form-container {
    width: 100%;
    max-width: 360px;
    height: 100%; // 使用父容器的完整高度
    padding: 40px 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center; // 垂直居中
    transform-origin: center center; // 缩放中心点
    transition: transform 0.3s ease; // 平滑缩放过渡

    .form-header {
      margin-bottom: 32px;
      flex-shrink: 0; // 防止标题被压缩

      .welcome-title {
        color: #1a1a1a !important;
        font-size: clamp(20px, 4vw, 28px) !important; // 响应式字体大小
        font-weight: 700 !important;
        margin: 0 0 8px 0 !important;
        line-height: 1.2;
      }

      .welcome-subtitle {
        color: rgba(0, 0, 0, 0.65) !important;
        font-size: clamp(14px, 3vw, 16px) !important; // 响应式字体大小
        line-height: 1.4;
      }
    }
  }
}

// 高度受限时的自适应缩放
@media (max-height: 600px) {
  .login-right-panel {
    .login-form-container {
      transform: scale(0.8); // 高度不足600px时缩放到80%
      padding: 20px 24px;
    }
  }
}

@media (max-height: 500px) {
  .login-right-panel {
    .login-form-container {
      transform: scale(0.7); // 高度不足500px时缩放到70%
      padding: 16px 20px;
    }
  }
}

@media (max-height: 400px) {
  .login-right-panel {
    .login-form-container {
      transform: scale(0.6); // 高度不足400px时缩放到60%
      padding: 12px 16px;
    }
  }
}

// 现代化表单样式
.modern-login-form {
  .ant-form-item {
    margin-bottom: 20px;

    .ant-form-item-label > label {
      color: rgba(0, 0, 0, 0.85) !important;
      font-weight: 500;
      font-size: 14px;
    }

    .required-mark {
      color: #1890ff;
      margin-left: 2px;
    }
  }

  // 现代化输入框
  .modern-input {
    background: #ffffff !important;
    border: 1px solid #d9d9d9 !important;
    border-radius: 8px !important;
    height: 48px !important;
    color: rgba(0, 0, 0, 0.85) !important;
    font-size: 15px;

    &::placeholder {
      color: rgba(0, 0, 0, 0.45) !important;
    }

    &:hover {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
    }

    &:focus,
    &.ant-input-focused {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.15) !important;
    }

    .input-icon {
      color: rgba(0, 0, 0, 0.45);
      font-size: 16px;
    }

    .password-toggle {
      background: none;
      border: none;
      color: rgba(0, 0, 0, 0.45);
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        color: rgba(0, 0, 0, 0.75);
        background: rgba(0, 0, 0, 0.04);
      }
    }
  }

  // 登录按钮
  .login-button-item {
    margin-top: 24px;
    margin-bottom: 0;
  }

  .modern-login-button {
    height: 48px !important;
    border-radius: 8px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25) !important;
    position: relative;
    overflow: hidden;
    color: #ffffff !important;

    .button-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      .button-arrow {
        font-size: 14px;
        transition: transform 0.2s ease;
      }
    }

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.35) !important;
      background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%) !important;

      .button-arrow {
        transform: translateX(2px);
      }
    }

    &:active {
      transform: translateY(0);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    // 按钮光效动画
    &.hovered::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      animation: buttonShine 1s ease-in-out;
    }
  }

  @keyframes buttonShine {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }
}

// 验证码样式
.modern-captcha-container {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }

  .captcha-image-wrapper {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden;

    .captcha-image {
      width: 100%;
      height: 100%;
      object-fit: contain;

      .ant-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }
    }

    .captcha-refresh {
      position: absolute;
      top: 4px;
      right: 4px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px;
      border-radius: 4px;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover .captcha-refresh {
      opacity: 1;
    }
  }

  .captcha-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }
}

// 登录页脚
.login-footer {
  text-align: center;
  margin-top: 24px;

  .default-account-tip {
    color: rgba(0, 0, 0, 0.45) !important;
    font-size: 13px;
    padding: 12px 16px;
    background: rgba(24, 144, 255, 0.04);
    border: 1px solid rgba(24, 144, 255, 0.15);
    border-radius: 8px;
    display: inline-block;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-wrapper {
    max-width: 800px;
    height: 500px;
  }

  .login-left-panel .brand-overlay {
    padding: 24px;

    .brand-title {
      font-size: 28px !important;
    }

    .brand-subtitle {
      font-size: 13px;
    }
  }

  .login-right-panel .login-form-container {
    max-width: 320px;
    padding: 28px 24px;
    transform: scale(0.95);

    .form-header {
      margin-bottom: 24px;
    }
  }
}

@media (max-width: 768px) {
  .modern-login-container {
    padding: 12px;
  }

  .login-wrapper {
    grid-template-columns: 1fr; // 单列布局
    grid-template-rows: 200px 1fr; // 上方200px给地图，下方自适应给表单
    max-width: 400px;
    height: auto;
    min-height: 600px;
  }

  .login-left-panel {
    grid-column: 1;
    grid-row: 1; // 占据第一行
    border-right: none;
    border-bottom: 1px solid #e8e8e8;

    .brand-overlay {
      padding: 20px;

      .brand-logo {
        margin-bottom: 16px;

        .logo-icon {
          width: 40px;
          height: 40px;
          font-size: 20px;
        }
      }

      .brand-title {
        font-size: 24px !important;
      }

      .brand-subtitle {
        font-size: 12px;
        max-width: 240px;
      }
    }
  }

  .login-right-panel {
    grid-column: 1;
    grid-row: 2; // 占据第二行

    .login-form-container {
      max-width: 100%;
      width: calc(100% - 32px);
      padding: 24px 16px; // 适中的padding
      margin: 0 auto;
      box-sizing: border-box;
      transform: scale(0.9); // 缩放到90%以适应小屏幕

      .form-header {
        text-align: center;
        margin-bottom: 24px;
      }
    }
  }

  .modern-login-form {
    .modern-input {
      height: 44px !important;
      font-size: 14px;
    }

    .modern-login-button {
      height: 44px !important;
      font-size: 15px !important;
    }
  }
}

@media (max-width: 480px) {
  .modern-login-container {
    padding: 8px;
  }

  .login-wrapper {
    max-width: 100%;
    border-radius: 12px;
  }

  .login-wrapper {
    grid-template-rows: 160px 1fr; // 调整地图区域高度为160px
  }

  .login-left-panel {
    // 高度由grid-template-rows控制，无需单独设置

    .brand-overlay {
      padding: 16px;

      .brand-logo {
        margin-bottom: 12px;

        .logo-icon {
          width: 36px;
          height: 36px;
          font-size: 18px;
        }
      }

      .brand-title {
        font-size: 20px !important;
      }

      .brand-subtitle {
        font-size: 11px;
        max-width: 200px;
      }
    }
  }

  .login-right-panel {
    .login-form-container {
      width: calc(100% - 24px);
      padding: 16px 12px; // 最小padding
      margin: 0 auto;
      box-sizing: border-box;
      transform: scale(0.85); // 缩放到85%以适应超小屏幕

      .form-header {
        margin-bottom: 20px;
      }
    }
  }

  .modern-login-form {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .modern-input {
      height: 42px !important;
      font-size: 14px;
    }

    .modern-login-button {
      height: 42px !important;
      font-size: 14px !important;
    }

    .login-button-item {
      margin-top: 20px;
    }
  }

  .login-footer {
    margin-top: 20px;

    .default-account-tip {
      font-size: 12px;
      padding: 10px 12px;
    }
  }
}

// 动画优化 - 减少动画偏好支持
@media (prefers-reduced-motion: reduce) {
  .login-wrapper {
    transition: none;

    &:hover {
      transform: none;
    }
  }

  .modern-login-button {
    &:hover:not(:disabled) {
      transform: none;
    }

    &.hovered::before {
      animation: none;
    }
  }

  .dot-map-canvas {
    display: none;
  }
}

// 高对比度支持
@media (prefers-contrast: high) {
  .login-wrapper,
  .modern-login-button {
    border: 2px solid #ffffff !important;
  }

  .modern-input {
    border-color: #ffffff !important;

    &:focus,
    &.ant-input-focused {
      border-color: #ffffff !important;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }
}

// ===== 暗色主题支持 =====
[data-theme="dark"] {
  .modern-login-container {
    background: linear-gradient(135deg, #0f1419 0%, #1a1a1a 100%);

    // 暗色主题背景纹理
    &::before {
      background-image:
        radial-gradient(circle at 25% 25%, rgba(64, 169, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(64, 169, 255, 0.06) 0%, transparent 50%);
    }
  }

  .login-wrapper {
    background: #1f1f1f;
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.1);

    &:hover {
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(64, 169, 255, 0.3);
    }
  }

  // 左侧面板暗色主题
  .login-left-panel {
    background: linear-gradient(135deg, #1a1a1a 0%, #262626 100%);
    border-right: 1px solid #424242;

    .brand-overlay {
      .brand-title {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .brand-subtitle {
        color: rgba(255, 255, 255, 0.65) !important;
      }
    }
  }

  // 右侧面板暗色主题
  .login-right-panel {
    background: #1f1f1f;

    .login-form-container {
      .form-header {
        .welcome-title {
          color: rgba(255, 255, 255, 0.95) !important;
        }

        .welcome-subtitle {
          color: rgba(255, 255, 255, 0.65) !important;
        }
      }
    }
  }

  // 表单元素暗色主题
  .modern-login-form {
    .ant-form-item {
      .ant-form-item-label > label {
        color: rgba(255, 255, 255, 0.85) !important;
      }
    }

    .modern-input {
      background: #262626 !important;
      border: 1px solid #424242 !important;
      color: rgba(255, 255, 255, 0.85) !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.45) !important;
      }

      &:hover {
        border-color: #40a9ff !important;
        box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2) !important;
      }

      &:focus,
      &.ant-input-focused {
        border-color: #40a9ff !important;
        box-shadow: 0 0 0 3px rgba(64, 169, 255, 0.25) !important;
      }

      .input-icon {
        color: rgba(255, 255, 255, 0.45);
      }

      .password-toggle {
        color: rgba(255, 255, 255, 0.45);

        &:hover {
          color: rgba(255, 255, 255, 0.75);
          background: rgba(255, 255, 255, 0.08);
        }
      }
    }
  }

  // 验证码暗色主题
  .modern-captcha-container {
    background: #262626;
    border: 1px solid #424242;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(64, 169, 255, 0.2);
    }

    .captcha-loading {
      color: rgba(255, 255, 255, 0.45);
    }
  }

  // 登录页脚暗色主题
  .login-footer {
    .default-account-tip {
      color: rgba(255, 255, 255, 0.65) !important;
      background: rgba(64, 169, 255, 0.1);
      border: 1px solid rgba(64, 169, 255, 0.3);
    }
  }

  // 移动端暗色主题适配
  @media (max-width: 768px) {
    .login-left-panel {
      border-bottom: 1px solid #424242;
    }
  }
}

// 暗色主题下的主题切换按钮
[data-theme="dark"] {
  .login-theme-toggle {
    background: rgba(31, 31, 31, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;

    &:hover {
      background: rgba(31, 31, 31, 1) !important;
      border-color: #40a9ff !important;
      box-shadow: 0 4px 12px rgba(64, 169, 255, 0.25) !important;
    }

    .anticon {
      color: #40a9ff;
    }
  }
}
