@use '../../styles/variables' as *;

.user-management-refactored {
  padding: 0;
  // background: var(--theme-bg-secondary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  transition: background-color 0.2s ease;

  // 页面标题
  .page-header {
    margin-bottom: $spacing-md; // 统一间距：16px
    padding-bottom: $spacing-md; // 统一间距：16px
    border-bottom: 1px solid var(--theme-border-color-split);
    transition: border-color 0.2s ease;

    h1 {
      margin: 0 0 $spacing-md 0; // 标题下方间距：16px
      font-size: 24px;
      font-weight: 600;
      color: var(--theme-text-primary);
      transition: color 0.2s ease;

      // 手机端字体大小优化
      @media (max-width: 768px) {
        font-size: 20px;
      }
    }

    p {
      margin: 0;
      color: var(--theme-text-secondary);
      font-size: 14px;
      transition: color 0.2s ease;
    }
  }

  // ProComponents QueryFilter 样式
  .ant-pro-query-filter {
    border: 1px solid var(--theme-border-color-split);
    border-radius: $border-radius-base;
    background: var(--theme-bg-primary);
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  // 表格容器 - 一体化设计（参考模型页面）
  .table-section {
    border: 1px solid var(--theme-border-color-split);
    border-radius: 8px;
    background: var(--theme-bg-primary);
    box-shadow: var(--theme-shadow-1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;

    // 紧凑操作区域样式 - 作为表格顶部
    .compact-action-section {
      padding: $spacing-md; // 统一内边距：16px
      background: var(--theme-bg-tertiary);
      border-bottom: 1px solid var(--theme-border-color-split);
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: $spacing-md; // 统一间距：16px
      transition: background-color 0.2s ease, border-color 0.2s ease;

      // 手机端布局优化
      @media (max-width: 768px) {
        flex-direction: column; // 手机端垂直布局
        align-items: stretch;
      }

    .action-buttons {
      flex-shrink: 0;
      display: flex;
      gap: $spacing-md; // 按钮间距：16px

      // 手机端优化
      @media (max-width: 768px) {
        flex-wrap: wrap;
      }

      // 统一按钮样式 - 保持原生 Ant Design 动画效果
      .ant-btn {
        // 统一高度和基础样式
        height: 32px;
        border-radius: $border-radius-base;
        font-size: 14px;
        font-weight: 500;

        // 确保所有按钮变体都有统一高度
        &.ant-btn-sm {
          height: 32px;
        }

        &.ant-btn-lg {
          height: 32px;
        }

        // 保持原生动画，不添加自定义 transition 或 transform
        // 让 Ant Design 处理所有动画效果（包括波纹动画）
      }

    }
    }

    .stats-summary {
      flex-shrink: 0;

      .summary-items {
        display: flex;
        gap: $spacing-md; // 统计项间距：16px

        // 手机端优化
        @media (max-width: 768px) {
          flex-direction: column;
        }

        .summary-item {
          display: flex;
          align-items: center;
          gap: $spacing-md; // 图标与文字间距：16px
          font-size: 13px;
          color: var(--theme-text-secondary);
          transition: color 0.2s ease;

          .summary-icon {
            font-size: 14px;
            color: var(--theme-text-tertiary);
            transition: color 0.2s ease;

            &.enabled {
              color: $success-color;
            }
          }

          strong {
            color: var(--theme-text-primary);
            font-weight: 600;
            transition: color 0.2s ease;
          }
        }
      }
    }

    // 表格容器内的基本样式（保留有效的样式）
    .tanstack-table {
      // 表格容器样式调整
      .table-container {
        background: transparent;
      }

      // 表格特定样式
      table {
        // 选择列样式
        td:first-child,
        th:first-child {
          text-align: center;
          padding-left: 12px;
          padding-right: 12px;
          width: 50px;
          min-width: 50px;
          max-width: 50px;
        }

        // 头像列样式
        td:nth-child(2),
        th:nth-child(2) {
          text-align: center;
          padding-left: 8px;
          padding-right: 8px;
          width: 69px;
          min-width: 69px;
          max-width: 69px;
        }

        // 添加表头居中样式 - 仅影响当前页面
        thead th .header-content {
          justify-content: center !important;
        }

        // 复选框样式调整
        .ant-checkbox-wrapper {
          display: flex;
          justify-content: center;
          margin: 0;
        }

        // 头像容器样式
        .avatar-container {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  // 状态点样式
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: $spacing-md; // 状态点右边距：16px

    &.active {
      background: $success-color;
    }

    &.inactive {
      background: $error-color;
    }
  }

  // 响应式表格优化
  @media (max-width: 1200px) {
    .table-section {
      .tanstack-table {
        table {
          // 在中等屏幕上优化列宽
          td:first-child,
          th:first-child {
            width: 45px;
            min-width: 45px;
            max-width: 45px;
          }

          td:nth-child(2),
          th:nth-child(2) {
            width: 60px;
            min-width: 60px;
            max-width: 60px;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .table-section {
      .tanstack-table {
        table {
          // 在小屏幕上进一步优化
          td:first-child,
          th:first-child {
            width: 40px;
            min-width: 40px;
            max-width: 40px;
            padding-left: 8px;
            padding-right: 8px;
          }

          td:nth-child(2),
          th:nth-child(2) {
            width: 55px;
            min-width: 55px;
            max-width: 55px;
            padding-left: 4px;
            padding-right: 4px;
          }

          // 小屏幕上头像稍微小一点
          .ant-avatar {
            width: 28px !important;
            height: 28px !important;
            font-size: 12px !important;
          }
        }
      }
    }
  }
}

// ===== 统一间距系统 =====
// 注意：所有间距使用统一的 $spacing-md (16px) 标准
// 简化设计系统，提高一致性，减少维护复杂度
