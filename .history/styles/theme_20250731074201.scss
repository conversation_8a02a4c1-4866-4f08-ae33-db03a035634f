/**
 * 主题系统 - 基础CSS变量定义
 * 使用 data-theme 属性来切换主题
 */

/* 默认主题（亮色） */
:root,
[data-theme="light"] {
  /* 基础颜色 */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f5f5;
  --theme-bg-tertiary: #fafafa;

  /* 文字颜色 */
  --theme-text-primary: #000000d9;
  --theme-text-secondary: #00000073;
  --theme-text-tertiary: #00000040;

  /* 边框颜色 */
  --theme-border-color: #d9d9d9;
  --theme-border-color-split: #f0f0f0;

  // /* 阴影 */
  // --theme-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
  // --theme-shadow-2: 0 6px 16px rgba(0, 0, 0, 0.08);

  /* 表格特定颜色 */
  --theme-table-header-bg: #ffffff;
  --theme-table-row-hover: #f5f5f5;
  --theme-table-row-stripe: #fafafa;

  /* 布局特定颜色 */
  --theme-mask-bg: rgba(0, 0, 0, 0.45);
  --theme-loading-bg: rgba(255, 255, 255, 0.8);

  /* 滚动条颜色 */
  --theme-scrollbar-thumb: rgba(0, 0, 0, 0.15);
  --theme-scrollbar-thumb-hover: rgba(0, 0, 0, 0.25);

  /* 状态颜色 */
  --theme-error-color: #ff4d4f;

  /* 主色调 */
  --theme-primary-color: #1890ff;
  --theme-primary-color-hover: #40a9ff;
  --theme-primary-color-light: #e6f7ff;
  --theme-success-color: #52c41a;
  --theme-warning-color: #faad14;
  --theme-info-color: #1890ff;

  /* 间距系统 */
  --theme-spacing-md: 16px;

  /* 字体系统 */
  --theme-font-size-xs: 11px;
  --theme-font-size-sm: 12px;
  --theme-font-size-base: 14px;
  --theme-font-size-lg: 16px;
  --theme-font-size-xl: 18px;
  --theme-font-size-xxl: 20px;

  /* 字体权重 */
  --theme-font-weight-normal: 400;
  --theme-font-weight-medium: 500;
  --theme-font-weight-semibold: 600;
  --theme-font-weight-bold: 700;

  /* 圆角系统 */
  --theme-border-radius-xs: 2px;
  --theme-border-radius-sm: 4px;
  --theme-border-radius-base: 8px;
  --theme-border-radius-md: 6px;
  --theme-border-radius-lg: 8px;

  /* 阴影系统 */
  --theme-box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  --theme-box-shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --theme-box-shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --theme-box-shadow-hover: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);

  /* 动画系统 */
  --theme-transition-base: all 0.2s ease;

  /* 响应式断点 */
  --theme-breakpoint-xs: 480px;
  --theme-breakpoint-sm: 768px;
  --theme-breakpoint-md: 1024px;
  --theme-breakpoint-lg: 1440px;
  --theme-breakpoint-xl: 1920px;
  --theme-breakpoint-xxl: 2560px;

  /* 布局尺寸 */
  --theme-header-height: 48px;
  --theme-header-height-mobile: 44px;
  --theme-content-padding: 24px;
  --theme-content-padding-mobile: 16px;

  /* Z-index 层级 */
  --theme-z-index-sticky: 1020;
  --theme-z-index-fixed: 1030;
  --theme-z-index-modal-backdrop: 1040;
}

/* 暗色主题 */
[data-theme="dark"] {
  /* 基础颜色 */
  --theme-bg-primary: #1f1f1f;
  --theme-bg-secondary: #141414;
  --theme-bg-tertiary: #262626;

  /* 文字颜色 */
  --theme-text-primary: rgba(255, 255, 255, 0.85);
  --theme-text-secondary: rgba(255, 255, 255, 0.65);
  --theme-text-tertiary: rgba(255, 255, 255, 0.45);

  /* 边框颜色 */
  --theme-border-color: #434343;
  --theme-border-color-split: #303030;

  // /* 阴影 */
  // --theme-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.45);
  // --theme-shadow-2: 0 6px 16px rgba(0, 0, 0, 0.25);

  /* 表格特定颜色 */
  --theme-table-header-bg: #1f1f1f;
  --theme-table-row-hover: #262626;
  --theme-table-row-stripe: #252525;

  /* 布局特定颜色 */
  --theme-mask-bg: rgba(0, 0, 0, 0.65);
  --theme-loading-bg: rgba(0, 0, 0, 0.8);

  /* 滚动条颜色 */
  --theme-scrollbar-thumb: rgba(255, 255, 255, 0.15);
  --theme-scrollbar-thumb-hover: rgba(255, 255, 255, 0.25);

  /* 状态颜色 */
  --theme-error-color: #ff7875;

  /* 主色调 */
  --theme-primary-color: #1890ff;
  --theme-primary-color-hover: #40a9ff;
  --theme-primary-color-light: #e6f7ff;
  --theme-success-color: #52c41a;
  --theme-warning-color: #faad14;
  --theme-info-color: #1890ff;

  /* 间距系统 */
  --theme-spacing-md: 16px;

  /* 字体系统 */
  --theme-font-size-xs: 11px;
  --theme-font-size-sm: 12px;
  --theme-font-size-base: 14px;
  --theme-font-size-lg: 16px;
  --theme-font-size-xl: 18px;
  --theme-font-size-xxl: 20px;

  /* 字体权重 */
  --theme-font-weight-normal: 400;
  --theme-font-weight-medium: 500;
  --theme-font-weight-semibold: 600;
  --theme-font-weight-bold: 700;

  /* 圆角系统 */
  --theme-border-radius-xs: 2px;
  --theme-border-radius-sm: 4px;
  --theme-border-radius-base: 8px;
  --theme-border-radius-md: 6px;
  --theme-border-radius-lg: 8px;

  /* 阴影系统 */
  --theme-box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.45), 0 6px 16px 0 rgba(0, 0, 0, 0.25), 0 9px 28px 8px rgba(0, 0, 0, 0.15);
  --theme-box-shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --theme-box-shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
  --theme-box-shadow-hover: 0 6px 16px -8px rgba(0, 0, 0, 0.25), 0 9px 28px 0 rgba(0, 0, 0, 0.15), 0 12px 48px 16px rgba(0, 0, 0, 0.1);

  /* 动画系统 */
  --theme-transition-base: all 0.2s ease;

  /* 响应式断点 */
  --theme-breakpoint-xs: 480px;
  --theme-breakpoint-sm: 768px;
  --theme-breakpoint-md: 1024px;
  --theme-breakpoint-lg: 1440px;
  --theme-breakpoint-xl: 1920px;
  --theme-breakpoint-xxl: 2560px;
}

/* ===== 主题切换过渡动画已移除 ===== */
/* 现在使用直接的主题切换，无过渡效果 */

/* 应用主题变量到全局元素 */
body {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

/* 为现有组件提供主题变量支持 */
/* 注意：现在优先使用 Ant Design 组件的 styles API 进行主题定制 */
/* 这些全局样式仅作为后备方案，新组件应使用 src/constants/antdStyles.ts 中的配置 */

.ant-layout {
  background-color: var(--theme-bg-secondary) !important;
}

.ant-layout-header {
  background-color: var(--theme-bg-primary) !important;
  border-bottom: 1px solid var(--theme-border-color-split) !important;
}

.ant-layout-sider {
  background-color: var(--theme-bg-primary) !important;
}

.ant-menu {
  background-color: transparent !important;
  color: var(--theme-text-primary) !important;
}
