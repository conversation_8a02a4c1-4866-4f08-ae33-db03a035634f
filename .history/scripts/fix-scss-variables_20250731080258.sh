#!/bin/bash

# SCSS 变量到 CSS 变量安全迁移脚本
# 作者：V2 Admin 开发团队
# 版本：1.0.0

cd  /Users/<USER>/LLM/admin_ui/v2-admin_副本
set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
BACKUP_DIR="backups/scss-migration-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

log_info "🚀 开始 SCSS 变量到 CSS 变量安全迁移"
log_info "📁 备份目录: $BACKUP_DIR"

# 第一步：移除所有对 variables.scss 的引用
log_info "\n📋 第一步：移除对已删除 variables.scss 的引用"

# 需要修复的文件列表
declare -a FILES_TO_FIX=(
    "components/TabsHistory/style.scss"
    "components/UserAvatar/style.scss"
    "components/UserCreateForm/style.scss"
    "components/DynamicBreadcrumb/style.scss"
    "pages/ModelManagementV2/style.scss"
    "pages/OnlineUsersManagement/style.scss"
    "layouts/BasicLayout/style.scss"
    "index.ts"
)

# 备份并修复每个文件
for file in "${FILES_TO_FIX[@]}"; do
    if [ -f "$file" ]; then
        log_info "🔧 处理文件: $file"
        
        # 创建备份
        cp "$file" "$BACKUP_DIR/$(basename $file).backup"
        
        # 移除 @use 和 @import 语句
        sed -i.tmp "s|@use '../../styles/variables' as \*;|// 使用现代 CSS 变量系统|g" "$file"
        sed -i.tmp "s|@import '../../styles/variables.scss';|// 使用现代 CSS 变量系统|g" "$file"
        sed -i.tmp "s|import './styles/variables.scss';|// 已迁移到 CSS 变量系统|g" "$file"
        
        # 删除临时文件
        rm -f "$file.tmp"
        
        log_success "✅ 已修复: $file"
    else
        log_warning "⚠️  文件不存在: $file"
    fi
done

# 第二步：替换 SCSS 变量为 CSS 变量
log_info "\n📋 第二步：替换 SCSS 变量为 CSS 变量"

# 需要替换变量的文件列表
declare -a SCSS_FILES=(
    "pages/DashboardV2/style.scss"
    "pages/SystemSettings/style.scss"
    "pages/AssistantManagement/style.scss"
    "pages/UserListManagement/style.scss"
    "components/Sidebar/style.scss"
    "components/Header/style.scss"
    "components/TabsHistory/style.scss"
    "components/UserAvatar/style.scss"
    "components/DynamicBreadcrumb/style.scss"
)

# 变量替换映射（按顺序，长的在前避免部分匹配）
declare -A VARIABLE_MAP=(
    # 颜色变量 - 长的在前
    ['\$primary-color-hover']=var\(--theme-primary-color-hover\)
    ['\$primary-color-light']=var\(--theme-primary-color-light\)
    ['\$primary-color']=var\(--theme-primary-color\)
    ['\$success-color']=var\(--theme-success-color\)
    ['\$warning-color']=var\(--theme-warning-color\)
    ['\$error-color']=var\(--theme-error-color\)
    ['\$info-color']=var\(--theme-info-color\)
    
    # 文本颜色变量
    ['\$text-color-primary']=var\(--theme-text-primary\)
    ['\$text-color-secondary']=var\(--theme-text-secondary\)
    ['\$text-color-tertiary']=var\(--theme-text-tertiary\)
    ['\$text-color-disabled']=var\(--theme-text-disabled\)
    ['\$text-color']=var\(--theme-text-primary\)
    
    # 背景颜色变量
    ['\$background-color-base']=var\(--theme-bg-secondary\)
    ['\$background-color-light']=var\(--theme-bg-tertiary\)
    ['\$background-color-white']=var\(--theme-bg-primary\)
    
    # 边框颜色变量
    ['\$border-color-base']=var\(--theme-border-color\)
    ['\$border-color-split']=var\(--theme-border-color-split\)
    
    # 间距变量
    ['\$spacing-xs']=var\(--theme-spacing-xs\)
    ['\$spacing-sm']=var\(--theme-spacing-sm\)
    ['\$spacing-md']=var\(--theme-spacing-md\)
    ['\$spacing-lg']=var\(--theme-spacing-lg\)
    ['\$spacing-xl']=var\(--theme-spacing-xl\)
    
    # 字体大小变量
    ['\$font-size-xs']=var\(--theme-font-size-xs\)
    ['\$font-size-sm']=var\(--theme-font-size-sm\)
    ['\$font-size-base']=var\(--theme-font-size-base\)
    ['\$font-size-lg']=var\(--theme-font-size-lg\)
    ['\$font-size-xl']=var\(--theme-font-size-xl\)
    ['\$font-size-xxl']=var\(--theme-font-size-xxl\)
    
    # 字体权重变量
    ['\$font-weight-normal']=var\(--theme-font-weight-normal\)
    ['\$font-weight-medium']=var\(--theme-font-weight-medium\)
    ['\$font-weight-semibold']=var\(--theme-font-weight-semibold\)
    ['\$font-weight-bold']=var\(--theme-font-weight-bold\)
    
    # 圆角变量
    ['\$border-radius-xs']=var\(--theme-border-radius-xs\)
    ['\$border-radius-sm']=var\(--theme-border-radius-sm\)
    ['\$border-radius-base']=var\(--theme-border-radius-base\)
    ['\$border-radius-md']=var\(--theme-border-radius-md\)
    ['\$border-radius-lg']=var\(--theme-border-radius-lg\)
    
    # 响应式断点变量
    ['\$breakpoint-xs']=var\(--theme-breakpoint-xs\)
    ['\$breakpoint-sm']=var\(--theme-breakpoint-sm\)
    ['\$breakpoint-md']=var\(--theme-breakpoint-md\)
    ['\$breakpoint-lg']=var\(--theme-breakpoint-lg\)
    ['\$breakpoint-xl']=var\(--theme-breakpoint-xl\)
    ['\$breakpoint-xxl']=var\(--theme-breakpoint-xxl\)
    
    # 其他变量
    ['\$transition-base']=var\(--theme-transition-base\)
    ['\$box-shadow-base']=var\(--theme-box-shadow-base\)
    ['\$z-index-sticky']=var\(--theme-z-index-sticky\)
    ['\$z-index-fixed']=var\(--theme-z-index-fixed\)
    ['\$header-height']=var\(--theme-header-height\)
    ['\$header-bg-light']=var\(--theme-bg-primary\)
    ['\$sidebar-bg-light']=var\(--theme-bg-primary\)
)

# 统计信息
total_files=0
processed_files=0
total_replacements=0

# 处理每个文件
for file in "${SCSS_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_info "🔧 处理文件: $file"
        total_files=$((total_files + 1))
        
        # 创建备份（如果还没有备份）
        if [ ! -f "$BACKUP_DIR/$(basename $file).backup" ]; then
            cp "$file" "$BACKUP_DIR/$(basename $file).backup"
        fi
        
        file_replacements=0
        
        # 应用所有变量替换
        for scss_var in "${!VARIABLE_MAP[@]}"; do
            css_var="${VARIABLE_MAP[$scss_var]}"
            
            # 使用 sed 进行替换，确保只替换完整的变量名
            count=$(grep -o "$scss_var\b" "$file" | wc -l)
            if [ "$count" -gt 0 ]; then
                sed -i.tmp "s|$scss_var\b|$css_var|g" "$file"
                file_replacements=$((file_replacements + count))
                log_success "  • $scss_var → $css_var ($count 次)"
            fi
        done
        
        # 删除临时文件
        rm -f "$file.tmp"
        
        if [ "$file_replacements" -gt 0 ]; then
            log_success "✅ $file: 完成 $file_replacements 处替换"
            processed_files=$((processed_files + 1))
            total_replacements=$((total_replacements + file_replacements))
        else
            log_info "ℹ️  $file: 无需替换"
        fi
    else
        log_warning "⚠️  文件不存在: $file"
    fi
done

# 输出最终报告
log_info "\n" 
echo "=============================="
log_success "📊 迁移完成报告"
echo "=============================="
log_info "📁 总文件数: $total_files"
log_success "✅ 处理成功: $processed_files"
log_success "🔄 总替换次数: $total_replacements"
log_info "📁 备份位置: $BACKUP_DIR"

# 验证项目是否能正常编译
log_info "\n🔍 验证项目编译状态..."
if command -v npm &> /dev/null; then
    log_info "正在检查项目依赖..."
    # 这里可以添加编译检查，但为了安全起见先跳过
    log_warning "请手动运行 'npm run dev' 验证项目是否正常编译"
else
    log_warning "未找到 npm，请手动验证项目编译状态"
fi

log_success "\n🎉 SCSS 变量迁移脚本执行完成！"
log_info "💡 下一步："
log_info "   1. 运行 'npm run dev' 验证项目编译"
log_info "   2. 测试亮色和暗色主题切换"
log_info "   3. 验证响应式布局是否正常"
log_info "   4. 如有问题，可从 $BACKUP_DIR 恢复文件"
