import { create } from 'zustand';
import { MenuItem } from '../types';

// 🎯 侧边栏状态接口
interface SidebarState {
  // 基础状态
  collapsed: boolean;
  isMobile: boolean;
  
  // 弹出层状态
  popoverVisible: boolean;
  popoverMenuItem: MenuItem | null;
  popoverPosition: { top: number; left: number };
  
  // 悬浮状态
  hoveredMenuKey: string | null;
  hoveredElement: HTMLElement | null;
  isHovering: boolean;
  
  // 定时器管理
  hoverTimer: NodeJS.Timeout | null;
  closeTimer: NodeJS.Timeout | null;
  
  // 动画状态
  isAnimating: boolean;
  animationType: 'show' | 'hide' | null;
}

// 🎯 侧边栏操作接口
interface SidebarActions {
  // 基础操作
  setCollapsed: (collapsed: boolean) => void;
  setIsMobile: (isMobile: boolean) => void;
  
  // 弹出层操作
  showPopover: (menuItem: MenuItem, position: { top: number; left: number }) => void;
  hidePopover: () => void;
  setPopoverPosition: (position: { top: number; left: number }) => void;
  
  // 悬浮操作
  setHoveredMenu: (key: string | null, element: HTMLElement | null) => void;
  setIsHovering: (hovering: boolean) => void;
  
  // 定时器操作
  setHoverTimer: (timer: NodeJS.Timeout | null) => void;
  setCloseTimer: (timer: NodeJS.Timeout | null) => void;
  clearAllTimers: () => void;
  
  // 动画操作
  setAnimating: (animating: boolean, type?: 'show' | 'hide' | null) => void;
  
  // 复合操作
  handleMenuHover: (menuItem: MenuItem, element: HTMLElement) => void;
  handleMenuLeave: () => void;
  forceShowPopover: (menuItem: MenuItem, element: HTMLElement) => void;
}

// 🎯 完整的 Store 类型
type SidebarStore = SidebarState & SidebarActions;

// 🎯 初始状态
const initialState: SidebarState = {
  collapsed: false,
  isMobile: false,
  popoverVisible: false,
  popoverMenuItem: null,
  popoverPosition: { top: 0, left: 0 },
  hoveredMenuKey: null,
  hoveredElement: null,
  isHovering: false,
  hoverTimer: null,
  closeTimer: null,
  isAnimating: false,
  animationType: null,
};

// 🎯 创建 Zustand Store
export const useSidebarStore = create<SidebarStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // 🔧 基础操作
    setCollapsed: (collapsed) => {
      const current = get();
      if (current.collapsed !== collapsed) {
        set({ collapsed });
        // 收缩时自动隐藏弹出层
        if (collapsed && current.popoverVisible) {
          setTimeout(() => get().hidePopover(), 0);
        }
      }
    },

    setIsMobile: (isMobile) => {
      const current = get();
      if (current.isMobile !== isMobile) {
        set({ isMobile });
        // 移动端时隐藏弹出层
        if (isMobile && current.popoverVisible) {
          setTimeout(() => get().hidePopover(), 0);
        }
      }
    },

    // 🔧 弹出层操作
    showPopover: (menuItem, position) => {
      const current = get();

      // 如果已经显示相同的菜单项，不重复操作
      if (current.popoverVisible && current.popoverMenuItem?.key === menuItem.key) {
        return;
      }

      current.clearAllTimers();

      set({
        popoverVisible: true,
        popoverMenuItem: menuItem,
        popoverPosition: position,
        isAnimating: true,
        animationType: 'show',
      });

      // 动画完成后重置状态
      setTimeout(() => {
        const state = get();
        if (state.animationType === 'show') { // 确保没有被其他操作覆盖
          set({ isAnimating: false, animationType: null });
        }
      }, 250);
    },

    hidePopover: () => {
      const current = get();
      if (!current.popoverVisible && !current.isAnimating) {
        return; // 已经隐藏，避免重复操作
      }

      current.clearAllTimers();

      set({
        isAnimating: true,
        animationType: 'hide',
      });

      // 动画完成后隐藏
      setTimeout(() => {
        const state = get();
        if (state.animationType === 'hide') { // 确保没有被其他操作覆盖
          set({
            popoverVisible: false,
            popoverMenuItem: null,
            hoveredMenuKey: null,
            hoveredElement: null,
            isHovering: false,
            isAnimating: false,
            animationType: null,
          });
        }
      }, 200);
    },

    setPopoverPosition: (position) => set({ popoverPosition: position }),

    // 🔧 悬浮操作
    setHoveredMenu: (key, element) => set({ hoveredMenuKey: key, hoveredElement: element }),
    setIsHovering: (hovering) => set({ isHovering: hovering }),

    // 🔧 定时器操作
    setHoverTimer: (timer) => {
      const current = get().hoverTimer;
      if (current) clearTimeout(current);
      set({ hoverTimer: timer });
    },

    setCloseTimer: (timer) => {
      const current = get().closeTimer;
      if (current) clearTimeout(current);
      set({ closeTimer: timer });
    },

    clearAllTimers: () => {
      const { hoverTimer, closeTimer } = get();
      if (hoverTimer) {
        clearTimeout(hoverTimer);
      }
      if (closeTimer) {
        clearTimeout(closeTimer);
      }
      set({ hoverTimer: null, closeTimer: null });
    },

    // 🔧 动画操作
    setAnimating: (animating, type = null) => set({ isAnimating: animating, animationType: type }),

    // 🔧 简化的复合操作
    handleMenuHover: (menuItem, element) => {
      const state = get();

      if (!state.collapsed || state.isMobile || !menuItem.children?.length) return;

      // 清理定时器
      if (state.hoverTimer) clearTimeout(state.hoverTimer);
      if (state.closeTimer) clearTimeout(state.closeTimer);

      // 设置悬浮状态
      set({
        hoveredMenuKey: menuItem.key,
        hoveredElement: element,
        isHovering: true,
        hoverTimer: null,
        closeTimer: null,
      });

      // 计算位置
      const sidebarRect = element.closest('.v2-sidebar')?.getBoundingClientRect();
      const targetRect = element.getBoundingClientRect();

      if (sidebarRect && targetRect) {
        const position = {
          top: Math.max(10, targetRect.top + (targetRect.height / 2) - 20),
          left: sidebarRect.right + 8,
        };

        // 检查是否超出屏幕
        const maxTop = window.innerHeight - 200;
        if (position.top > maxTop) {
          position.top = maxTop;
        }

        // 延迟显示
        const timer = setTimeout(() => {
          const currentState = get();
          if (currentState.hoveredMenuKey === menuItem.key) {
            set({
              popoverVisible: true,
              popoverMenuItem: menuItem,
              popoverPosition: position,
              hoverTimer: null,
            });
          }
        }, 180);

        set({ hoverTimer: timer });
      }
    },

    handleMenuLeave: () => {
      const state = get();

      set({ isHovering: false });

      // 延迟关闭
      const timer = setTimeout(() => {
        const currentState = get();
        if (!currentState.isHovering) {
          set({
            popoverVisible: false,
            popoverMenuItem: null,
            hoveredMenuKey: null,
            hoveredElement: null,
            closeTimer: null,
          });
        }
      }, 450);

      set({ closeTimer: timer });
    },

    forceShowPopover: (menuItem, element) => {
      const state = get();

      if (!state.collapsed || state.isMobile || !menuItem.children?.length) return;

      // 强制计算位置并显示
      const sidebarRect = element.closest('.v2-sidebar')?.getBoundingClientRect();
      const targetRect = element.getBoundingClientRect();

      if (sidebarRect && targetRect) {
        const position = {
          top: Math.max(10, targetRect.top + (targetRect.height / 2) - 20),
          left: sidebarRect.right + 8,
        };

        const maxTop = window.innerHeight - 200;
        if (position.top > maxTop) {
          position.top = maxTop;
        }

        set({
          popoverVisible: true,
          popoverMenuItem: menuItem,
          popoverPosition: position,
        });
      }
    },
  }))
);

// 🎯 选择器 hooks
export const usePopoverState = () => useSidebarStore((state) => ({
  visible: state.popoverVisible,
  menuItem: state.popoverMenuItem,
  position: state.popoverPosition,
  isAnimating: state.isAnimating,
  animationType: state.animationType,
}));

export const useHoverState = () => useSidebarStore((state) => ({
  hoveredMenuKey: state.hoveredMenuKey,
  hoveredElement: state.hoveredElement,
  isHovering: state.isHovering,
}));

export const useSidebarActions = () => useSidebarStore((state) => ({
  setCollapsed: state.setCollapsed,
  setIsMobile: state.setIsMobile,
  handleMenuHover: state.handleMenuHover,
  handleMenuLeave: state.handleMenuLeave,
  forceShowPopover: state.forceShowPopover,
  hidePopover: state.hidePopover,
  setIsHovering: state.setIsHovering,
  clearAllTimers: state.clearAllTimers,
}));
