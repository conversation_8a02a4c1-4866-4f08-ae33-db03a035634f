import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { MenuItem } from '../types';

// 🎯 侧边栏状态接口
interface SidebarState {
  // 基础状态
  collapsed: boolean;
  isMobile: boolean;
  
  // 弹出层状态
  popoverVisible: boolean;
  popoverMenuItem: MenuItem | null;
  popoverPosition: { top: number; left: number };
  
  // 悬浮状态
  hoveredMenuKey: string | null;
  hoveredElement: HTMLElement | null;
  isHovering: boolean;
  
  // 定时器管理
  hoverTimer: NodeJS.Timeout | null;
  closeTimer: NodeJS.Timeout | null;
  
  // 动画状态
  isAnimating: boolean;
  animationType: 'show' | 'hide' | null;
}

// 🎯 侧边栏操作接口
interface SidebarActions {
  // 基础操作
  setCollapsed: (collapsed: boolean) => void;
  setIsMobile: (isMobile: boolean) => void;
  
  // 弹出层操作
  showPopover: (menuItem: MenuItem, position: { top: number; left: number }) => void;
  hidePopover: () => void;
  setPopoverPosition: (position: { top: number; left: number }) => void;
  
  // 悬浮操作
  setHoveredMenu: (key: string | null, element: HTMLElement | null) => void;
  setIsHovering: (hovering: boolean) => void;
  
  // 定时器操作
  setHoverTimer: (timer: NodeJS.Timeout | null) => void;
  setCloseTimer: (timer: NodeJS.Timeout | null) => void;
  clearAllTimers: () => void;
  
  // 动画操作
  setAnimating: (animating: boolean, type?: 'show' | 'hide' | null) => void;
  
  // 复合操作
  handleMenuHover: (menuItem: MenuItem, element: HTMLElement) => void;
  handleMenuLeave: () => void;
  forceShowPopover: (menuItem: MenuItem, element: HTMLElement) => void;
}

// 🎯 完整的 Store 类型
type SidebarStore = SidebarState & SidebarActions;

// 🎯 初始状态
const initialState: SidebarState = {
  collapsed: false,
  isMobile: false,
  popoverVisible: false,
  popoverMenuItem: null,
  popoverPosition: { top: 0, left: 0 },
  hoveredMenuKey: null,
  hoveredElement: null,
  isHovering: false,
  hoverTimer: null,
  closeTimer: null,
  isAnimating: false,
  animationType: null,
};

// 🎯 创建 Zustand Store
export const useSidebarStore = create<SidebarStore>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    // 🔧 基础操作
    setCollapsed: (collapsed) => {
      const current = get();
      if (current.collapsed !== collapsed) {
        set({ collapsed });
        // 收缩时自动隐藏弹出层
        if (collapsed && current.popoverVisible) {
          setTimeout(() => get().hidePopover(), 0);
        }
      }
    },

    setIsMobile: (isMobile) => {
      const current = get();
      if (current.isMobile !== isMobile) {
        set({ isMobile });
        // 移动端时隐藏弹出层
        if (isMobile && current.popoverVisible) {
          setTimeout(() => get().hidePopover(), 0);
        }
      }
    },

    // 🔧 弹出层操作
    showPopover: (menuItem, position) => {
      const { clearAllTimers } = get();
      clearAllTimers();
      
      set({
        popoverVisible: true,
        popoverMenuItem: menuItem,
        popoverPosition: position,
        isAnimating: true,
        animationType: 'show',
      });

      // 动画完成后重置状态
      setTimeout(() => {
        set({ isAnimating: false, animationType: null });
      }, 250);
    },

    hidePopover: () => {
      const current = get();
      if (!current.popoverVisible && !current.isAnimating) {
        return; // 已经隐藏，避免重复操作
      }

      current.clearAllTimers();

      set({
        isAnimating: true,
        animationType: 'hide',
      });

      // 动画完成后隐藏
      setTimeout(() => {
        const state = get();
        if (state.animationType === 'hide') { // 确保没有被其他操作覆盖
          set({
            popoverVisible: false,
            popoverMenuItem: null,
            hoveredMenuKey: null,
            hoveredElement: null,
            isHovering: false,
            isAnimating: false,
            animationType: null,
          });
        }
      }, 200);
    },

    setPopoverPosition: (position) => set({ popoverPosition: position }),

    // 🔧 悬浮操作
    setHoveredMenu: (key, element) => set({ hoveredMenuKey: key, hoveredElement: element }),
    setIsHovering: (hovering) => set({ isHovering: hovering }),

    // 🔧 定时器操作
    setHoverTimer: (timer) => {
      const current = get().hoverTimer;
      if (current) clearTimeout(current);
      set({ hoverTimer: timer });
    },

    setCloseTimer: (timer) => {
      const current = get().closeTimer;
      if (current) clearTimeout(current);
      set({ closeTimer: timer });
    },

    clearAllTimers: () => {
      const { hoverTimer, closeTimer } = get();
      if (hoverTimer) {
        clearTimeout(hoverTimer);
      }
      if (closeTimer) {
        clearTimeout(closeTimer);
      }
      set({ hoverTimer: null, closeTimer: null });
    },

    // 🔧 动画操作
    setAnimating: (animating, type = null) => set({ isAnimating: animating, animationType: type }),

    // 🔧 复合操作
    handleMenuHover: (menuItem, element) => {
      const { collapsed, isMobile, setHoveredMenu, setIsHovering, clearAllTimers, showPopover } = get();
      
      if (!collapsed || isMobile || !menuItem.children?.length) return;

      clearAllTimers();
      setHoveredMenu(menuItem.key, element);
      setIsHovering(true);

      // 计算位置
      const sidebarRect = element.closest('.v2-sidebar')?.getBoundingClientRect();
      const targetRect = element.getBoundingClientRect();

      if (sidebarRect && targetRect) {
        const position = {
          top: Math.max(10, targetRect.top + (targetRect.height / 2) - 20),
          left: sidebarRect.right + 8,
        };

        // 检查是否超出屏幕
        const maxTop = window.innerHeight - 200;
        if (position.top > maxTop) {
          position.top = maxTop;
        }

        // 延迟显示，提供平滑体验
        const timer = setTimeout(() => {
          showPopover(menuItem, position);
        }, 180);

        get().setHoverTimer(timer);
      }
    },

    handleMenuLeave: () => {
      const { setIsHovering, setCloseTimer, hidePopover } = get();
      
      setIsHovering(false);

      // 延迟关闭，给用户时间移回
      const timer = setTimeout(() => {
        const { isHovering } = get();
        if (!isHovering) {
          hidePopover();
        }
      }, 450);

      setCloseTimer(timer);
    },

    forceShowPopover: (menuItem, element) => {
      const { collapsed, isMobile, showPopover } = get();
      
      if (!collapsed || isMobile || !menuItem.children?.length) return;

      // 强制计算位置并显示
      const sidebarRect = element.closest('.v2-sidebar')?.getBoundingClientRect();
      const targetRect = element.getBoundingClientRect();

      if (sidebarRect && targetRect) {
        const position = {
          top: Math.max(10, targetRect.top + (targetRect.height / 2) - 20),
          left: sidebarRect.right + 8,
        };

        const maxTop = window.innerHeight - 200;
        if (position.top > maxTop) {
          position.top = maxTop;
        }

        showPopover(menuItem, position);
      }
    },
  }))
);

// 🎯 选择器 hooks
export const usePopoverState = () => useSidebarStore((state) => ({
  visible: state.popoverVisible,
  menuItem: state.popoverMenuItem,
  position: state.popoverPosition,
  isAnimating: state.isAnimating,
  animationType: state.animationType,
}));

export const useHoverState = () => useSidebarStore((state) => ({
  hoveredMenuKey: state.hoveredMenuKey,
  hoveredElement: state.hoveredElement,
  isHovering: state.isHovering,
}));

export const useSidebarActions = () => useSidebarStore((state) => ({
  setCollapsed: state.setCollapsed,
  setIsMobile: state.setIsMobile,
  handleMenuHover: state.handleMenuHover,
  handleMenuLeave: state.handleMenuLeave,
  forceShowPopover: state.forceShowPopover,
  hidePopover: state.hidePopover,
  setIsHovering: state.setIsHovering,
  clearAllTimers: state.clearAllTimers,
}));
