import { StateCreator } from 'zustand';

// 主题类型定义
export type ThemeMode = 'light' | 'dark';
export type ThemePreference = 'light' | 'dark' | 'system';

// 主题状态接口
export interface ThemeState {
  // 当前实际应用的主题模式
  theme: ThemeMode;
  // 用户偏好设置（system表示跟随系统）
  preference: ThemePreference;
  // 系统主题
  systemTheme: ThemeMode;
  // 是否为手动设置（用于判断是否覆盖系统检测）
  isManuallySet: boolean;
}

// 主题操作接口
export interface ThemeActions {
  // 设置主题偏好
  setThemePreference: (preference: ThemePreference) => void;
  // 切换主题（在 light 和 dark 之间切换）
  toggleTheme: () => void;
  // 初始化主题
  initializeTheme: () => void;
  // 更新系统主题
  updateSystemTheme: (systemTheme: ThemeMode) => void;
}

// 从 localStorage 获取保存的主题模式
const getSavedTheme = (): ThemeMode => {
  if (typeof window === 'undefined') return 'light';
  const saved = localStorage.getItem('v2-admin-theme');
  return (saved === 'dark' || saved === 'light') ? saved : 'light';
};

// 应用主题到DOM
const applyThemeToDOM = (theme: ThemeMode) => {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', theme);
  }
};

// 默认状态
const defaultThemeState: ThemeState = {
  theme: getSavedTheme(),
};

// 创建主题 slice
export const createThemeSlice: StateCreator<
  ThemeState & ThemeActions,
  [],
  [],
  ThemeState & ThemeActions
> = (set, get, api) => ({
  ...defaultThemeState,

  setTheme: (theme: ThemeMode) => {
    // 直接设置主题
    set({ theme });

    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('v2-admin-theme', theme);
    }

    // 应用到DOM
    applyThemeToDOM(theme);
  },

  toggleTheme: () => {
    const { theme } = get();
    const newTheme = theme === 'light' ? 'dark' : 'light';
    get().setTheme(newTheme);
  },

  initializeTheme: () => {
    const savedTheme = getSavedTheme();
    set({ theme: savedTheme });
    applyThemeToDOM(savedTheme);
  },
});

// 导出类型
export type ThemeSlice = ThemeState & ThemeActions;
