import { StateCreator } from 'zustand';

// 主题类型定义
export type ThemeMode = 'light' | 'dark';
export type ThemePreference = 'light' | 'dark' | 'system';

// 主题状态接口
export interface ThemeState {
  // 当前实际应用的主题模式
  theme: ThemeMode;
  // 用户偏好设置（system表示跟随系统）
  preference: ThemePreference;
  // 系统主题
  systemTheme: ThemeMode;
  // 是否为手动设置（用于判断是否覆盖系统检测）
  isManuallySet: boolean;
}

// 主题操作接口
export interface ThemeActions {
  // 设置主题偏好
  setThemePreference: (preference: ThemePreference) => void;
  // 切换主题（在 light 和 dark 之间切换）
  toggleTheme: () => void;
  // 初始化主题
  initializeTheme: () => void;
  // 更新系统主题
  updateSystemTheme: (systemTheme: ThemeMode) => void;
}

// 检测系统主题偏好
const getSystemTheme = (): ThemeMode => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// 从 localStorage 获取保存的主题偏好
const getSavedThemePreference = (): ThemePreference => {
  if (typeof window === 'undefined') return 'system';
  const saved = localStorage.getItem('v2-admin-theme-preference');
  return (saved === 'dark' || saved === 'light' || saved === 'system') ? saved as ThemePreference : 'system';
};

// 检查是否为手动设置
const getIsManuallySet = (): boolean => {
  if (typeof window === 'undefined') return false;
  return localStorage.getItem('v2-admin-theme-manual') === 'true';
};

// 计算实际应用的主题
const calculateActualTheme = (preference: ThemePreference, systemTheme: ThemeMode): ThemeMode => {
  return preference === 'system' ? systemTheme : preference;
};

// 应用主题到DOM
const applyThemeToDOM = (theme: ThemeMode) => {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', theme);
  }
};

// 保存主题偏好到 localStorage
const saveThemePreference = (preference: ThemePreference, isManual: boolean) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('v2-admin-theme-preference', preference);
    localStorage.setItem('v2-admin-theme-manual', isManual.toString());
  }
};

// 默认状态
const systemTheme = getSystemTheme();
const preference = getSavedThemePreference();
const isManuallySet = getIsManuallySet();

const defaultThemeState: ThemeState = {
  theme: calculateActualTheme(preference, systemTheme),
  preference,
  systemTheme,
  isManuallySet,
};

// 创建主题 slice
export const createThemeSlice: StateCreator<
  ThemeState & ThemeActions,
  [],
  [],
  ThemeState & ThemeActions
> = (set, get) => ({
  ...defaultThemeState,

  setThemePreference: (preference: ThemePreference) => {
    const { systemTheme } = get();
    const actualTheme = calculateActualTheme(preference, systemTheme);
    const isManual = preference !== 'system';

    // 更新状态
    set({
      preference,
      theme: actualTheme,
      isManuallySet: isManual
    });

    // 保存偏好
    saveThemePreference(preference, isManual);

    // 应用到DOM
    applyThemeToDOM(actualTheme);
  },

  toggleTheme: () => {
    const { theme } = get();
    const newPreference: ThemePreference = theme === 'light' ? 'dark' : 'light';
    get().setThemePreference(newPreference);
  },

  updateSystemTheme: (newSystemTheme: ThemeMode) => {
    const { preference } = get();
    const actualTheme = calculateActualTheme(preference, newSystemTheme);

    set({
      systemTheme: newSystemTheme,
      theme: actualTheme
    });

    // 如果当前是跟随系统，则应用新的系统主题
    if (preference === 'system') {
      applyThemeToDOM(actualTheme);
    }
  },

  initializeTheme: () => {
    const currentSystemTheme = getSystemTheme();
    const savedPreference = getSavedThemePreference();
    const savedIsManual = getIsManuallySet();
    const actualTheme = calculateActualTheme(savedPreference, currentSystemTheme);

    set({
      theme: actualTheme,
      preference: savedPreference,
      systemTheme: currentSystemTheme,
      isManuallySet: savedIsManual
    });

    applyThemeToDOM(actualTheme);

    // 监听系统主题变化
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleSystemThemeChange = (e: MediaQueryListEvent) => {
        const newSystemTheme: ThemeMode = e.matches ? 'dark' : 'light';
        get().updateSystemTheme(newSystemTheme);
      };

      // 添加监听器
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleSystemThemeChange);
      } else {
        // 兼容旧版浏览器
        mediaQuery.addListener(handleSystemThemeChange);
      }
    }
  },
});

// 导出类型
export type ThemeSlice = ThemeState & ThemeActions;
