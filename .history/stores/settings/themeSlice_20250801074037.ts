import { StateCreator } from 'zustand';

// 主题类型定义 - 简化为只支持light和dark
export type ThemeMode = 'light' | 'dark';

// 主题状态接口
export interface ThemeState {
  // 当前主题模式（跟随系统，可临时手动切换）
  theme: ThemeMode;
}

// 主题操作接口
export interface ThemeActions {
  // 设置主题模式（临时切换，不保存）
  setTheme: (theme: ThemeMode) => void;
  // 切换主题（在 light 和 dark 之间切换）
  toggleTheme: () => void;
  // 初始化主题（检测系统主题）
  initializeTheme: () => void;
}

// 检测系统主题偏好
const getSystemTheme = (): ThemeMode => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// 应用主题到DOM
const applyThemeToDOM = (theme: ThemeMode) => {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', theme);
  }
};

// 默认状态 - 跟随系统主题
const defaultThemeState: ThemeState = {
  theme: getSystemTheme(),
};

// 创建主题 slice
export const createThemeSlice: StateCreator<
  ThemeState & ThemeActions,
  [],
  [],
  ThemeState & ThemeActions
> = (set, get) => ({
  ...defaultThemeState,

  setTheme: (theme: ThemeMode) => {
    // 临时设置主题（不保存到localStorage）
    set({ theme });
    applyThemeToDOM(theme);
  },

  toggleTheme: () => {
    const { theme } = get();
    const newTheme: ThemeMode = theme === 'light' ? 'dark' : 'light';
    get().setTheme(newTheme);
  },

  initializeTheme: () => {
    const systemTheme = getSystemTheme();
    set({ theme: systemTheme });
    applyThemeToDOM(systemTheme);

    // 监听系统主题变化，自动跟随
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleSystemThemeChange = (e: MediaQueryListEvent) => {
        const newSystemTheme: ThemeMode = e.matches ? 'dark' : 'light';
        set({ theme: newSystemTheme });
        applyThemeToDOM(newSystemTheme);
      };

      // 添加监听器
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleSystemThemeChange);
      } else {
        // 兼容旧版浏览器
        mediaQuery.addListener(handleSystemThemeChange);
      }
    }
  },
});

// 导出类型
export type ThemeSlice = ThemeState & ThemeActions;

// 便利函数：检查当前是否为暗色主题
export const isDarkTheme = (theme: ThemeMode): boolean => theme === 'dark';

// 便利函数：检查是否应该显示主题切换按钮（仅桌面端）
export const shouldShowThemeToggle = (): boolean => {
  if (typeof window === 'undefined') return false;

  // 检查是否为移动设备（768px以下）
  const isMobile = window.innerWidth < 768;

  return !isMobile;
};
