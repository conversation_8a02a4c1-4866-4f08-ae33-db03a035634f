/**
 * Ant Design 组件样式配置常量
 * 统一使用官方 API 方式进行样式定制，避免 CSS 强制覆盖
 */

// ===== Card 组件样式配置 =====

/**
 * Card 组件无内边距配置
 * 用于需要移除默认内边距的场景，如表格容器等
 */
export const CARD_ZERO_PADDING_STYLES = {
  body: {
    padding: 0,
    borderRadius: 0,
  }
} as const;

/**
 * Card 组件紧凑内边距配置
 * 用于需要较小内边距的场景
 */
export const CARD_COMPACT_PADDING_STYLES = {
  body: {
    padding: 12,
  }
} as const;

/**
 * Card 组件标准内边距配置
 * 用于标准内容展示场景
 */
export const CARD_STANDARD_PADDING_STYLES = {
  body: {
    padding: 24,
  }
} as const;

// ===== Table 组件样式配置 =====

/**
 * Table 组件紧凑样式配置
 * 用于数据密集型表格
 */
export const TABLE_COMPACT_STYLES = {
  header: {
    backgroundColor: 'var(--theme-table-header-bg)',
    borderBottom: '1px solid var(--theme-border-color-split)',
  },
  body: {
    backgroundColor: 'var(--theme-bg-primary)',
  }
} as const;

/**
 * Table 组件无阴影配置
 * 用于系统状态页面等扁平化设计
 */
export const TABLE_NO_SHADOW_STYLES = {
  header: {
    backgroundColor: 'var(--theme-table-header-bg)',
    boxShadow: 'none',
    borderBottom: '1px solid var(--theme-border-color-split)',
  },
  body: {
    boxShadow: 'none',
    backgroundColor: 'var(--theme-bg-primary)',
  }
} as const;

/**
 * Table 组件小尺寸样式配置
 * 用于磁盘表格等紧凑显示场景
 */
export const TABLE_SMALL_STYLES = {
  header: {
    padding: '8px 12px',
    fontSize: 12,
    fontWeight: 500,
    backgroundColor: 'var(--theme-table-header-bg)',
    borderBottom: '1px solid var(--theme-border-color-split)',
    color: 'var(--theme-text-primary)',
  },
  body: {
    padding: '8px 12px',
    fontSize: 12,
    backgroundColor: 'var(--theme-bg-primary)',
    borderBottom: '1px solid var(--theme-border-color-split)',
    color: 'var(--theme-text-primary)',
  }
} as const;

// ===== Tabs 组件样式配置 =====

/**
 * Tabs 组件自定义样式配置
 * 用于助手管理等页面
 */
export const TABS_CUSTOM_STYLES = {
  tab: {
    padding: '16px 24px',
    fontWeight: 500,
    fontSize: 14,
  },
  tabpane: {
    padding: 16,
  }
} as const;

// ===== Button 组件样式配置 =====

/**
 * Button 组件无阴影配置
 * 用于扁平化设计场景
 */
export const BUTTON_NO_SHADOW_STYLES = {
  boxShadow: 'none',
} as const;

/**
 * Button 组件统一样式配置
 * 用于统一按钮高度和样式
 */
export const BUTTON_UNIFIED_STYLES = {
  height: 32,
  borderRadius: 6,
  fontSize: 14,
  fontWeight: 500,
} as const;

// ===== Statistic 组件样式配置 =====

/**
 * Statistic 组件自定义样式配置
 * 用于仪表板统计卡片
 */
export const STATISTIC_CUSTOM_STYLES = {
  title: {
    fontSize: 14,
    color: 'var(--theme-text-secondary)',
    marginBottom: 4,
    transition: 'color 0.2s ease',
  },
  content: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'var(--theme-text-primary)',
    transition: 'color 0.2s ease',
  }
} as const;

// ===== Progress 组件样式配置 =====

/**
 * Progress 组件紧凑样式配置
 * 用于统计卡片中的进度条
 */
export const PROGRESS_COMPACT_STYLES = {
  marginBottom: 0,
} as const;

// ===== Tag 组件样式配置 =====

/**
 * Tag 组件可选择样式配置
 * 用于时间筛选等场景
 */
export const TAG_CHECKABLE_STYLES = {
  borderRadius: 6,
  fontSize: 12,
  padding: '4px 12px',
  cursor: 'pointer',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
} as const;

// ===== Modal 组件样式配置 =====

/**
 * Modal 组件样式配置
 * 用于统一模态框样式
 */
export const MODAL_STANDARD_STYLES = {
  mask: {
    backgroundColor: 'var(--theme-mask-bg)',
  },
  content: {
    backgroundColor: 'var(--theme-bg-primary)',
    borderRadius: 8,
  }
} as const;

// ===== Form 组件样式配置 =====

/**
 * Form 组件紧凑布局配置
 * 用于空间受限的表单场景
 */
export const FORM_COMPACT_STYLES = {
  item: {
    marginBottom: 16,
  }
} as const;

// ===== Input 组件样式配置 =====

/**
 * Input 组件全宽配置
 * 用于移动端等需要全宽的场景
 */
export const INPUT_FULL_WIDTH_STYLES = {
  width: '100%',
} as const;

/**
 * Input 组件搜索样式配置
 * 用于工具栏搜索框
 */
export const INPUT_SEARCH_STYLES = {
  borderRadius: 4,
} as const;

// ===== Select 组件样式配置 =====

/**
 * Select 组件自定义样式配置
 * 用于工具栏选择器
 */
export const SELECT_CUSTOM_STYLES = {
  selector: {
    borderRadius: 4,
    borderColor: 'var(--theme-border-color)',
    backgroundColor: 'var(--theme-bg-primary)',
    color: 'var(--theme-text-primary)',
  }
} as const;

// ===== Avatar 组件样式配置 =====

/**
 * Avatar 组件小尺寸配置
 * 用于移动端等小屏幕场景
 */
export const AVATAR_SMALL_STYLES = {
  width: 28,
  height: 28,
  fontSize: 12,
} as const;

// ===== Pagination 组件样式配置 =====

/**
 * Pagination 组件自定义样式配置
 * 用于助手管理等页面
 */
export const PAGINATION_CUSTOM_STYLES = {
  item: {
    color: 'var(--theme-text-secondary)',
    fontSize: 12,
  }
} as const;

// ===== 通用组件变体配置 =====

/**
 * 无边框变体
 * 适用于 Card、Table 等支持 variant 属性的组件
 */
export const BORDERLESS_VARIANT = 'borderless' as const;

/**
 * 填充变体
 * 适用于 Button 等组件
 */
export const FILLED_VARIANT = 'filled' as const;

// ===== 组件尺寸配置 =====

/**
 * 组件尺寸枚举
 */
export const COMPONENT_SIZES = {
  SMALL: 'small',
  MIDDLE: 'middle', 
  LARGE: 'large',
} as const;

// ===== 主题相关样式配置 =====

/**
 * 主题适配样式配置
 * 使用 CSS 变量确保主题兼容性
 */
export const THEME_ADAPTIVE_STYLES = {
  backgroundColor: 'var(--theme-bg-primary)',
  borderColor: 'var(--theme-border-color-split)',
  color: 'var(--theme-text-primary)',
} as const;

// ===== 无阴影样式配置 =====

/**
 * 通用无阴影配置
 * 用于系统状态页面等扁平化设计
 */
export const NO_SHADOW_STYLES = {
  boxShadow: 'none',
} as const;

// ===== 暗色主题特定样式配置 =====

/**
 * 暗色主题优化的 Card 样式配置
 * 提供更好的暗色主题对比度
 */
export const CARD_DARK_OPTIMIZED_STYLES = {
  body: {
    backgroundColor: 'var(--theme-bg-primary)',
    borderColor: 'var(--theme-border-color-split)',
    padding: 24,
  },
  header: {
    backgroundColor: 'var(--theme-bg-tertiary)',
    borderBottom: '1px solid var(--theme-border-color-split)',
    color: 'var(--theme-text-primary)',
  }
} as const;

/**
 * 暗色主题优化的 Button 样式配置
 * 提供更好的暗色主题可见性
 */
export const BUTTON_DARK_OPTIMIZED_STYLES = {
  backgroundColor: 'var(--theme-bg-tertiary)',
  borderColor: 'var(--theme-border-color)',
  color: 'var(--theme-text-primary)',
  boxShadow: 'none',
} as const;

/**
 * 暗色主题优化的 Input 样式配置
 * 提供更好的暗色主题输入体验
 */
export const INPUT_DARK_OPTIMIZED_STYLES = {
  backgroundColor: 'var(--theme-bg-primary)',
  borderColor: 'var(--theme-border-color)',
  color: 'var(--theme-text-primary)',
  placeholderColor: 'var(--theme-text-tertiary)',
} as const;

/**
 * Card 组件无阴影配置
 * 用于系统状态页面的卡片
 */
export const CARD_NO_SHADOW_STYLES = {
  body: {
    boxShadow: 'none',
    padding: 16,
  },
  header: {
    backgroundColor: 'var(--theme-bg-tertiary)',
    borderBottom: '1px solid var(--theme-border-color-split)',
    minHeight: 40,
    fontSize: 14,
    fontWeight: 500,
  }
} as const;

/**
 * Card 组件系统卡片样式配置
 * 用于系统状态页面
 */
export const CARD_SYSTEM_STYLES = {
  body: {
    padding: 16,
    boxShadow: 'none',
  },
  header: {
    backgroundColor: 'var(--theme-bg-tertiary)',
    borderBottom: '1px solid var(--theme-border-color-split)',
    borderRadius: '8px 8px 0 0',
  }
} as const;

// ===== 类型定义 =====

/**
 * Card 样式配置类型
 */
export type CardStylesConfig = typeof CARD_ZERO_PADDING_STYLES;

/**
 * 组件尺寸类型
 */
export type ComponentSize = typeof COMPONENT_SIZES[keyof typeof COMPONENT_SIZES];

/**
 * 组件变体类型
 */
export type ComponentVariant = typeof BORDERLESS_VARIANT | typeof FILLED_VARIANT;
