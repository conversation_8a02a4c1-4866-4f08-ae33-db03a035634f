// V2 Admin 入口文件 - 统一导出所有组件和工具

// 类型定义
export * from './types';

// Hooks (新的 Zustand-based hooks)
export { useResponsive } from './hooks/useResponsiveListener';
export { useTabsStore, useLayoutStore, useResponsiveStore, useRouteHistoryStore, useRecentVisitsStore } from './stores';

// 组件
export { default as Sidebar } from './components/Sidebar';
export { default as Header } from './components/Header';

// 布局
export { default as BasicLayout } from './layouts/BasicLayout';

// 工具函数
export {
  default as menuData,
  getMenuPathMap,
  getMenuKeyByPath,
  getParentKeys,
  getDirectParentKey,
  getDefaultOpenKeys,
  getMenuStateByPath,
  flattenMenuData,
  searchMenuItems
} from './utils/menuData';

// 样式文件 - 已迁移到 CSS 变量系统

// 版本信息
export const VERSION = '2.0.0';

// 默认配置
export const DEFAULT_CONFIG = {
  theme: 'light' as const,
  sidebarWidth: 240,
  sidebarCollapsedWidth: 80,
  headerHeight: 64,
  contentPadding: 24,
  breakpoints: {
    xs: 480,
    sm: 768,
    md: 1024,
    lg: 1440,
    xl: 1920,
    xxl: 2560,
  },
};
