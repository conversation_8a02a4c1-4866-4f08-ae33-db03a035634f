{"name": "v2-admin-standalone", "private": true, "version": "1.0.0", "type": "module", "description": "V2 Admin Interface - Standalone Project", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.8.9", "@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-react": "^0.4.9", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@types/moment": "^2.13.0", "antd": "^5.8.6", "axios": "^1.10.0", "classnames": "^2.3.2", "dayjs": "^1.11.9", "framer-motion": "^12.23.0", "js.foresight": "^3.2.1", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.6.0", "react-router-dom": "^6.15.0", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^24.0.10", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "glob": "^11.0.3", "postcss": "^8.5.6", "sass": "^1.64.2", "ts-unused-exports": "^11.0.1", "typescript": "^5.0.2", "vite": "^4.4.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}