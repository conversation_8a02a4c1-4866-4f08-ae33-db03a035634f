#!/usr/bin/env node

/**
 * SCSS 变量到 CSS 变量自动化迁移脚本
 * 批量替换项目中剩余的所有 SCSS 变量为 CSS 自定义属性
 */

const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// 变量映射表 - 基于 docs/scss-to-css-variables-migration.md
const variableMap = {
  // 间距变量
  '$spacing-xs': 'var(--theme-spacing-xs)',
  '$spacing-sm': 'var(--theme-spacing-sm)', 
  '$spacing-md': 'var(--theme-spacing-md)',
  '$spacing-lg': 'var(--theme-spacing-lg)',
  '$spacing-xl': 'var(--theme-spacing-xl)',
  
  // 字体大小变量
  '$font-size-xs': 'var(--theme-font-size-xs)',
  '$font-size-sm': 'var(--theme-font-size-sm)',
  '$font-size-base': 'var(--theme-font-size-base)',
  '$font-size-lg': 'var(--theme-font-size-lg)',
  '$font-size-xl': 'var(--theme-font-size-xl)',
  '$font-size-xxl': 'var(--theme-font-size-xxl)',
  
  // 字体权重变量
  '$font-weight-normal': 'var(--theme-font-weight-normal)',
  '$font-weight-medium': 'var(--theme-font-weight-medium)',
  '$font-weight-semibold': 'var(--theme-font-weight-semibold)',
  '$font-weight-bold': 'var(--theme-font-weight-bold)',
  
  // 圆角变量
  '$border-radius-xs': 'var(--theme-border-radius-xs)',
  '$border-radius-sm': 'var(--theme-border-radius-sm)',
  '$border-radius-base': 'var(--theme-border-radius-base)',
  '$border-radius-md': 'var(--theme-border-radius-md)',
  '$border-radius-lg': 'var(--theme-border-radius-lg)',
  
  // 颜色变量 - 注意顺序：长的在前，避免部分匹配
  '$primary-color-hover': 'var(--theme-primary-color-hover)',
  '$primary-color-light': 'var(--theme-primary-color-light)',
  '$primary-color': 'var(--theme-primary-color)',
  '$success-color': 'var(--theme-success-color)',
  '$warning-color': 'var(--theme-warning-color)',
  '$error-color': 'var(--theme-error-color)',
  '$info-color': 'var(--theme-info-color)',
  
  // 文本颜色变量
  '$text-color-primary': 'var(--theme-text-primary)',
  '$text-color-secondary': 'var(--theme-text-secondary)',
  '$text-color-tertiary': 'var(--theme-text-tertiary)',
  '$text-color-disabled': 'var(--theme-text-disabled)',
  '$text-color': 'var(--theme-text-primary)',
  
  // 背景颜色变量
  '$background-color-base': 'var(--theme-bg-secondary)',
  '$background-color-light': 'var(--theme-bg-tertiary)',
  '$background-color-white': 'var(--theme-bg-primary)',
  
  // 边框颜色变量
  '$border-color-base': 'var(--theme-border-color)',
  '$border-color-dark': 'var(--theme-border-color)',
  '$border-color-light': 'var(--theme-border-color-split)',
  '$border-color-split': 'var(--theme-border-color-split)',
  
  // 响应式断点变量
  '$breakpoint-xs': 'var(--theme-breakpoint-xs)',
  '$breakpoint-sm': 'var(--theme-breakpoint-sm)',
  '$breakpoint-md': 'var(--theme-breakpoint-md)',
  '$breakpoint-lg': 'var(--theme-breakpoint-lg)',
  '$breakpoint-xl': 'var(--theme-breakpoint-xl)',
  '$breakpoint-xxl': 'var(--theme-breakpoint-xxl)',
  
  // 阴影变量
  '$box-shadow-base': 'var(--theme-box-shadow-base)',
  '$box-shadow-light': 'var(--theme-box-shadow-light)',
  '$box-shadow-large': 'var(--theme-box-shadow-large)',
  '$box-shadow-hover': 'var(--theme-box-shadow-hover)',
  
  // 动画变量
  '$transition-base': 'var(--theme-transition-base)',
  
  // 布局变量
  '$header-height': 'var(--theme-header-height)',
  '$header-height-mobile': 'var(--theme-header-height-mobile)',
  '$content-padding': 'var(--theme-content-padding)',
  '$content-padding-mobile': 'var(--theme-content-padding-mobile)',
  
  // Z-index 变量
  '$z-index-sticky': 'var(--theme-z-index-sticky)',
  '$z-index-fixed': 'var(--theme-z-index-fixed)',
  '$z-index-modal-backdrop': 'var(--theme-z-index-modal-backdrop)',
  
  // 组件特定变量
  '$sidebar-bg-light': 'var(--theme-bg-primary)',
  '$sidebar-bg-dark': '#001529',
  '$sidebar-text-color-dark': 'rgba(255, 255, 255, 0.85)',
  '$header-bg-light': 'var(--theme-bg-primary)',
  '$header-bg-dark': 'var(--theme-bg-primary)',
  '$content-bg': 'var(--theme-bg-primary)',
  '$content-bg-dark': 'var(--theme-bg-primary)',
  '$item-hover-bg': 'var(--theme-bg-tertiary)',
  
  // 菜单系统变量
  '$menu-item-height': '46px',
  '$menu-icon-size': '18px',
  '$menu-item-margin-block': '0',
  '$menu-item-margin-inline': '0',
  
  // 行高系统
  '$line-height-tight': '1.25',
  '$line-height-base': '1.5'
};

// 目标文件列表
const targetFiles = [
  'pages/DashboardV2/style.scss',
  'pages/SystemSettings/style.scss',
  'pages/AssistantManagement/style.scss',
  'pages/UserListManagement/style.scss',
  'pages/OnlineUsersManagement/style.scss',
  'components/Sidebar/style.scss',
  'components/Header/style.scss',
  'components/UserAvatar/style.scss',
  'components/TanStackTable/style.scss'
];

// 统计信息
let stats = {
  totalFiles: 0,
  processedFiles: 0,
  totalReplacements: 0,
  errors: [],
  replacements: {}
};

/**
 * 日志输出函数
 */
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 创建备份文件
 */
function createBackup(filePath) {
  const backupPath = `${filePath}.backup.${Date.now()}`;
  try {
    fs.copyFileSync(filePath, backupPath);
    log(`  📁 备份创建: ${backupPath}`, 'cyan');
    return backupPath;
  } catch (error) {
    log(`  ❌ 备份失败: ${error.message}`, 'red');
    return null;
  }
}

/**
 * 处理单个文件
 */
function processFile(filePath) {
  const fullPath = path.resolve(filePath);
  
  if (!fs.existsSync(fullPath)) {
    log(`  ⚠️  文件不存在: ${filePath}`, 'yellow');
    return false;
  }
  
  log(`\n📄 处理文件: ${filePath}`, 'blue');
  
  // 创建备份
  const backupPath = createBackup(fullPath);
  if (!backupPath) {
    return false;
  }
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    let fileReplacements = 0;
    let fileReplacementDetails = [];
    
    // 按照映射表进行替换
    Object.entries(variableMap).forEach(([scssVar, cssVar]) => {
      // 使用正则表达式确保精确匹配，避免部分匹配
      const regex = new RegExp(`\\${scssVar}(?![a-zA-Z0-9_-])`, 'g');
      const matches = content.match(regex);
      
      if (matches) {
        content = content.replace(regex, cssVar);
        const count = matches.length;
        fileReplacements += count;
        fileReplacementDetails.push(`${scssVar} → ${cssVar} (${count}次)`);
        
        // 更新全局统计
        if (!stats.replacements[scssVar]) {
          stats.replacements[scssVar] = 0;
        }
        stats.replacements[scssVar] += count;
      }
    });
    
    // 写入更新后的内容
    fs.writeFileSync(fullPath, content, 'utf8');
    
    if (fileReplacements > 0) {
      log(`  ✅ 完成替换 ${fileReplacements} 处变量:`, 'green');
      fileReplacementDetails.forEach(detail => {
        log(`    • ${detail}`, 'green');
      });
    } else {
      log(`  ℹ️  未发现需要替换的变量`, 'cyan');
    }
    
    stats.totalReplacements += fileReplacements;
    stats.processedFiles++;
    
    return true;
    
  } catch (error) {
    log(`  ❌ 处理失败: ${error.message}`, 'red');
    stats.errors.push(`${filePath}: ${error.message}`);
    return false;
  }
}

/**
 * 主执行函数
 */
function main() {
  log('🚀 开始 SCSS 变量到 CSS 变量自动化迁移', 'bright');
  log('=' .repeat(60), 'cyan');
  
  stats.totalFiles = targetFiles.length;
  
  // 处理每个文件
  targetFiles.forEach(processFile);
  
  // 输出最终统计报告
  log('\n' + '='.repeat(60), 'cyan');
  log('📊 迁移完成报告', 'bright');
  log('='.repeat(60), 'cyan');
  
  log(`📁 总文件数: ${stats.totalFiles}`, 'blue');
  log(`✅ 处理成功: ${stats.processedFiles}`, 'green');
  log(`❌ 处理失败: ${stats.totalFiles - stats.processedFiles}`, 'red');
  log(`🔄 总替换次数: ${stats.totalReplacements}`, 'magenta');
  
  if (Object.keys(stats.replacements).length > 0) {
    log('\n📋 变量替换详情:', 'blue');
    Object.entries(stats.replacements)
      .sort(([,a], [,b]) => b - a)
      .forEach(([variable, count]) => {
        log(`  • ${variable}: ${count} 次`, 'cyan');
      });
  }
  
  if (stats.errors.length > 0) {
    log('\n❌ 错误列表:', 'red');
    stats.errors.forEach(error => {
      log(`  • ${error}`, 'red');
    });
  }
  
  log('\n🎉 迁移脚本执行完成!', 'green');
  log('💡 请运行项目验证替换结果是否正确', 'yellow');
}

// 执行脚本
if (require.main === module) {
  main();
}

module.exports = { processFile, variableMap };
