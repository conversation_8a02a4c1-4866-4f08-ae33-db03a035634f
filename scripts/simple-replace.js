const fs = require('fs');

// 简单的变量替换映射
const replacements = [
  ['$spacing-md', 'var(--theme-spacing-md)'],
  ['$spacing-lg', 'var(--theme-spacing-lg)'],
  ['$font-size-xs', 'var(--theme-font-size-xs)'],
  ['$font-size-sm', 'var(--theme-font-size-sm)'],
  ['$font-size-base', 'var(--theme-font-size-base)'],
  ['$font-size-lg', 'var(--theme-font-size-lg)'],
  ['$font-size-xl', 'var(--theme-font-size-xl)'],
  ['$font-weight-medium', 'var(--theme-font-weight-medium)'],
  ['$font-weight-semibold', 'var(--theme-font-weight-semibold)'],
  ['$font-weight-bold', 'var(--theme-font-weight-bold)'],
  ['$border-radius-base', 'var(--theme-border-radius-base)'],
  ['$border-radius-lg', 'var(--theme-border-radius-lg)'],
  ['$border-radius-sm', 'var(--theme-border-radius-sm)'],
  ['$primary-color-light', 'var(--theme-primary-color-light)'],
  ['$primary-color-hover', 'var(--theme-primary-color-hover)'],
  ['$primary-color', 'var(--theme-primary-color)'],
  ['$success-color', 'var(--theme-success-color)'],
  ['$breakpoint-md', 'var(--theme-breakpoint-md)'],
  ['$breakpoint-sm', 'var(--theme-breakpoint-sm)'],
  ['$transition-base', 'var(--theme-transition-base)'],
  ['$box-shadow-base', 'var(--theme-box-shadow-base)']
];

function replaceInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changes = 0;
    
    replacements.forEach(([from, to]) => {
      const regex = new RegExp(from.replace('$', '\\$'), 'g');
      const matches = content.match(regex);
      if (matches) {
        content = content.replace(regex, to);
        changes += matches.length;
        console.log(`  ${from} → ${to} (${matches.length}次)`);
      }
    });
    
    if (changes > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ ${filePath}: ${changes} 处替换完成`);
    } else {
      console.log(`ℹ️  ${filePath}: 无需替换`);
    }
    
    return changes;
  } catch (error) {
    console.error(`❌ ${filePath}: ${error.message}`);
    return 0;
  }
}

// 目标文件
const files = [
  'pages/DashboardV2/style.scss',
  'pages/SystemSettings/style.scss',
  'pages/AssistantManagement/style.scss',
  'pages/UserListManagement/style.scss',
  'components/Sidebar/style.scss',
  'components/Header/style.scss'
];

console.log('🚀 开始批量替换 SCSS 变量...\n');

let totalChanges = 0;
files.forEach(file => {
  console.log(`\n📄 处理: ${file}`);
  totalChanges += replaceInFile(file);
});

console.log(`\n🎉 替换完成！总计 ${totalChanges} 处变量被替换。`);
