import React, { useEffect } from "react";
import {
  Button,
  Table,
  Space,
  Form,
  Input,
  Select,
  Modal,
  Tag,
  Row,
  Col,
  Tooltip,
  Popconfirm,
  Alert,
  InputNumber,
  Switch
} from "antd";
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import {
  ICON_OPTIONS,
  CATEGORY_OPTIONS,
  STATUS_OPTIONS,
  STATUS_COLORS,
  CATEGORY_COLORS,
} from "../../types/faq";
import type { FAQSuggestion, CreateFAQSuggestionRequest } from "../../types/faq";
import { useFAQManagementStore } from "../../stores";

const { TextArea } = Input;
const { Option } = Select;

interface FAQManagementProps {
  onFormChange?: () => void;
}

/**
 * FAQ管理组件 - V2 Admin版本
 * 适配v2-admin设计系统
 */
const FAQManagementComponent: React.FC<FAQManagementProps> = ({
  onFormChange,
}) => {
  const [form] = Form.useForm<CreateFAQSuggestionRequest>();

  // 使用 Zustand store
  const {
    suggestions,
    pagination,
    loading,
    saving,
    deleting,
    error,
    modalVisible,
    editingItem,
    selectedRowKeys,
    loadSuggestions,
    createSuggestion,
    updateSuggestion,
    deleteSuggestion,
    batchDeleteSuggestions,
    updateSuggestionStatus,
    setModalVisible,
    setEditingItem,
    setSelectedRowKeys,
    setError,
  } = useFAQManagementStore();

  // 创建或更新FAQ建议
  const handleSave = async () => {
    try {
      const values = await form.validateFields();

      if (editingItem) {
        await updateSuggestion(editingItem.id, values);
      } else {
        await createSuggestion(values);
      }

      form.resetFields();

      if (onFormChange) {
        onFormChange();
      }
    } catch (error: any) {
      if (error.errorFields) {
        // Ant Design 表单验证错误
        const { message } = await import('antd');
        message.error("请检查表单输入");
      }
      // API 错误已在 store 中处理
    }
  };

  // 删除FAQ建议
  const handleDelete = async (id: string) => {
    await deleteSuggestion(id);

    if (onFormChange) {
      onFormChange();
    }
  };

  // 批量删除FAQ建议
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      const { message } = await import('antd');
      message.warning('请选择要删除的项目');
      return;
    }

    await batchDeleteSuggestions(selectedRowKeys as string[]);

    if (onFormChange) {
      onFormChange();
    }
  };

  // 更新状态
  const handleStatusChange = async (id: string, status: 'active' | 'inactive' | 'draft') => {
    await updateSuggestionStatus(id, status);

    if (onFormChange) {
      onFormChange();
    }
  };

  // 打开编辑模态框
  const handleEdit = (item: FAQSuggestion) => {
    setEditingItem(item);
    form.setFieldsValue(item);
    setModalVisible(true);
  };

  // 打开创建模态框
  const handleCreate = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
    setEditingItem(null);
    form.resetFields();
    setError(null);
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadSuggestions();
  }, [loadSuggestions]);

  // 表格列定义
  const columns = [
    {
      title: "图标",
      dataIndex: "icon",
      key: "icon",
      width: 60,
      render: (icon: string) => (
        <div className="faq-icon">
          <span>{icon || '❓'}</span>
        </div>
      ),
    },
    {
      title: "标题",
      dataIndex: "title",
      key: "title",
      ellipsis: true,
    },
    {
      title: "内容",
      dataIndex: "content",
      key: "content",
      ellipsis: true,
      render: (content: string) => (
        <Tooltip title={content}>
          <span>{content?.length > 50 ? `${content.substring(0, 50)}...` : content}</span>
        </Tooltip>
      ),
    },
    {
      title: "分类",
      dataIndex: "category",
      key: "category",
      width: 100,
      render: (category: string) => {
        const categoryInfo = CATEGORY_OPTIONS.find(c => c.value === category);
        const color = CATEGORY_COLORS[category as keyof typeof CATEGORY_COLORS] || 'default';
        return (
          <Tag color={color}>
            {categoryInfo?.label || category}
          </Tag>
        );
      },
    },
    {
      title: "排序",
      dataIndex: "order",
      key: "order",
      width: 80,
      sorter: (a: FAQSuggestion, b: FAQSuggestion) => a.order - b.order,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string, record: FAQSuggestion) => {
        const statusInfo = STATUS_OPTIONS.find(s => s.value === status);
        const color = STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'default';
        return (
          <Select
            value={status}
            size="small"
            style={{ width: 80 }}
            onChange={(newStatus) => handleStatusChange(record.id, newStatus as 'active' | 'inactive' | 'draft')}
            disabled={saving}
          >
            {STATUS_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                <Tag color={STATUS_COLORS[option.value as keyof typeof STATUS_COLORS]}>
                  {option.label}
                </Tag>
              </Option>
            ))}
          </Select>
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 150,
      render: (_: any, record: FAQSuggestion) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={saving}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个FAQ建议吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={deleting}
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                loading={deleting}
                disabled={deleting}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys as string[]),
  };

  // 页面动画配置
  const pageTransition = usePageTransition({
    type: AnimationType.FADE_IN_UP,
    duration: 0.6,
    delay: 0,
  });

  // 提取有效的 motion props，避免将控制方法传递给 DOM 元素
  const {
    variants,
    transition,
    initial,
    animate,
    exit,
    style,
    className: transitionClassName,
  } = pageTransition;

  return (
    <motion.div
      variants={variants}
      transition={transition}
      initial={initial}
      animate={animate}
      exit={exit}
      style={style}
      className={`faq-management-settings ${transitionClassName || ''}`}
    >
        {/* 错误提示 */}
        {error && (
          <Alert
            message="错误"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError(null)}
            style={{ marginBottom: 16 }}
          />
        )}

        <div className="actions-bar" style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
              disabled={saving}
            >
            添加FAQ建议
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => loadSuggestions()}
            loading={loading}
            disabled={saving}
          >
            刷新
          </Button>
          {selectedRowKeys.length > 0 && (
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchDelete}
              loading={deleting}
              disabled={deleting}
            >
              批量删除 ({selectedRowKeys.length})
            </Button>
          )}
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={suggestions}
        rowKey="id"
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          onChange: (page, pageSize) => {
            loadSuggestions({ page, pageSize });
          },
        }}
      />

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingItem ? "编辑FAQ建议" : "添加FAQ建议"}
        open={modalVisible}
        onOk={handleSave}
        onCancel={handleCancel}
        width={600}
        okText="保存"
        cancelText="取消"
        confirmLoading={saving}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active',
            order: 0,
            category: 'general',
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="标题"
                name="title"
                rules={[{ required: true, message: "请输入标题" }]}
              >
                <Input placeholder="请输入FAQ标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="图标"
                name="icon"
              >
                <Select placeholder="选择图标" allowClear>
                  {ICON_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      <Space>
                        <span>{option.value}</span>
                        {option.label}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="内容"
            name="content"
            rules={[{ required: true, message: "请输入FAQ内容" }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入FAQ内容"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="分类"
                name="category"
                rules={[{ required: true, message: "请选择分类" }]}
              >
                <Select placeholder="选择分类">
                  {CATEGORY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="排序"
                name="order"
                rules={[{ required: true, message: "请输入排序值" }]}
              >
                <InputNumber
                  min={0}
                  placeholder="排序值"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="状态"
                name="status"
                rules={[{ required: true, message: "请选择状态" }]}
              >
                <Select placeholder="选择状态">
                  {STATUS_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      <Tag color={STATUS_COLORS[option.value as keyof typeof STATUS_COLORS]}>
                        {option.label}
                      </Tag>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="标签"
            name="tags"
            help="多个标签用逗号分隔"
          >
            <Input placeholder="请输入标签，多个标签用逗号分隔" />
          </Form.Item>
        </Form>
      </Modal>
    </motion.div>
  );
};

export default FAQManagementComponent;
