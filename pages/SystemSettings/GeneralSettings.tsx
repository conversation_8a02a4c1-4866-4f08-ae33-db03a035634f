import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Switch, Button, message, Space, Divider, InputNumber, Select } from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;

interface GeneralSettingsData {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  adminEmail: string;
  enableRegistration: boolean;
  enableEmailVerification: boolean;
  maxFileSize: number;
  allowedFileTypes: string[];
  sessionTimeout: number;
  defaultLanguage: string;
  timezone: string;
  enableMaintenance: boolean;
  maintenanceMessage: string;
}

const GeneralSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // 模拟初始数据
  const mockSettings: GeneralSettingsData = {
    siteName: 'V2 Admin',
    siteDescription: '基于 React + Ant Design 的现代化管理系统',
    siteUrl: 'https://admin.example.com',
    adminEmail: '<EMAIL>',
    enableRegistration: true,
    enableEmailVerification: false,
    maxFileSize: 10,
    allowedFileTypes: ['jpg', 'png', 'pdf', 'doc', 'docx'],
    sessionTimeout: 30,
    defaultLanguage: 'zh-CN',
    timezone: 'Asia/Shanghai',
    enableMaintenance: false,
    maintenanceMessage: '系统正在维护中，请稍后再试。'
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      form.setFieldsValue(mockSettings);
    } catch (error) {
      message.error('加载设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values: GeneralSettingsData) => {
    setSaveLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存设置失败');
    } finally {
      setSaveLoading(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(mockSettings);
    message.info('设置已重置');
  };

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="基础设置"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              重置
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              loading={saveLoading}
              onClick={() => form.submit()}
            >
              保存设置
            </Button>
          </Space>
        }
        loading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          style={{ maxWidth: 800 }}
        >
          <Divider orientation="left">站点信息</Divider>
          
          <Form.Item
            name="siteName"
            label="站点名称"
            rules={[{ required: true, message: '请输入站点名称' }]}
          >
            <Input placeholder="请输入站点名称" />
          </Form.Item>

          <Form.Item
            name="siteDescription"
            label="站点描述"
            rules={[{ required: true, message: '请输入站点描述' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入站点描述"
            />
          </Form.Item>

          <Form.Item
            name="siteUrl"
            label="站点URL"
            rules={[
              { required: true, message: '请输入站点URL' },
              { type: 'url', message: '请输入有效的URL' }
            ]}
          >
            <Input placeholder="https://example.com" />
          </Form.Item>

          <Form.Item
            name="adminEmail"
            label="管理员邮箱"
            rules={[
              { required: true, message: '请输入管理员邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>

          <Divider orientation="left">用户设置</Divider>

          <Form.Item
            name="enableRegistration"
            label="允许用户注册"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enableEmailVerification"
            label="启用邮箱验证"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="sessionTimeout"
            label="会话超时时间（分钟）"
            rules={[{ required: true, message: '请输入会话超时时间' }]}
          >
            <InputNumber
              min={5}
              max={1440}
              style={{ width: '100%' }}
              placeholder="30"
            />
          </Form.Item>

          <Divider orientation="left">文件设置</Divider>

          <Form.Item
            name="maxFileSize"
            label="最大文件大小（MB）"
            rules={[{ required: true, message: '请输入最大文件大小' }]}
          >
            <InputNumber
              min={1}
              max={100}
              style={{ width: '100%' }}
              placeholder="10"
            />
          </Form.Item>

          <Form.Item
            name="allowedFileTypes"
            label="允许的文件类型"
            rules={[{ required: true, message: '请选择允许的文件类型' }]}
          >
            <Select
              mode="tags"
              style={{ width: '100%' }}
              placeholder="请选择或输入文件类型"
              options={[
                { value: 'jpg', label: 'JPG' },
                { value: 'png', label: 'PNG' },
                { value: 'gif', label: 'GIF' },
                { value: 'pdf', label: 'PDF' },
                { value: 'doc', label: 'DOC' },
                { value: 'docx', label: 'DOCX' },
                { value: 'xls', label: 'XLS' },
                { value: 'xlsx', label: 'XLSX' },
                { value: 'txt', label: 'TXT' },
                { value: 'zip', label: 'ZIP' }
              ]}
            />
          </Form.Item>

          <Divider orientation="left">系统设置</Divider>

          <Form.Item
            name="defaultLanguage"
            label="默认语言"
            rules={[{ required: true, message: '请选择默认语言' }]}
          >
            <Select placeholder="请选择默认语言">
              <Option value="zh-CN">简体中文</Option>
              <Option value="zh-TW">繁体中文</Option>
              <Option value="en-US">English</Option>
              <Option value="ja-JP">日本語</Option>
              <Option value="ko-KR">한국어</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="timezone"
            label="时区"
            rules={[{ required: true, message: '请选择时区' }]}
          >
            <Select placeholder="请选择时区">
              <Option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</Option>
              <Option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</Option>
              <Option value="America/New_York">America/New_York (UTC-5)</Option>
              <Option value="Europe/London">Europe/London (UTC+0)</Option>
              <Option value="UTC">UTC (UTC+0)</Option>
            </Select>
          </Form.Item>

          <Divider orientation="left">维护模式</Divider>

          <Form.Item
            name="enableMaintenance"
            label="启用维护模式"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="maintenanceMessage"
            label="维护提示信息"
          >
            <TextArea
              rows={3}
              placeholder="请输入维护提示信息"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={saveLoading}
              >
                保存设置
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default GeneralSettings;
