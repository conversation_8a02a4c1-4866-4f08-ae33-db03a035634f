// V2 Admin 系统设置页面样式 - 适配v2-admin设计系统
@use '../../styles/variables' as *;

.v2-system-settings-page {
  // 使用v2-admin的内容区域样式
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: var(--theme-bg-primary);
    transition: background-color 0.2s ease;
    border-radius: 6px;
    border: 1px solid $border-color-split;
    box-shadow: none !important;

    .header-content {
      .page-title {
        margin-bottom: 8px !important;
        color: $text-color;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        .anticon {
          color: $primary-color;
        }
      }

      .page-description {
        color: $text-color-secondary;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        height: 40px;
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  // Remove shadows from all components within settings pages (matching system status page)
  .ant-card,
  .ant-table,
  .ant-btn,
  .ant-input,
  .ant-select,
  .ant-dropdown,
  .ant-tooltip,
  .ant-popover,
  .ant-form-item,
  .ant-switch,
  .ant-input-number {
    box-shadow: none !important;

    &:hover,
    &:focus,
    &:active,
    &.ant-btn-primary:hover,
    &.ant-btn-primary:focus,
    &.ant-btn-primary:active {
      box-shadow: none !important;
    }
  }

  // 注意：现在统一使用 Ant Design 的 styles API 进行样式定制
  // 如需定制 Card 样式，请使用 src/constants/antdStyles.ts 中的配置常量
  // 保留 settings-card 类仅用于向后兼容，新组件请使用 API 方式
  .settings-card {
    border-radius: 6px;
    box-shadow: $box-shadow-base;
    border: none;
  }

  .settings-tabs {
    .ant-tabs-nav {
      padding: 0 24px;
      margin-bottom: 0;
      background: $background-color-light;
      border-bottom: 1px solid $border-color-base;

      .ant-tabs-tab {
        padding: 16px 24px;
        font-weight: 500;
        color: $text-color-secondary;

        &.ant-tabs-tab-active {
          color: $primary-color;
          background: var(--theme-bg-primary);
          transition: background-color 0.2s ease;
          border-bottom: 2px solid $primary-color;
        }

        &:hover {
          color: $primary-color;
        }
      }
    }

    .ant-tabs-content-holder {
      padding: 32px 24px;
    }

    .ant-tabs-tabpane {
      .ant-form-item {
        margin-bottom: 24px;

        .ant-form-item-label {
          padding-bottom: 8px;

          label {
            font-weight: 500;
            color: $text-color;
            font-size: 14px;
          }
        }

        .ant-input,
        .ant-input-number,
        .ant-select-selector {
          border-radius: 6px;
          border: 1px solid $border-color-base;
          transition: all 0.2s;

          &:hover {
            border-color: $primary-color;
          }

          &:focus,
          &.ant-input-focused,
          &.ant-input-number-focused,
          &.ant-select-focused .ant-select-selector {
            border-color: $primary-color;
            box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
          }
        }

        .ant-switch {
          &.ant-switch-checked {
            background-color: $primary-color;
          }
        }
      }

      .setting-description {
        display: block;
        margin-top: 4px;
        font-size: 12px;
        line-height: 1.4;
        color: $text-color-tertiary;
      }

      .ant-divider {
        margin: 32px 0;
        border-color: $border-color-base;
      }

      .ant-alert {
        border-radius: 6px;
        border: 1px solid $primary-color-light;
        background: $primary-color-light;

        .ant-alert-icon {
          color: $info-color;
        }

        .ant-alert-message {
          color: $text-color;
          font-weight: 500;
        }

        .ant-alert-description {
          color: $text-color-secondary;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  // FAQ管理设置样式
  .faq-management-settings {
    .actions-bar {
      .ant-btn {
        border-radius: 6px;
        font-weight: 500;
      }
    }

    .ant-tabs-card {
      .ant-tabs-tab {
        border-radius: 6px 6px 0 0;
        font-weight: 500;
        color: $text-color-secondary;

        &.ant-tabs-tab-active {
          color: $primary-color;
          background: var(--theme-bg-primary);
          border-bottom-color: var(--theme-bg-primary);
          transition: background-color 0.2s ease, border-color 0.2s ease;
        }

        &:hover {
          color: $primary-color;
        }
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: $background-color-light;
        font-weight: 600;
        border-bottom: 1px solid $border-color-base;
      }

      .ant-table-tbody > tr:hover > td {
        background: $primary-color-light;
      }
    }

    .faq-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 6px;
      background: $primary-color-light;
      color: $primary-color;
      font-size: 16px;
    }

    .ant-modal {
      .ant-form-item-label > label {
        font-weight: 500;
        color: $text-color;
      }

      .ant-input,
      .ant-input-number,
      .ant-select-selector {
        border-radius: 6px;
        border: 1px solid $border-color-base;

        &:hover {
          border-color: $primary-color;
        }

        &:focus,
        &.ant-input-focused,
        &.ant-select-focused .ant-select-selector {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
        }
      }

      .ant-switch {
        &.ant-switch-checked {
          background-color: $primary-color;
        }
      }
    }

    .ant-tag {
      border-radius: 4px;
      font-size: 12px;
      padding: 2px 8px;
    }
  }

  // 响应式设计
  @media (max-width: $breakpoint-md) {
    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .settings-tabs {
      .ant-tabs-nav {
        padding: 0 16px;
      }

      .ant-tabs-content-holder {
        padding: 24px 16px;
      }

      .ant-tabs-tabpane {
        .ant-row {
          .ant-col {
            margin-bottom: 16px;
          }
        }
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .settings-tabs {
      .ant-tabs-tabpane {
        .ant-row {
          .ant-col {
            width: 100% !important;
            flex: none !important;
          }
        }
      }
    }
  }
}

// ===== 统一的设置页面样式 =====
// 基础设置页面
.general-settings {
  display: flex;
  flex-direction: column;
  padding: 0;
  min-height: 100%;

  // 响应式Card间距类 - 匹配系统状态页面的间距
  .responsive-card-spacing {
    // 默认电脑端：16px间距，匹配系统状态页面
    margin-bottom: 16px;

    // 平板端：保持16px间距以保持一致性
    @media (max-width: $breakpoint-md) {
      margin-bottom: 16px;
    }

    // 手机端：减少到12px以适应小屏幕
    @media (max-width: $breakpoint-sm) {
      margin-bottom: 12px;
    }
  }

  // 设置描述文字样式
  .setting-description {
    color: $text-color-tertiary;
    font-size: $font-size-sm;
    margin-top: 4px;
  }
}

// 标题生成设置页面
.title-generation-settings {
  display: flex;
  flex-direction: column;
  padding: 0;
  min-height: 100%;

  // 响应式Card间距类 - 匹配系统状态页面的间距
  .responsive-card-spacing {
    // 默认电脑端：16px间距，匹配系统状态页面
    margin-bottom: 16px;

    // 平板端：保持16px间距以保持一致性
    @media (max-width: $breakpoint-md) {
      margin-bottom: 16px;
    }

    // 手机端：减少到12px以适应小屏幕
    @media (max-width: $breakpoint-sm) {
      margin-bottom: 12px;
    }
  }

  // 设置描述文字样式
  .setting-description {
    color: $text-color-tertiary;
    font-size: $font-size-sm;
    margin-top: 4px;
  }
}

// 验证码设置页面
.captcha-settings {
  display: flex;
  flex-direction: column;
  padding: 0;
  min-height: 100%;

  // 响应式Card间距类 - 匹配系统状态页面的间距
  .responsive-card-spacing {
    // 默认电脑端：16px间距，匹配系统状态页面
    margin-bottom: 16px;

    // 平板端：保持16px间距以保持一致性
    @media (max-width: $breakpoint-md) {
      margin-bottom: 16px;
    }

    // 手机端：减少到12px以适应小屏幕
    @media (max-width: $breakpoint-sm) {
      margin-bottom: 12px;
    }
  }

  // 设置描述文字样式
  .setting-description {
    color: $text-color-tertiary;
    font-size: $font-size-sm;
    margin-top: 4px;
  }
}

// FAQ管理页面
.faq-management-settings {
  display: flex;
  flex-direction: column;
  padding: 0;
  min-height: 100%;

  // 响应式Card间距类 - 匹配系统状态页面的间距
  .responsive-card-spacing {
    // 默认电脑端：16px间距，匹配系统状态页面
    margin-bottom: 16px;

    // 平板端：保持16px间距以保持一致性
    @media (max-width: $breakpoint-md) {
      margin-bottom: 16px;
    }

    // 手机端：减少到12px以适应小屏幕
    @media (max-width: $breakpoint-sm) {
      margin-bottom: 12px;
    }
  }

  // 设置描述文字样式
  .setting-description {
    color: $text-color-tertiary;
    font-size: $font-size-sm;
    margin-top: 4px;
  }
}

// 文件中间件设置页面
.file-middleware-settings {
  // 响应式Card间距类
  .responsive-card-spacing {
    // 默认电脑端（>1024px）：margin-bottom: 16px
    margin-bottom: $spacing-md;

    // 平板端（<=1024px且>=768px）：margin-bottom: 8px
    @media (max-width: $breakpoint-md) {
      margin-bottom: $spacing-md;
    }

    // 手机端（<768px）：margin-bottom: 4px
    @media (max-width: $breakpoint-sm) {
      margin-bottom: $spacing-md;
    }
  }

  // 设置描述文字样式
  .setting-description {
    color: $text-color-tertiary;
    font-size: $font-size-sm;
    margin-top: 4px;
  }
}
