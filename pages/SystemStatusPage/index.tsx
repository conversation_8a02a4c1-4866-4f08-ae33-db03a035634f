import React from "react";
import { Card, Row, Col, Table } from "antd";
import {
  CARD_SYSTEM_STYLES,
  TABLE_SMALL_STYLES,
  NO_SHADOW_STYLES
} from "../../src/constants/antdStyles";
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import {
  DesktopOutlined,
  DatabaseOutlined,
  HddOutlined,
  InfoCircleOutlined,
  AppstoreOutlined,
} from "@ant-design/icons";
import "./style.scss";

// 模拟数据
const cpuData = [
  { key: "cores", label: "核心数", value: 2 },
  { key: "user", label: "用户使用率", value: "0.8%" },
  { key: "sys", label: "系统使用率", value: "5.49%" },
  { key: "idle", label: "当前空闲率", value: "93.71%" },
];

const memoryData = [
  { key: "total", label: "总内存", value: "1.92G" },
  { key: "used", label: "已用内存", value: "1.55G", highlight: true },
  { key: "free", label: "剩余内存", value: "0.37G" },
  { key: "usage", label: "使用率", value: "80.58%", highlight: true },
];

const serverData = [
  { key: "name", label: "服务器名称", value: "yangzz" },
  { key: "ip", label: "服务器IP", value: "**************" },
  { key: "os", label: "操作系统", value: "Windows Server 2012 R2" },
  { key: "arch", label: "系统架构", value: "amd64" },
];

const javaData = [
  { key: "name", label: "Java名称", value: "Java HotSpot(TM) 64-Bit Server VM" },
  { key: "version", label: "Java版本", value: "1.8.0_161" },
  { key: "start", label: "启动时间", value: "2025-05-14 16:05:51" },
  { key: "uptime", label: "运行时长", value: "48天22小时30分钟" },
  { key: "install", label: "安装路径", value: "C:\\Program Files\\Java\\jre1.8.0_161" },
  { key: "project", label: "项目路径", value: "C:\\ruoyi" },
  { key: "args", label: "运行参数", value: "[-Dname=ruoyi.jar ... -XX:+UseParallelGC]" },
];

const diskColumns = [
  { title: "盘符路径", dataIndex: "path", key: "path" },
  { title: "文件系统", dataIndex: "fs", key: "fs" },
  { title: "盘符类型", dataIndex: "type", key: "type" },
  { title: "总大小", dataIndex: "total", key: "total" },
  { title: "可用大小", dataIndex: "free", key: "free" },
  { title: "已用大小", dataIndex: "used", key: "used" },
  { title: "已用百分比", dataIndex: "percent", key: "percent" },
];
const diskData = [
  {
    key: "C",
    path: "C:\\",
    fs: "NTFS",
    type: "本地磁盘(固定盘)",
    total: "39.7 GB",
    free: "21.2 GB",
    used: "18.4 GB",
    percent: "46.42%",
  },
];

const renderTable = (data: any[], _highlightKey?: string) => (
  <table className="sys-table">
    <tbody>
      {data.map((item) => (
        <tr key={item.key}>
          <td className="sys-table-label">{item.label}</td>
          <td className={item.highlight ? "sys-table-value highlight" : "sys-table-value"}>{item.value}</td>
        </tr>
      ))}
    </tbody>
  </table>
);

const SystemStatusPage: React.FC = () => {
  // 页面动画配置
  const pageTransition = usePageTransition({
    type: AnimationType.FADE_IN_UP,
    duration: 0.6,
    delay: 0,
  });

  return (
    <motion.div
      {...pageTransition}
      className="system-status-page"
    >
        <Row gutter={[{ xs: 4, sm: 8, md: 16 }, { xs: 4, sm: 8, md: 16 }]}>
          {/* CPU/内存卡片 */}
          <Col xs={24} md={12}>
            <Card
              title={<><DatabaseOutlined /> CPU</>}
              size="small"
              extra={<InfoCircleOutlined />}
              className="sys-card"
              styles={CARD_SYSTEM_STYLES}
            >
              {renderTable(cpuData)}
            </Card>
          </Col>
          <Col xs={24} md={12}>
            <Card
              title={<><DesktopOutlined /> 内存</>}
              size="small"
              extra={<InfoCircleOutlined />}
              className="sys-card"
              styles={CARD_SYSTEM_STYLES}
            >
              {renderTable(memoryData)}
            </Card>
          </Col>

          {/* 服务器信息 */}
          <Col xs={24}>
            <Card
              title={<><AppstoreOutlined /> 服务器信息</>}
              size="small"
              extra={<InfoCircleOutlined />}
              className="sys-card"
              styles={CARD_SYSTEM_STYLES}
            >
              {renderTable(serverData)}
            </Card>
          </Col>

          {/* Java虚拟机信息 */}
          <Col xs={24}>
            <Card
              title={<><DatabaseOutlined /> Java虚拟机信息</>}
              size="small"
              extra={<InfoCircleOutlined />}
              className="sys-card"
              styles={CARD_SYSTEM_STYLES}
            >
              {renderTable(javaData)}
            </Card>
          </Col>

          {/* 磁盘状态 */}
          <Col xs={24}>
            <Card
              title={<><HddOutlined /> 磁盘状态</>}
              size="small"
              extra={<InfoCircleOutlined />}
              className="sys-card"
              styles={CARD_SYSTEM_STYLES}
            >
              <Table
                columns={diskColumns}
                dataSource={diskData}
                size="small"
                pagination={false}
                rowKey="key"
                styles={TABLE_SMALL_STYLES}
              />
            </Card>
          </Col>
        </Row>
    </motion.div>
  );
};

export default SystemStatusPage; 