import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Table, Tag, Space, Button } from 'antd';
import { 
  ServerOutlined, 
  DatabaseOutlined, 
  CloudOutlined, 
  MemoryOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
  };
  memory: {
    used: number;
    total: number;
    usage: number;
  };
  disk: {
    used: number;
    total: number;
    usage: number;
  };
  network: {
    inbound: number;
    outbound: number;
  };
}

interface ServiceStatus {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'error';
  uptime: string;
  port: number;
  lastCheck: string;
}

const SystemStatusPage: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟系统指标数据
  const mockMetrics: SystemMetrics = {
    cpu: {
      usage: 45.2,
      cores: 8
    },
    memory: {
      used: 6.4,
      total: 16,
      usage: 40
    },
    disk: {
      used: 120,
      total: 500,
      usage: 24
    },
    network: {
      inbound: 1.2,
      outbound: 0.8
    }
  };

  // 模拟服务状态数据
  const mockServices: ServiceStatus[] = [
    {
      id: '1',
      name: 'Web Server',
      status: 'running',
      uptime: '15天 3小时',
      port: 3000,
      lastCheck: '2024-01-15 10:30:00'
    },
    {
      id: '2',
      name: 'Database',
      status: 'running',
      uptime: '30天 12小时',
      port: 5432,
      lastCheck: '2024-01-15 10:30:00'
    },
    {
      id: '3',
      name: 'Redis Cache',
      status: 'running',
      uptime: '7天 8小时',
      port: 6379,
      lastCheck: '2024-01-15 10:30:00'
    },
    {
      id: '4',
      name: 'Message Queue',
      status: 'error',
      uptime: '-',
      port: 5672,
      lastCheck: '2024-01-15 10:25:00'
    }
  ];

  useEffect(() => {
    loadSystemStatus();
    // 设置定时刷新
    const interval = setInterval(loadSystemStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadSystemStatus = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMetrics(mockMetrics);
      setServices(mockServices);
    } catch (error) {
      console.error('加载系统状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'stopped':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'success';
      case 'stopped': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中';
      case 'stopped': return '已停止';
      case 'error': return '错误';
      default: return status;
    }
  };

  const getProgressColor = (usage: number) => {
    if (usage < 50) return '#52c41a';
    if (usage < 80) return '#faad14';
    return '#ff4d4f';
  };

  const columns: ColumnsType<ServiceStatus> = [
    {
      title: '服务名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {getStatusIcon(record.status)}
          {text}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '运行时间',
      dataIndex: 'uptime',
      key: 'uptime',
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheck',
      key: 'lastCheck',
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card
            title="系统状态监控"
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={loadSystemStatus}
                loading={loading}
              >
                刷新
              </Button>
            }
          >
            <Row gutter={[16, 16]}>
              {/* CPU 使用率 */}
              <Col xs={24} sm={12} lg={6}>
                <Card size="small">
                  <Statistic
                    title="CPU 使用率"
                    value={metrics?.cpu.usage || 0}
                    precision={1}
                    suffix="%"
                    prefix={<ServerOutlined />}
                  />
                  <Progress
                    percent={metrics?.cpu.usage || 0}
                    strokeColor={getProgressColor(metrics?.cpu.usage || 0)}
                    showInfo={false}
                    size="small"
                  />
                  <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                    {metrics?.cpu.cores || 0} 核心
                  </div>
                </Card>
              </Col>

              {/* 内存使用率 */}
              <Col xs={24} sm={12} lg={6}>
                <Card size="small">
                  <Statistic
                    title="内存使用率"
                    value={metrics?.memory.usage || 0}
                    precision={1}
                    suffix="%"
                    prefix={<MemoryOutlined />}
                  />
                  <Progress
                    percent={metrics?.memory.usage || 0}
                    strokeColor={getProgressColor(metrics?.memory.usage || 0)}
                    showInfo={false}
                    size="small"
                  />
                  <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                    {metrics?.memory.used || 0}GB / {metrics?.memory.total || 0}GB
                  </div>
                </Card>
              </Col>

              {/* 磁盘使用率 */}
              <Col xs={24} sm={12} lg={6}>
                <Card size="small">
                  <Statistic
                    title="磁盘使用率"
                    value={metrics?.disk.usage || 0}
                    precision={1}
                    suffix="%"
                    prefix={<DatabaseOutlined />}
                  />
                  <Progress
                    percent={metrics?.disk.usage || 0}
                    strokeColor={getProgressColor(metrics?.disk.usage || 0)}
                    showInfo={false}
                    size="small"
                  />
                  <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                    {metrics?.disk.used || 0}GB / {metrics?.disk.total || 0}GB
                  </div>
                </Card>
              </Col>

              {/* 网络流量 */}
              <Col xs={24} sm={12} lg={6}>
                <Card size="small">
                  <Statistic
                    title="网络流量"
                    value={metrics?.network.inbound || 0}
                    precision={1}
                    suffix="MB/s"
                    prefix={<CloudOutlined />}
                  />
                  <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                    入站: {metrics?.network.inbound || 0}MB/s
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    出站: {metrics?.network.outbound || 0}MB/s
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={24}>
          <Card title="服务状态">
            <Table
              columns={columns}
              dataSource={services}
              rowKey="id"
              loading={loading}
              pagination={false}
              size="middle"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemStatusPage;
