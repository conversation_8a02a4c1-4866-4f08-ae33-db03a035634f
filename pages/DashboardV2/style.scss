@use '../../styles/variables' as *;

// Dashboard 新版样式
.dashboard-v2 {
  // 使用flex布局进行页面结构
  display: flex;
  flex-direction: column;
  gap: $spacing-md; // 统一间距：16px
  padding: 0;
  background: transparent;
  min-height: 100%;

  // 页面sections共同样式
  .stats-section,
  .charts-section,
  .bottom-section {
    width: 100%;
  }

  // ===== 顶部统计卡片区域 - 响应式Grid布局 =====
  .stats-section {
    width: 100%;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); // 增加最小宽度
      gap: $spacing-md; // 使用统一的 16px 间距标准
      width: 100%;

      // 平板端优化
      @media (max-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-md; // 统一使用 16px 间距标准
      }

      // 手机端优化
      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px; // 手机端使用标准间距

        // 超小屏幕时单列显示
        @media (max-width: 480px) {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }

      // 大屏幕优化
      @media (min-width: 1200px) {
        grid-template-columns: repeat(4, 1fr);
        gap: $spacing-md; // 统一使用 16px 间距标准
      }
    }
    // 现代化统计卡片样式
    .modern-stats-card {
      position: relative;
      height: 130px; // 调整为 130px，平衡紧凑性和舒适性
      border-radius: 12px;
      overflow: hidden;
      background: var(--theme-bg-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid var(--theme-border-color-split);
      margin-bottom: 0; // 移除margin，使用Grid的gap控制间距

      // 移动端高度优化
      @media (max-width: 768px) {
        height: 120px; // 平板端按比例调整
      }

      @media (max-width: 480px) {
        height: 110px; // 手机端按比例调整
      }

      &:hover {
        transform: translateY(-2px); // 减少位移防止重叠
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: transparent;
        z-index: 10; // 确保悬浮时在最上层
      }

      .ant-card-body {
         padding: $spacing-md; // 使用统一的 16px 间距标准
         height: 100%;
         position: relative;
         display: flex;
         flex-direction: column;
         justify-content: space-between;
         overflow: hidden;

         @media (max-width: 768px) {
           padding: $spacing-md;
         }

         @media (max-width: 480px) {
           padding: $spacing-md;
         }

         // 创建明确的内容区域分配
         > * {
           flex-shrink: 0; // 防止内容被压缩
         }

         // 主内容区域（图标+标题+数值）
         .main-content {
           flex: 1;
           display: flex;
           flex-direction: column;
           min-height: 0; // 允许内容收缩
         }

         // 顶部区域：图标和标题横向布局
         .top-section {
           display: flex;
           align-items: flex-start;
           gap: $spacing-md; // 使用统一的 16px 间距
           margin-bottom: 0; // 移除底部边距，避免多余间距
           flex-shrink: 0;
         }
       }

      // 渐变背景装饰
      .card-gradient {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 0;
        border-radius: inherit; // 继承父元素的圆角
      }

      // 图标区域 - 适配 130px 高度
        .icon-section {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 38px; // 适当增加图标尺寸
          height: 38px;
          border-radius: 9px;
          font-size: 19px; // 适当增加图标字体
          position: relative;
          z-index: 1;
          flex-shrink: 0;
        }

      // 标题区域 - 横向布局优化
      .title-section {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 8px;
        position: relative;
        z-index: 1;
        flex: 1; // 占据剩余空间
        min-width: 0; // 允许收缩

        .card-title {
          margin: 0;
          font-size: 14px;
          font-weight: 500;
          color: var(--theme-text-secondary);
          line-height: 1.4;
          flex: 1; // 标题占据剩余空间
          min-width: 0; // 允许收缩
        }

        .trend-badge {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          
          &.trend-up {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #d9f7be;
          }
          
          &.trend-down {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
          }

          .anticon {
            font-size: 10px;
          }
        }
      }

      // 数值和统计信息区域 - 使用统一间距
      .value-section {
        display: flex;
        align-items: flex-start;
        gap: $spacing-md; // 使用统一的 16px 间距
        margin: 0; // 移除上下边距，由 justify-content: space-between 处理
        position: relative;
        z-index: 1;
        flex: 1;

        // 移动端保持一致
        @media (max-width: 480px) {
          gap: $spacing-md;
          margin: 0;
        }

        // 主要数值
        .main-value {
          font-size: 30px; // 适当增加字体大小，配合 130px 高度
          font-weight: 700;
          line-height: 1.2; // 稍微放松行高
          flex-shrink: 0;

          @media (max-width: 768px) {
            font-size: 26px;
          }

          @media (max-width: 480px) {
            font-size: 22px;
          }
        }

        // 右侧统计信息
        .stats-info {
          display: flex;
          flex-direction: column;
          gap: 4px; // 使用固定的小间距
          margin-top: 0; // 移除顶部边距
          flex: 1;
          min-width: 0;
          justify-content: center;

          @media (max-width: 480px) {
            margin-top: 0;
            gap: 4px; // 保持一致
          }

          .trend-info {
            display: flex;
            align-items: center;
            gap: 4px;
            line-height: 1.4; // 稍微放松行高

            .trend-label {
              font-size: 12px;
              color: var(--theme-text-secondary);
              white-space: nowrap;

              @media (max-width: 480px) {
                font-size: 11px;
              }
            }

            .trend-value {
              font-size: 12px;
              font-weight: 600;
              white-space: nowrap;

              @media (max-width: 480px) {
                font-size: 11px;
              }
            }
          }

          .sub-value {
            font-size: 12px;
            color: var(--theme-text-tertiary);
            line-height: 1.4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            @media (max-width: 480px) {
              font-size: 11px;
            }
          }
        }
      }

       // 进度条 - 使用统一间距，避免遮挡
       .progress-bar {
         margin: 0; // 移除边距，由容器的 justify-content: space-between 处理
         position: relative;
         z-index: 1;
         flex-shrink: 0;

         .ant-progress-bg {
           border-radius: 3px;
         }

         // 进度条高度调整
         .ant-progress-inner {
           height: 4px !important; // 保持适中的高度
         }
       }



      // 不同卡片的特殊样式
      &.stats-card-0 {
        .icon-section {
          background: linear-gradient(135deg, #1890ff15 0%, #1890ff08 100%);
        }
      }

      &.stats-card-1 {
        .icon-section {
          background: linear-gradient(135deg, #52c41a15 0%, #52c41a08 100%);
        }
      }

      &.stats-card-2 {
        .icon-section {
          background: linear-gradient(135deg, #faad1415 0%, #faad1408 100%);
        }
      }

      &.stats-card-3 {
        .icon-section {
          background: linear-gradient(135deg, #722ed115 0%, #722ed108 100%);
        }
      }
    }
  }

  // ===== 中间图表区域 - 响应式Grid布局 =====
  .charts-section {
    .charts-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: $spacing-md; // 电脑端：16px 间距
      width: 100%;

      // 平板端及以下布局优化
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
    }

    .main-chart-card,
    .ranking-card {
      border: 1px solid var(--theme-border-color-split);
      border-radius: 8px;
      background: var(--theme-bg-primary);
      transition: background-color 0.2s ease, border-color 0.2s ease;

      // 动态高度，确保内容完整显示
      min-height: 400px;
      height: auto;

      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        transform: translateY(-1px);
        border-color: #e6f7ff;
      }

      // Card body 样式现在通过 styles API 配置
      // 保留布局相关的样式
      .ant-card-body {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        box-sizing: border-box;
      }
    }

    .main-chart-card {
      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0; // 移除底部边距，让 Divider 控制间距
        flex-wrap: wrap;
        gap: $spacing-md;

        .chart-title {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          font-size: $font-size-lg;
          font-weight: 600;
          color: var(--theme-text-primary);
          transition: color 0.2s ease;

          .anticon {
            color: $primary-color;
            font-size: $font-size-xl;
          }
        }

        .chart-controls {
          display: flex;
          align-items: center;
          gap: $spacing-md; // 电脑端：16px 间距
          flex-wrap: wrap;

          .chart-tabs {
            display: flex;
            gap: $spacing-md;
          }

          .time-filters {
            display: flex;
            gap: $spacing-md;
            flex-wrap: wrap;

            // Tag checkable 样式现在通过 style 属性配置
            // 保留主题相关的样式
            .ant-tag-checkable {
              &:not(.ant-tag-checkable-checked) {
                background: var(--theme-bg-secondary);
                border-color: var(--theme-border-color);
                color: var(--theme-text-secondary);

                &:hover {
                  background: $primary-color-light;
                  border-color: $primary-color;
                  color: $primary-color;
                }
              }
            }
          }
        }
      }

      .chart-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .chart-placeholder {
          flex: 1;
          min-height: 300px;
          border-radius: $border-radius-base;
          background: var(--theme-bg-tertiary);
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid var(--theme-border-color-split);
          transition: background-color 0.2s ease, border-color 0.2s ease;

          .chart-mock {
            width: 100%;
            height: 100%;
            padding: $spacing-md; // 电脑端：16px 内边距
            display: flex;
            align-items: flex-end;
            justify-content: center;

            .chart-bars {
              display: flex;
              align-items: flex-end;
              gap: 12px;
              height: 85%;
              width: 90%;

              .bar {
                flex: 1;
                background: linear-gradient(to top, $primary-color, $primary-color-hover);
                border-radius: 4px 4px 0 0;
                min-height: 20px;
                transition: all 0.3s ease;

                &:nth-child(odd) {
                  background: linear-gradient(to top, $success-color, #73d13d);
                }

                &:hover {
                  opacity: 0.8;
                  transform: scale(1.05);
                }
              }
            }
          }
        }
      }
    }

    .ranking-card {
      .card-header {
        margin-bottom: 0;

        .card-title {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          font-size: $font-size-lg;
          font-weight: 600;
          color: var(--theme-text-primary);
          transition: color 0.2s ease;

          .anticon {
            color: $primary-color;
            font-size: $font-size-xl;
          }
        }
      }

      .ranking-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start; // 改为顶部对齐，更自然
        overflow: hidden; // 防止内容溢出
        max-height: calc(100% - 60px); // 确保不超出父容器，预留头部空间
        
        .ranking-item {
          display: flex;
          align-items: center;
          gap: $spacing-md; // 减少间距
          padding: $spacing-md $spacing-md; // 减少上下内边距
          border-radius: $border-radius-base; // 添加圆角
          transition: $transition-base;
          min-height: 48px; // 减少高度以适应容器

          &:hover {
            background: var(--theme-bg-secondary);
            border: 1px solid rgba(24, 144, 255, 0.1);
            transform: translateX(2px);
          }

          .rank-badge {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: $font-size-sm;
            font-weight: 600;
            color: white;
            flex-shrink: 0; // 防止压缩
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1); // 调低阴影 30%

            &.rank-1 {
              background: linear-gradient(135deg, #ffd700, #ffed4e);
              color: #d4af37;
            }

            &.rank-2 {
              background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
              color: #999;
            }

            &.rank-3 {
              background: linear-gradient(135deg, #cd7f32, #daa520);
              color: #8b4513;
            }

            &.rank-other {
              background: linear-gradient(135deg, $text-color-tertiary, #bfbfbf);
              color: white;
            }
          }

          .store-info {
            flex: 1;
            overflow: hidden; // 防止文字溢出
            min-width: 0; // 允许 flex 项目缩小

            .store-name {
              font-size: $font-size-sm;
              color: var(--theme-text-primary);
              font-weight: $font-weight-medium;
              margin-bottom: 4px;
              transition: color 0.2s ease;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis; // 超长文字显示省略号
            }

            .store-sales {
              font-size: $font-size-xs;
              color: var(--theme-text-secondary);
              font-weight: $font-weight-medium;
              white-space: nowrap;
              transition: color 0.2s ease;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  // ===== 底部数据区域 - 响应式Grid布局 =====
  .bottom-section {
    .bottom-grid {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: $spacing-md; // 电脑端：16px 间距
      width: 100%;

      // 平板端及以下布局优化
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
      }
    }

    .online-stores-card,
    .category-card {
      border: 1px solid var(--theme-border-color-split);
      border-radius: 8px;
      background: var(--theme-bg-primary);
      display: flex;
      flex-direction: column;
      transition: background-color 0.2s ease, border-color 0.2s ease;

      // 动态高度，确保内容完整显示
      min-height: 400px;
      height: auto;

      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        transform: translateY(-1px);
        border-color: #e6f7ff;
      }

      // Card body 样式现在通过 styles API 配置
      // 保留布局相关的样式
      .ant-card-body {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        box-sizing: border-box;
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0;
        flex-wrap: wrap;
        gap: $spacing-md;
        flex-shrink: 0; // 防止头部被压缩
        min-height: 40px; // 设置最小高度

        .card-title {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          font-size: $font-size-lg;
          font-weight: 600;
          color: var(--theme-text-primary);
          transition: color 0.2s ease;

          .anticon {
            color: $primary-color;
            font-size: $font-size-xl;
          }
        }

        .card-actions,
        .channel-tabs {
          display: flex;
          gap: $spacing-md;
          flex-wrap: wrap;
        }
      }
    }

    .online-stores-card {
      .stores-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden; // 防止内容溢出
        max-height: calc(100% - 60px); // 确保不超出父容器，预留头部空间
        
        .stores-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: $spacing-md; // 电脑端：16px 间距
          flex: 1;
          align-content: start;
          overflow: auto; // 允许滚动
          padding: $spacing-md; // 添加一些内边距



          .store-card {
            display: flex;
            align-items: center;
            gap: $spacing-md; // 减少间距
            padding: $spacing-md; // 减少内边距
            border: 1px solid var(--theme-border-color-split);
            border-radius: 8px;
            background: var(--theme-bg-tertiary);
            transition: all 0.3s ease;
            box-shadow: var(--theme-shadow-1);
            min-height: 70px; // 设置固定最小高度
            position: relative; // 为 z-index 做准备

            &:hover {
              border-color: #1890ff;
              box-shadow: var(--theme-shadow-2);
              transform: translateY(-1px); // 减少位移避免遮挡
              z-index: 2; // 确保悬浮时在上层
            }

            .store-avatar {
              min-width: 40px; // 使用min-width确保响应式
              width: 40px;
              height: 40px;
              border-radius: $border-radius-lg;
              background: linear-gradient(135deg, $primary-color-light, rgba(24, 144, 255, 0.2));
              display: flex;
              align-items: center;
              justify-content: center;
              color: #1890ff;
              font-size: 18px; // 减少字体大小
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06); // 调低阴影 30%
              flex-shrink: 0; // 防止压缩
            }

            .store-details {
              flex: 1;
              overflow: hidden; // 防止文字溢出
              min-width: 0; // 允许 flex 项目缩小

              .store-name {
                font-size: $font-size-base;
                color: var(--theme-text-primary);
                font-weight: $font-weight-medium;
                margin-bottom: 4px;
                transition: color 0.2s ease;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis; // 超长文字显示省略号
              }
            }
          }
        }
      }
    }

    .category-card {
      .category-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: visible; // 改为可见，确保图例显示
        max-height: calc(100% - 60px); // 确保不超出父容器，预留头部空间
        gap: $spacing-md; // 统一间距：16px
        
        .pie-chart-placeholder {
          height: 180px; // 减少高度给图例留更多空间
          border-radius: $border-radius-lg;
          background: var(--theme-bg-tertiary);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          border: 1px solid var(--theme-border-color-split);
          flex-shrink: 0; // 防止压缩
          z-index: 1; // 确保饼图在下层
          transition: background-color 0.2s ease, border-color 0.2s ease;

          .pie-mock {
            width: 140px; // 适当减少尺寸
            height: 140px; // 适当减少尺寸
            border-radius: 50%;
            background: conic-gradient(
              #1890ff 0deg 126deg,
              #52c41a 126deg 226.8deg,
              #faad14 226.8deg 306deg,
              #f5222d 306deg 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); // 进一步减轻阴影避免遮挡

            &::before {
              content: '';
              width: 80px; // 适当减少尺寸
              height: 80px; // 适当减少尺寸
              background: var(--theme-bg-primary);
              border-radius: 50%;
              position: absolute;
              box-shadow: var(--theme-shadow-1);
              transition: background-color 0.2s ease, box-shadow 0.2s ease;
            }

            .pie-center {
              position: relative;
              z-index: 1;
              text-align: center;

              .total-label {
                font-size: $font-size-xs;
                color: var(--theme-text-secondary);
                margin-bottom: 2px;
                font-weight: $font-weight-medium;
                transition: color 0.2s ease;
              }

              .total-value {
                font-size: $font-size-lg;
                color: var(--theme-text-primary);
                font-weight: 600;
                transition: color 0.2s ease;
              }
            }
          }
        }

        .category-legend {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-start; // 改为顶部对齐，避免与饼图重叠
          overflow: visible; // 改为可见，确保内容不被裁切
          padding: 0; // 移除内边距避免布局问题
          background: var(--theme-bg-primary); // 添加背景确保显示清晰
          border-radius: $border-radius-base;
          z-index: 2; // 确保图例在饼图上层
          transition: background-color 0.2s ease;

          .legend-item {
            display: flex;
            align-items: center;
            gap: $spacing-md; // 减少间距
            padding: $spacing-md $spacing-md; // 恢复一些内边距
            border-radius: $border-radius-base;
            transition: all 0.3s ease;
            min-height: 36px; // 减少高度，紧凑布局
            overflow: hidden; // 防止内容溢出
            background: transparent; // 透明背景

            &:hover {
              background: var(--theme-bg-secondary);
              transform: translateX(2px); // 轻微右移提供视觉反馈
              box-shadow: var(--theme-shadow-1);
            }

            .legend-color {
              width: 14px;
              height: 14px;
              border-radius: 3px;
              flex-shrink: 0; // 防止压缩
              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.14); // 调低阴影 30%
            }

            .legend-name {
              flex: 1;
              font-size: $font-size-sm;
              color: var(--theme-text-primary);
              font-weight: $font-weight-medium;
              white-space: nowrap;
              transition: color 0.2s ease;
              overflow: hidden;
              text-overflow: ellipsis; // 超长文字显示省略号
              min-width: 0; // 允许 flex 项目缩小
            }

            .legend-value {
              font-size: $font-size-sm;
              color: var(--theme-text-secondary);
              font-weight: 600;
              flex-shrink: 0; // 防止压缩
              transition: color 0.2s ease;
              min-width: 40px; // 确保百分比有足够空间
              text-align: right;
            }
          }
        }
      }
    }
  }

  // ===== 响应式设计优化 =====
  // 注意：主要的间距响应式设计已在上面各个组件中统一定义

  @media (max-width: $breakpoint-md) {
    // 注意：主要的gap间距已在上面定义，这里只处理其他样式

    .stats-section .ant-card {
      height: 130px; // 移动端固定高度
    }

    .charts-section {
      .main-chart-card,
      .ranking-card {
        height: auto; // 在中等屏幕上使用自适应高度
      }
      
      .main-chart-card {
        .chart-header {
          flex-direction: column;
          align-items: flex-start;
          gap: $spacing-md;

          .chart-controls {
            width: 100%;
            justify-content: space-between;
          }
        }

        .chart-content .chart-placeholder {
          min-height: 280px;
        }
      }
    }

    .bottom-section {
      .online-stores-card,
      .category-card {
        height: auto; // 在中等屏幕上使用自适应高度
      }
      
      .online-stores-card .stores-content .stores-grid {
        // 中等屏幕（768px以下）：每行2个卡片，充分利用空间
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-md; // 统一间距

        .store-card {
          padding: $spacing-md; // 统一内边距

          .store-avatar {
            min-width: 32px;
            height: 32px;
            font-size: 16px;
          }

          .store-details {
            .store-name {
              font-size: 13px;
              line-height: 1.3;
            }

            .store-status .ant-tag {
              font-size: 11px;
              padding: 2px 6px;
              line-height: 1.2;
            }
          }
        }
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .charts-section {
      .main-chart-card .chart-header .chart-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-md;
      }
    }
  }

  // ===== 动画效果 =====
  .stats-grid,
  .charts-grid,
  .bottom-grid {
    // Grid布局过渡动画
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .stats-section .stats-card,
  .main-chart-card,
  .ranking-card,
  .online-stores-card,
  .category-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
  }

  // 统计卡片动画完成后的状态，允许悬停动画
  .stats-section .stats-card {
    animation-fill-mode: none; // 动画完成后不保持最终状态
    opacity: 1; // 手动设置最终透明度
  }

  // 顶部统计卡片同时进入，无延迟
  .stats-section .stats-card {
    animation-delay: 0s; // 所有统计卡片同时开始动画
  }

  .charts-section .main-chart-card {
    animation-delay: 0.4s;
  }

  .charts-section .ranking-card {
    animation-delay: 0.5s;
  }

  .bottom-section .category-card {
    animation-delay: 0.6s;
  }

  .bottom-section .online-stores-card {
    animation-delay: 0.7s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // ===== 主题支持现在通过CSS变量自动处理 =====
}

// ===== 统一间距系统 =====
// 注意：所有间距使用统一的 $spacing-md (16px) 标准
// 简化设计系统，提高一致性，减少维护复杂度