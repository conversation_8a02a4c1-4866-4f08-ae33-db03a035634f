import React from 'react';
import { Card, Row, Col, Typography, Space, Button, Alert } from 'antd';
import {
  CheckCircleOutlined,
  BugOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import SiderLayoutComponent from '../../layouts/SiderLayout';

const { Title, Paragraph, Text } = Typography;

/**
 * SiderLayout 演示页面
 * 展示使用 Ant Design 官方 Layout.Sider 的新布局
 */
const SiderLayoutDemo: React.FC = () => {
  return (
    <SiderLayoutComponent>
      <div style={{ padding: '24px' }}>
        <Title level={2}>
          <ThunderboltOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
          Ant Design 官方 Layout.Sider 演示
        </Title>

        <Alert
          message="新的侧边栏实现"
          description="这个页面展示了使用 Ant Design 官方 Layout.Sider 组件的新侧边栏实现，具有更好的主题兼容性和内置的折叠功能。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={8}>
            <Card
              title={
                <Space>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  <span>官方组件</span>
                </Space>
              }
              bordered={false}
            >
              <Paragraph>
                使用 Ant Design 官方的 <Text code>Layout.Sider</Text> 组件，
                确保与 Ant Design 设计系统的完美兼容。
              </Paragraph>
              <ul>
                <li>内置折叠功能</li>
                <li>响应式断点支持</li>
                <li>官方主题兼容</li>
                <li>无需自定义 hack</li>
              </ul>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={8}>
            <Card
              title={
                <Space>
                  <SafetyOutlined style={{ color: '#722ed1' }} />
                  <span>主题兼容</span>
                </Space>
              }
              bordered={false}
            >
              <Paragraph>
                完美支持明亮和暗黑主题切换，无需额外的样式调整。
              </Paragraph>
              <ul>
                <li>自动主题适配</li>
                <li>CSS 变量支持</li>
                <li>平滑过渡动画</li>
                <li>一致的视觉体验</li>
              </ul>
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={8}>
            <Card
              title={
                <Space>
                  <BugOutlined style={{ color: '#fa541c' }} />
                  <span>问题解决</span>
                </Space>
              }
              bordered={false}
            >
              <Paragraph>
                解决了自定义箭头组件的问题，提供更稳定的用户体验。
              </Paragraph>
              <ul>
                <li>移除自定义箭头</li>
                <li>使用官方触发器</li>
                <li>更好的可访问性</li>
                <li>减少维护成本</li>
              </ul>
            </Card>
          </Col>
        </Row>

        <Card
          title="功能特性"
          style={{ marginTop: '24px' }}
          bordered={false}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Title level={4}>✅ 已实现功能</Title>
              <ul>
                <li>
                  <Text strong>内置折叠触发器</Text>：使用 Ant Design 官方的折叠按钮
                </li>
                <li>
                  <Text strong>响应式布局</Text>：支持移动端自动折叠
                </li>
                <li>
                  <Text strong>主题切换</Text>：完美支持明亮/暗黑主题
                </li>
                <li>
                  <Text strong>菜单导航</Text>：保持原有的菜单功能
                </li>
                <li>
                  <Text strong>Logo 显示</Text>：折叠状态下的 Logo 适配
                </li>
              </ul>
            </Col>
            <Col xs={24} md={12}>
              <Title level={4}>🎯 技术优势</Title>
              <ul>
                <li>
                  <Text strong>官方支持</Text>：使用 Ant Design 官方 API
                </li>
                <li>
                  <Text strong>更少代码</Text>：减少自定义样式和逻辑
                </li>
                <li>
                  <Text strong>更好维护</Text>：跟随 Ant Design 版本更新
                </li>
                <li>
                  <Text strong>无兼容问题</Text>：避免主题切换时的样式冲突
                </li>
                <li>
                  <Text strong>更好性能</Text>：利用 Ant Design 的优化
                </li>
              </ul>
            </Col>
          </Row>
        </Card>

        <Card
          title="使用说明"
          style={{ marginTop: '24px' }}
          bordered={false}
        >
          <Paragraph>
            要在你的项目中使用新的 SiderLayout，只需要：
          </Paragraph>
          <ol>
            <li>
              导入 <Text code>SiderLayoutComponent</Text> 替代原来的 <Text code>BasicLayout</Text>
            </li>
            <li>
              移除 Header 中的自定义折叠按钮（已在 <Text code>HeaderWithoutTrigger</Text> 中实现）
            </li>
            <li>
              享受 Ant Design 官方组件带来的稳定性和兼容性
            </li>
          </ol>

          <Space style={{ marginTop: '16px' }}>
            <Button type="primary">
              开始使用
            </Button>
            <Button>
              查看文档
            </Button>
          </Space>
        </Card>
      </div>
    </SiderLayoutComponent>
  );
};

export default SiderLayoutDemo;
