import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Input, Space, Tag, Modal, Form, message, Switch, Popconfirm } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, ApiOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface Model {
  id: string;
  name: string;
  provider: string;
  type: 'chat' | 'embedding' | 'image';
  status: 'active' | 'inactive';
  apiKey: string;
  endpoint: string;
  maxTokens: number;
  temperature: number;
  createdAt: string;
}

const ModelManagementV2: React.FC = () => {
  const [models, setModels] = useState<Model[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingModel, setEditingModel] = useState<Model | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  const mockModels: Model[] = [
    {
      id: '1',
      name: 'GPT-4',
      provider: 'OpenAI',
      type: 'chat',
      status: 'active',
      apiKey: 'sk-***',
      endpoint: 'https://api.openai.com/v1',
      maxTokens: 4096,
      temperature: 0.7,
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      name: 'Claude-3',
      provider: 'Anthropic',
      type: 'chat',
      status: 'active',
      apiKey: 'sk-***',
      endpoint: 'https://api.anthropic.com/v1',
      maxTokens: 8192,
      temperature: 0.5,
      createdAt: '2024-01-02'
    },
    {
      id: '3',
      name: 'text-embedding-ada-002',
      provider: 'OpenAI',
      type: 'embedding',
      status: 'inactive',
      apiKey: 'sk-***',
      endpoint: 'https://api.openai.com/v1',
      maxTokens: 8191,
      temperature: 0,
      createdAt: '2024-01-03'
    }
  ];

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      setModels(mockModels);
    } catch (error) {
      message.error('加载模型列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingModel(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (model: Model) => {
    setEditingModel(model);
    form.setFieldsValue(model);
    setIsModalVisible(true);
  };

  const handleDelete = async (modelId: string) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      setModels(models.filter(model => model.id !== modelId));
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleStatusChange = async (modelId: string, checked: boolean) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 300));
      setModels(models.map(model => 
        model.id === modelId 
          ? { ...model, status: checked ? 'active' : 'inactive' }
          : model
      ));
      message.success(`模型已${checked ? '启用' : '禁用'}`);
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (editingModel) {
        // 编辑
        setModels(models.map(model => 
          model.id === editingModel.id ? { ...model, ...values } : model
        ));
        message.success('更新成功');
      } else {
        // 新增
        const newModel: Model = {
          id: Date.now().toString(),
          ...values,
          createdAt: new Date().toISOString().split('T')[0]
        };
        setModels([...models, newModel]);
        message.success('添加成功');
      }
      
      setIsModalVisible(false);
    } catch (error) {
      message.error('操作失败');
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'chat': return 'blue';
      case 'embedding': return 'green';
      case 'image': return 'purple';
      default: return 'default';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'chat': return '对话模型';
      case 'embedding': return '嵌入模型';
      case 'image': return '图像模型';
      default: return type;
    }
  };

  const columns: ColumnsType<Model> = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      filteredValue: searchText ? [searchText] : null,
      onFilter: (value, record) =>
        record.name.toLowerCase().includes(value.toString().toLowerCase()) ||
        record.provider.toLowerCase().includes(value.toString().toLowerCase()),
      render: (text) => (
        <Space>
          <ApiOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={getTypeColor(type)}>
          {getTypeText(type)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Switch
          checked={status === 'active'}
          onChange={(checked) => handleStatusChange(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: 'API密钥',
      dataIndex: 'apiKey',
      key: 'apiKey',
      render: (apiKey) => (
        <span style={{ fontFamily: 'monospace' }}>
          {apiKey}
        </span>
      ),
    },
    {
      title: '最大令牌',
      dataIndex: 'maxTokens',
      key: 'maxTokens',
      render: (tokens) => tokens.toLocaleString(),
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      key: 'temperature',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个模型吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="模型管理"
        extra={
          <Space>
            <Input
              placeholder="搜索模型名称或提供商"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加模型
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={models}
          rowKey="id"
          loading={loading}
          pagination={{
            total: models.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title={editingModel ? '编辑模型' : '添加模型'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="provider"
            label="提供商"
            rules={[{ required: true, message: '请输入提供商' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="apiKey"
            label="API密钥"
            rules={[{ required: true, message: '请输入API密钥' }]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            name="endpoint"
            label="API端点"
            rules={[{ required: true, message: '请输入API端点' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="maxTokens"
            label="最大令牌数"
            rules={[{ required: true, message: '请输入最大令牌数' }]}
          >
            <Input type="number" />
          </Form.Item>
          <Form.Item
            name="temperature"
            label="温度"
            rules={[{ required: true, message: '请输入温度值' }]}
          >
            <Input type="number" step="0.1" min="0" max="2" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingModel ? '更新' : '添加'}
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ModelManagementV2;
